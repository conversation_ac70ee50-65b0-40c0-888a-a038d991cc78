import { useState, useCallback } from 'react'
import { LinkedInProfile } from '@/lib/openai'

interface ProcessingState {
  isProcessing: boolean
  progress: number
  status: string
  error: string | null
  result: LinkedInProfile | null
}

export function useLinkedInProcessor() {
  const [state, setState] = useState<ProcessingState>({
    isProcessing: false,
    progress: 0,
    status: '',
    error: null,
    result: null
  })

  const processFile = useCallback(async (file: File): Promise<LinkedInProfile | null> => {
    setState({
      isProcessing: true,
      progress: 0,
      status: 'Preparing image...',
      error: null,
      result: null
    })

    try {
      // Create form data
      const formData = new FormData()
      formData.append('file', file)

      // Update progress
      setState(prev => ({
        ...prev,
        progress: 20,
        status: 'Uploading to AI service...'
      }))

      // Make API call
      const response = await fetch('/api/process-linkedin', {
        method: 'POST',
        body: formData,
      })

      setState(prev => ({
        ...prev,
        progress: 60,
        status: 'AI is analyzing your profile...'
      }))

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to process image')
      }

      const data = await response.json()

      setState(prev => ({
        ...prev,
        progress: 90,
        status: 'Finalizing results...'
      }))

      // Simulate final processing time for better UX
      await new Promise(resolve => setTimeout(resolve, 1000))

      setState(prev => ({
        ...prev,
        progress: 100,
        status: 'Complete!',
        isProcessing: false,
        result: data.data
      }))

      return data.data

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        progress: 0,
        status: '',
        error: errorMessage
      }))

      return null
    }
  }, [])

  const reset = useCallback(() => {
    setState({
      isProcessing: false,
      progress: 0,
      status: '',
      error: null,
      result: null
    })
  }, [])

  return {
    ...state,
    processFile,
    reset
  }
}
