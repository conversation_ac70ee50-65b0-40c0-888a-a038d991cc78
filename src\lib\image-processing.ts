// Image processing utilities for better OCR results

export interface ImageProcessingOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  format?: 'jpeg' | 'png' | 'webp'
}

export async function preprocessImage(
  file: File, 
  options: ImageProcessingOptions = {}
): Promise<string> {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.9,
    format = 'jpeg'
  } = options

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      try {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img
        
        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height
          
          if (width > height) {
            width = Math.min(width, maxWidth)
            height = width / aspectRatio
          } else {
            height = Math.min(height, maxHeight)
            width = height * aspectRatio
          }
        }

        // Set canvas dimensions
        canvas.width = width
        canvas.height = height

        if (!ctx) {
          reject(new Error('Could not get canvas context'))
          return
        }

        // Apply image enhancements for better OCR
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'

        // Draw the image
        ctx.drawImage(img, 0, 0, width, height)

        // Convert to base64
        const dataUrl = canvas.toDataURL(`image/${format}`, quality)
        const base64 = dataUrl.split(',')[1]
        
        console.log(`Image preprocessed: ${img.width}x${img.height} -> ${width}x${height}`)
        resolve(base64)
      } catch (error) {
        reject(error)
      }
    }

    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }

    // Create object URL for the image
    const objectUrl = URL.createObjectURL(file)
    img.src = objectUrl
  })
}

export function validateImageForProcessing(file: File): { valid: boolean; error?: string } {
  // Check file type
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  if (!validTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Please upload a JPEG, PNG, or WebP image.'
    }
  }

  // Check file size (max 15MB for processing)
  const maxSize = 15 * 1024 * 1024
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File too large. Please upload an image smaller than 15MB.'
    }
  }

  // Check minimum dimensions (too small images won't have readable text)
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => {
      if (img.width < 300 || img.height < 300) {
        resolve({
          valid: false,
          error: 'Image too small. Please upload an image at least 300x300 pixels.'
        })
      } else {
        resolve({ valid: true })
      }
      URL.revokeObjectURL(img.src)
    }
    img.onerror = () => {
      resolve({
        valid: false,
        error: 'Could not read image file.'
      })
    }
    img.src = URL.createObjectURL(file)
  }) as any // Type assertion for simplicity
}

export function getImageMetadata(file: File): Promise<{
  width: number
  height: number
  size: number
  type: string
}> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height,
        size: file.size,
        type: file.type
      })
      URL.revokeObjectURL(img.src)
    }
    img.onerror = () => {
      reject(new Error('Could not read image metadata'))
    }
    img.src = URL.createObjectURL(file)
  })
}
