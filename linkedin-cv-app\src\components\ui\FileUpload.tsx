'use client'

import React, { useState, useCallback, useRef } from 'react'
import { Upload, X, Image as ImageIcon, AlertCircle } from 'lucide-react'
import { cn, validateImageFile, formatFileSize } from '@/lib/utils'
import { Button } from './Button'

interface FileUploadProps {
  onFileSelect: (file: File) => void
  onFileRemove: () => void
  selectedFile?: File | null
  className?: string
}

export function FileUpload({ onFileSelect, onFileRemove, selectedFile, className }: FileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [preview, setPreview] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelection(files[0])
    }
  }, [])

  const handleFileSelection = (file: File) => {
    setError(null)
    
    if (!validateImageFile(file)) {
      setError('Please upload a valid image file (JPEG, PNG, WebP) under 10MB')
      return
    }

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)

    onFileSelect(file)
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelection(files[0])
    }
  }

  const handleRemoveFile = () => {
    setPreview(null)
    setError(null)
    onFileRemove()
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={cn('w-full', className)}>
      {!selectedFile ? (
        <div
          className={cn(
            'neo-border neo-shadow-lg p-8 bg-white transition-all duration-200 cursor-pointer',
            isDragOver && 'bg-yellow-100 transform translate-x-1 translate-y-1',
            error && 'border-red-500'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={openFileDialog}
        >
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="neo-border neo-shadow p-6 bg-cyan-400">
                <Upload size={48} className="text-black" />
              </div>
            </div>
            
            <h3 className="text-2xl font-bold uppercase tracking-wide mb-4">
              Upload LinkedIn Screenshot
            </h3>
            
            <p className="text-lg font-medium mb-6">
              Drag & drop your LinkedIn profile screenshot here, or click to browse
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
              <Button size="lg" color="pink">
                Choose File
              </Button>
              <span className="font-bold uppercase text-sm">
                or drag & drop
              </span>
            </div>
            
            <div className="text-sm font-medium text-gray-600">
              <p>Supported formats: JPEG, PNG, WebP</p>
              <p>Maximum file size: 10MB</p>
            </div>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
        </div>
      ) : (
        <div className="neo-border neo-shadow-lg bg-white p-6">
          <div className="flex items-start justify-between mb-4">
            <h3 className="text-xl font-bold uppercase tracking-wide">
              LinkedIn Screenshot Preview
            </h3>
            <Button
              size="sm"
              variant="danger"
              onClick={handleRemoveFile}
              className="flex items-center gap-2"
            >
              <X size={16} />
              Remove
            </Button>
          </div>
          
          {preview && (
            <div className="neo-border neo-shadow mb-4 bg-gray-50 p-4">
              <img
                src={preview}
                alt="LinkedIn screenshot preview"
                className="max-w-full h-auto max-h-96 mx-auto neo-border"
              />
            </div>
          )}
          
          <div className="flex items-center gap-4 p-4 neo-border bg-gray-50">
            <ImageIcon size={24} className="text-gray-600" />
            <div className="flex-1">
              <p className="font-bold text-sm uppercase">
                {selectedFile.name}
              </p>
              <p className="text-sm font-medium text-gray-600">
                {formatFileSize(selectedFile.size)}
              </p>
            </div>
          </div>
        </div>
      )}
      
      {error && (
        <div className="mt-4 neo-border bg-red-100 border-red-500 p-4 flex items-center gap-3">
          <AlertCircle size={20} className="text-red-500" />
          <p className="font-medium text-red-700">
            {error}
          </p>
        </div>
      )}
    </div>
  )
}
