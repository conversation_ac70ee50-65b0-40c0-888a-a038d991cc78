{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/google-vision.ts"], "sourcesContent": ["import { LinkedInProfile } from './openai'\n\n// Google Vision API configuration\nconst GOOGLE_VISION_API_KEY = process.env.GOOGLE_VISION_API_KEY\nconst GOOGLE_SERVICE_ACCOUNT = process.env.GOOGLE_SERVICE_ACCOUNT\nconst GOOGLE_VISION_ENDPOINT = 'https://vision.googleapis.com/v1/images:annotate'\n\ninterface GoogleVisionResponse {\n  responses: Array<{\n    textAnnotations?: Array<{\n      description: string\n      boundingPoly?: {\n        vertices: Array<{ x: number; y: number }>\n      }\n    }>\n    fullTextAnnotation?: {\n      text: string\n    }\n    error?: {\n      code: number\n      message: string\n    }\n  }>\n}\n\nexport async function extractTextWithGoogleVision(imageBase64: string): Promise<string> {\n  if (!GOOGLE_VISION_API_KEY) {\n    throw new Error('Google Vision API key not configured')\n  }\n\n  try {\n    console.log('Starting Google Vision API text extraction...')\n\n    const requestBody = {\n      requests: [\n        {\n          image: {\n            content: imageBase64\n          },\n          features: [\n            {\n              type: 'TEXT_DETECTION',\n              maxResults: 1\n            },\n            {\n              type: 'DOCUMENT_TEXT_DETECTION',\n              maxResults: 1\n            }\n          ]\n        }\n      ]\n    }\n\n    const response = await fetch(`${GOOGLE_VISION_ENDPOINT}?key=${GOOGLE_VISION_API_KEY}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(requestBody)\n    })\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error('Google Vision API error response:', errorText)\n      throw new Error(`Google Vision API error: ${response.status} ${response.statusText} - ${errorText}`)\n    }\n\n    const data: GoogleVisionResponse = await response.json()\n    console.log('Google Vision API response received')\n\n    if (data.responses[0]?.error) {\n      throw new Error(`Google Vision API error: ${data.responses[0].error.message}`)\n    }\n\n    // Get the full text from the response\n    const fullText = data.responses[0]?.fullTextAnnotation?.text || \n                    data.responses[0]?.textAnnotations?.[0]?.description || ''\n\n    console.log('Extracted text length:', fullText.length)\n    console.log('Text preview:', fullText.substring(0, 200) + '...')\n\n    return fullText\n\n  } catch (error) {\n    console.error('Google Vision API error:', error)\n    throw new Error(`Failed to extract text with Google Vision: ${error instanceof Error ? error.message : 'Unknown error'}`)\n  }\n}\n\nexport function parseLinkedInText(extractedText: string): LinkedInProfile {\n  console.log('Parsing LinkedIn text...')\n  \n  // Split text into lines for easier parsing\n  const lines = extractedText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n  \n  // Initialize profile structure\n  const profile: LinkedInProfile = {\n    personalInfo: {\n      name: '',\n      title: '',\n      location: '',\n      email: '',\n      phone: '',\n      linkedin: ''\n    },\n    summary: '',\n    experience: [],\n    education: [],\n    skills: [],\n    certifications: [],\n    languages: []\n  }\n\n  // Parse personal information (usually at the top)\n  if (lines.length > 0) {\n    // First non-empty line is usually the name\n    profile.personalInfo.name = lines[0]\n    \n    // Look for title/headline (usually second line or after name)\n    if (lines.length > 1) {\n      profile.personalInfo.title = lines[1]\n    }\n    \n    // Look for location (often contains city, state, country)\n    for (let i = 0; i < Math.min(5, lines.length); i++) {\n      const line = lines[i].toLowerCase()\n      if (line.includes(',') && (line.includes('ca') || line.includes('ny') || line.includes('usa') || \n          line.includes('united states') || line.includes('san francisco') || line.includes('new york'))) {\n        profile.personalInfo.location = lines[i]\n        break\n      }\n    }\n  }\n\n  // Look for email addresses\n  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/g\n  const emailMatch = extractedText.match(emailRegex)\n  if (emailMatch) {\n    profile.personalInfo.email = emailMatch[0]\n  }\n\n  // Look for phone numbers\n  const phoneRegex = /(\\+?1?[-.\\s]?)?\\(?([0-9]{3})\\)?[-.\\s]?([0-9]{3})[-.\\s]?([0-9]{4})/g\n  const phoneMatch = extractedText.match(phoneRegex)\n  if (phoneMatch) {\n    profile.personalInfo.phone = phoneMatch[0]\n  }\n\n  // Look for LinkedIn URL\n  const linkedinRegex = /linkedin\\.com\\/in\\/[a-zA-Z0-9-]+/g\n  const linkedinMatch = extractedText.match(linkedinRegex)\n  if (linkedinMatch) {\n    profile.personalInfo.linkedin = 'https://' + linkedinMatch[0]\n  }\n\n  // Look for About/Summary section\n  const aboutIndex = extractedText.toLowerCase().indexOf('about')\n  if (aboutIndex !== -1) {\n    const aboutSection = extractedText.substring(aboutIndex)\n    const nextSectionIndex = aboutSection.toLowerCase().search(/(experience|education|skills)/i)\n    if (nextSectionIndex !== -1) {\n      profile.summary = aboutSection.substring(5, nextSectionIndex).trim()\n    } else {\n      profile.summary = aboutSection.substring(5, Math.min(500, aboutSection.length)).trim()\n    }\n  }\n\n  // Parse Experience section\n  const experienceIndex = extractedText.toLowerCase().indexOf('experience')\n  if (experienceIndex !== -1) {\n    const experienceSection = extractedText.substring(experienceIndex)\n    const nextSectionIndex = experienceSection.toLowerCase().search(/(education|skills|certifications)/i)\n    const expText = nextSectionIndex !== -1 ? \n      experienceSection.substring(10, nextSectionIndex) : \n      experienceSection.substring(10, Math.min(1000, experienceSection.length))\n    \n    // Simple parsing - look for company patterns\n    const expLines = expText.split('\\n').filter(line => line.trim().length > 0)\n    let currentExp: any = null\n    \n    for (const line of expLines) {\n      if (line.includes('·') || line.includes('•') || /\\d{4}/.test(line)) {\n        if (currentExp) {\n          profile.experience.push(currentExp)\n        }\n        currentExp = {\n          company: '',\n          position: line.trim(),\n          duration: '',\n          description: '',\n          location: ''\n        }\n      } else if (currentExp && line.trim().length > 0) {\n        if (!currentExp.company) {\n          currentExp.company = line.trim()\n        } else {\n          currentExp.description += line.trim() + ' '\n        }\n      }\n    }\n    if (currentExp) {\n      profile.experience.push(currentExp)\n    }\n  }\n\n  // Parse Education section\n  const educationIndex = extractedText.toLowerCase().indexOf('education')\n  if (educationIndex !== -1) {\n    const educationSection = extractedText.substring(educationIndex)\n    const nextSectionIndex = educationSection.toLowerCase().search(/(skills|certifications|languages)/i)\n    const eduText = nextSectionIndex !== -1 ? \n      educationSection.substring(9, nextSectionIndex) : \n      educationSection.substring(9, Math.min(500, educationSection.length))\n    \n    const eduLines = eduText.split('\\n').filter(line => line.trim().length > 0)\n    if (eduLines.length > 0) {\n      profile.education.push({\n        institution: eduLines[0] || '',\n        degree: eduLines[1] || '',\n        field: eduLines[2] || '',\n        duration: eduLines.find(line => /\\d{4}/.test(line)) || '',\n        location: ''\n      })\n    }\n  }\n\n  // Parse Skills section\n  const skillsIndex = extractedText.toLowerCase().indexOf('skills')\n  if (skillsIndex !== -1) {\n    const skillsSection = extractedText.substring(skillsIndex)\n    const nextSectionIndex = skillsSection.toLowerCase().search(/(certifications|languages|recommendations)/i)\n    const skillsText = nextSectionIndex !== -1 ? \n      skillsSection.substring(6, nextSectionIndex) : \n      skillsSection.substring(6, Math.min(300, skillsSection.length))\n    \n    // Split by common delimiters and clean up\n    const skillsList = skillsText.split(/[,•·\\n]/)\n      .map(skill => skill.trim())\n      .filter(skill => skill.length > 0 && skill.length < 50)\n      .slice(0, 20) // Limit to 20 skills\n    \n    profile.skills = skillsList\n  }\n\n  console.log('Parsing completed:', {\n    name: profile.personalInfo.name,\n    title: profile.personalInfo.title,\n    experienceCount: profile.experience.length,\n    skillsCount: profile.skills.length\n  })\n\n  return profile\n}\n"], "names": [], "mappings": ";;;;AAEA,kCAAkC;AAClC,MAAM,wBAAwB,QAAQ,GAAG,CAAC,qBAAqB;AAC/D,MAAM,yBAAyB,QAAQ,GAAG,CAAC,sBAAsB;AACjE,MAAM,yBAAyB;AAoBxB,eAAe,4BAA4B,WAAmB;IACnE,IAAI,CAAC,uBAAuB;QAC1B,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,cAAc;YAClB,UAAU;gBACR;oBACE,OAAO;wBACL,SAAS;oBACX;oBACA,UAAU;wBACR;4BACE,MAAM;4BACN,YAAY;wBACd;wBACA;4BACE,MAAM;4BACN,YAAY;wBACd;qBACD;gBACH;aACD;QACH;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,uBAAuB,KAAK,EAAE,uBAAuB,EAAE;YACrF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;QACrG;QAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;QACtD,QAAQ,GAAG,CAAC;QAEZ,IAAI,KAAK,SAAS,CAAC,EAAE,EAAE,OAAO;YAC5B,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE;QAC/E;QAEA,sCAAsC;QACtC,MAAM,WAAW,KAAK,SAAS,CAAC,EAAE,EAAE,oBAAoB,QACxC,KAAK,SAAS,CAAC,EAAE,EAAE,iBAAiB,CAAC,EAAE,EAAE,eAAe;QAExE,QAAQ,GAAG,CAAC,0BAA0B,SAAS,MAAM;QACrD,QAAQ,GAAG,CAAC,iBAAiB,SAAS,SAAS,CAAC,GAAG,OAAO;QAE1D,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM,IAAI,MAAM,CAAC,2CAA2C,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC1H;AACF;AAEO,SAAS,kBAAkB,aAAqB;IACrD,QAAQ,GAAG,CAAC;IAEZ,2CAA2C;IAC3C,MAAM,QAAQ,cAAc,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAE9F,+BAA+B;IAC/B,MAAM,UAA2B;QAC/B,cAAc;YACZ,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QACA,SAAS;QACT,YAAY,EAAE;QACd,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,WAAW,EAAE;IACf;IAEA,kDAAkD;IAClD,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,2CAA2C;QAC3C,QAAQ,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;QAEpC,8DAA8D;QAC9D,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,QAAQ,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE;QACvC;QAEA,0DAA0D;QAC1D,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,IAAK;YAClD,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,WAAW;YACjC,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,UACnF,KAAK,QAAQ,CAAC,oBAAoB,KAAK,QAAQ,CAAC,oBAAoB,KAAK,QAAQ,CAAC,WAAW,GAAG;gBAClG,QAAQ,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACxC;YACF;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,aAAa;IACnB,MAAM,aAAa,cAAc,KAAK,CAAC;IACvC,IAAI,YAAY;QACd,QAAQ,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE;IAC5C;IAEA,yBAAyB;IACzB,MAAM,aAAa;IACnB,MAAM,aAAa,cAAc,KAAK,CAAC;IACvC,IAAI,YAAY;QACd,QAAQ,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE;IAC5C;IAEA,wBAAwB;IACxB,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,cAAc,KAAK,CAAC;IAC1C,IAAI,eAAe;QACjB,QAAQ,YAAY,CAAC,QAAQ,GAAG,aAAa,aAAa,CAAC,EAAE;IAC/D;IAEA,iCAAiC;IACjC,MAAM,aAAa,cAAc,WAAW,GAAG,OAAO,CAAC;IACvD,IAAI,eAAe,CAAC,GAAG;QACrB,MAAM,eAAe,cAAc,SAAS,CAAC;QAC7C,MAAM,mBAAmB,aAAa,WAAW,GAAG,MAAM,CAAC;QAC3D,IAAI,qBAAqB,CAAC,GAAG;YAC3B,QAAQ,OAAO,GAAG,aAAa,SAAS,CAAC,GAAG,kBAAkB,IAAI;QACpE,OAAO;YACL,QAAQ,OAAO,GAAG,aAAa,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,aAAa,MAAM,GAAG,IAAI;QACtF;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,cAAc,WAAW,GAAG,OAAO,CAAC;IAC5D,IAAI,oBAAoB,CAAC,GAAG;QAC1B,MAAM,oBAAoB,cAAc,SAAS,CAAC;QAClD,MAAM,mBAAmB,kBAAkB,WAAW,GAAG,MAAM,CAAC;QAChE,MAAM,UAAU,qBAAqB,CAAC,IACpC,kBAAkB,SAAS,CAAC,IAAI,oBAChC,kBAAkB,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,kBAAkB,MAAM;QAEzE,6CAA6C;QAC7C,MAAM,WAAW,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,MAAM,GAAG;QACzE,IAAI,aAAkB;QAEtB,KAAK,MAAM,QAAQ,SAAU;YAC3B,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,QAAQ,IAAI,CAAC,OAAO;gBAClE,IAAI,YAAY;oBACd,QAAQ,UAAU,CAAC,IAAI,CAAC;gBAC1B;gBACA,aAAa;oBACX,SAAS;oBACT,UAAU,KAAK,IAAI;oBACnB,UAAU;oBACV,aAAa;oBACb,UAAU;gBACZ;YACF,OAAO,IAAI,cAAc,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG;gBAC/C,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,WAAW,OAAO,GAAG,KAAK,IAAI;gBAChC,OAAO;oBACL,WAAW,WAAW,IAAI,KAAK,IAAI,KAAK;gBAC1C;YACF;QACF;QACA,IAAI,YAAY;YACd,QAAQ,UAAU,CAAC,IAAI,CAAC;QAC1B;IACF;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,cAAc,WAAW,GAAG,OAAO,CAAC;IAC3D,IAAI,mBAAmB,CAAC,GAAG;QACzB,MAAM,mBAAmB,cAAc,SAAS,CAAC;QACjD,MAAM,mBAAmB,iBAAiB,WAAW,GAAG,MAAM,CAAC;QAC/D,MAAM,UAAU,qBAAqB,CAAC,IACpC,iBAAiB,SAAS,CAAC,GAAG,oBAC9B,iBAAiB,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,iBAAiB,MAAM;QAErE,MAAM,WAAW,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,MAAM,GAAG;QACzE,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,QAAQ,SAAS,CAAC,IAAI,CAAC;gBACrB,aAAa,QAAQ,CAAC,EAAE,IAAI;gBAC5B,QAAQ,QAAQ,CAAC,EAAE,IAAI;gBACvB,OAAO,QAAQ,CAAC,EAAE,IAAI;gBACtB,UAAU,SAAS,IAAI,CAAC,CAAA,OAAQ,QAAQ,IAAI,CAAC,UAAU;gBACvD,UAAU;YACZ;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAc,cAAc,WAAW,GAAG,OAAO,CAAC;IACxD,IAAI,gBAAgB,CAAC,GAAG;QACtB,MAAM,gBAAgB,cAAc,SAAS,CAAC;QAC9C,MAAM,mBAAmB,cAAc,WAAW,GAAG,MAAM,CAAC;QAC5D,MAAM,aAAa,qBAAqB,CAAC,IACvC,cAAc,SAAS,CAAC,GAAG,oBAC3B,cAAc,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,cAAc,MAAM;QAE/D,0CAA0C;QAC1C,MAAM,aAAa,WAAW,KAAK,CAAC,WACjC,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,IACnD,KAAK,CAAC,GAAG,IAAI,qBAAqB;;QAErC,QAAQ,MAAM,GAAG;IACnB;IAEA,QAAQ,GAAG,CAAC,sBAAsB;QAChC,MAAM,QAAQ,YAAY,CAAC,IAAI;QAC/B,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,iBAAiB,QAAQ,UAAU,CAAC,MAAM;QAC1C,aAAa,QAAQ,MAAM,CAAC,MAAM;IACpC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/api/test-google-vision/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { extractTextWithGoogleVision } from '@/lib/google-vision'\n\nexport async function GET() {\n  try {\n    if (!process.env.GOOGLE_VISION_API_KEY) {\n      return NextResponse.json({\n        error: 'Google Vision API key not configured',\n        instructions: 'Add GOOGLE_VISION_API_KEY to your .env.local file'\n      })\n    }\n\n    // Test with a simple image (1x1 pixel PNG with some text)\n    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='\n\n    console.log('Testing Google Vision API...')\n\n    const extractedText = await extractTextWithGoogleVision(testImageBase64)\n\n    return NextResponse.json({\n      success: true,\n      message: 'Google Vision API is working',\n      extractedText: extractedText || 'No text found in test image (this is normal)',\n      apiKeyConfigured: true\n    })\n\n  } catch (error) {\n    console.error('Google Vision API test failed:', error)\n    \n    return NextResponse.json({\n      error: 'Google Vision API test failed',\n      details: error instanceof Error ? error.message : 'Unknown error',\n      instructions: [\n        '1. Get API key from Google Cloud Console',\n        '2. Enable Vision API in your Google Cloud project',\n        '3. Add GOOGLE_VISION_API_KEY to .env.local',\n        '4. Make sure billing is enabled for your Google Cloud project'\n      ]\n    }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    if (!process.env.GOOGLE_VISION_API_KEY) {\n      return NextResponse.json({\n        error: 'Google Vision API key not configured'\n      })\n    }\n\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n\n    if (!file) {\n      return NextResponse.json({\n        error: 'No file provided'\n      }, { status: 400 })\n    }\n\n    // Convert file to base64\n    const arrayBuffer = await file.arrayBuffer()\n    const base64 = Buffer.from(arrayBuffer).toString('base64')\n\n    console.log('Testing Google Vision with uploaded file...')\n    console.log('File type:', file.type)\n    console.log('File size:', file.size)\n\n    const extractedText = await extractTextWithGoogleVision(base64)\n\n    return NextResponse.json({\n      success: true,\n      message: 'Google Vision text extraction successful',\n      extractedText: extractedText,\n      textLength: extractedText.length,\n      preview: extractedText.substring(0, 500) + (extractedText.length > 500 ? '...' : '')\n    })\n\n  } catch (error) {\n    console.error('Google Vision file test failed:', error)\n    \n    return NextResponse.json({\n      error: 'Google Vision file test failed',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,qBAAqB,EAAE;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;gBACP,cAAc;YAChB;QACF;QAEA,0DAA0D;QAC1D,MAAM,kBAAkB;QAExB,QAAQ,GAAG,CAAC;QAEZ,MAAM,gBAAgB,MAAM,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD,EAAE;QAExD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,eAAe,iBAAiB;YAChC,kBAAkB;QACpB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;QACH,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,qBAAqB,EAAE;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT;QACF;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;QAEjD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;QACnC,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;QAEnC,MAAM,gBAAgB,MAAM,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD,EAAE;QAExD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,eAAe;YACf,YAAY,cAAc,MAAM;YAChC,SAAS,cAAc,SAAS,CAAC,GAAG,OAAO,CAAC,cAAc,MAAM,GAAG,MAAM,QAAQ,EAAE;QACrF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}