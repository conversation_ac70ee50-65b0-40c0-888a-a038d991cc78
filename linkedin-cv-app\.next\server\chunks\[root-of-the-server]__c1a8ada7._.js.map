{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai'\n\n// Initialize AIML API client (using OpenAI SDK with custom base URL)\nconst aimlClient = new OpenAI({\n  apiKey: process.env.AIML_API_KEY,\n  baseURL: 'https://api.aimlapi.com',\n})\n\n// AIML API provides access to GPT-4o and other advanced vision models\n\nexport interface LinkedInProfile {\n  personalInfo: {\n    name: string\n    title: string\n    location: string\n    email?: string\n    phone?: string\n    linkedin?: string\n  }\n  summary: string\n  experience: Array<{\n    company: string\n    position: string\n    duration: string\n    description: string\n    location?: string\n  }>\n  education: Array<{\n    institution: string\n    degree: string\n    field: string\n    duration: string\n    location?: string\n  }>\n  skills: string[]\n  certifications?: Array<{\n    name: string\n    issuer: string\n    date?: string\n  }>\n  languages?: Array<{\n    language: string\n    proficiency: string\n  }>\n}\n\nexport async function processLinkedInScreenshot(imageBase64: string): Promise<LinkedInProfile> {\n  const maxRetries = 2\n  let lastError: Error | null = null\n\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      console.log(`Starting AIML API request (attempt ${attempt}/${maxRetries})...`)\n\n      // Process the image directly\n\n      const response = await aimlClient.chat.completions.create({\n      model: \"gpt-4o\",\n      messages: [\n        {\n          role: \"user\",\n          content: [\n            {\n              type: \"text\",\n              text: `What text do you see in this image? Describe what you can read.`\n            },\n            {\n              type: \"image_url\",\n              image_url: {\n                url: `data:image/jpeg;base64,${imageBase64}`,\n                detail: \"low\"\n              }\n            }\n          ]\n        }\n      ],\n      max_tokens: 500,\n      temperature: 0.1\n    })\n\n    console.log('AIML API response received')\n    console.log('Response object:', JSON.stringify(response, null, 2))\n\n    const content = response.choices[0]?.message?.content\n    console.log('Response content length:', content?.length || 0)\n    console.log('Response content preview:', content?.substring(0, 200))\n    if (!content) {\n      throw new Error('No response from AIML API')\n    }\n\n    console.log('Raw AI response:', content.substring(0, 500) + '...')\n\n    // Try to parse the JSON response\n    try {\n      const rawProfileData = JSON.parse(content) as LinkedInProfile\n      console.log('Successfully parsed JSON response')\n\n      // Clean and enhance the extracted data\n      const cleanedProfileData = cleanProfileData(rawProfileData)\n      console.log('Data cleaning completed')\n\n      return cleanedProfileData\n    } catch (parseError) {\n      console.log('Initial JSON parse failed, trying to extract JSON...')\n\n      // If JSON parsing fails, try to extract JSON from the response\n      // Look for JSON between ```json and ``` or just between { and }\n      let jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/)\n      if (!jsonMatch) {\n        jsonMatch = content.match(/```\\s*([\\s\\S]*?)\\s*```/)\n      }\n      if (!jsonMatch) {\n        jsonMatch = content.match(/(\\{[\\s\\S]*\\})/)\n      }\n\n      if (jsonMatch) {\n        try {\n          const cleanedJson = jsonMatch[1] || jsonMatch[0]\n          console.log('Extracted JSON:', cleanedJson.substring(0, 200) + '...')\n          const rawProfileData = JSON.parse(cleanedJson) as LinkedInProfile\n          console.log('Successfully parsed extracted JSON')\n\n          // Clean and enhance the extracted data\n          const cleanedProfileData = cleanProfileData(rawProfileData)\n          console.log('Data cleaning completed')\n\n          return cleanedProfileData\n        } catch (secondParseError) {\n          console.error('Failed to parse extracted JSON:', secondParseError)\n        }\n      }\n\n      console.error('Raw response that failed to parse:', content)\n      throw new Error('Failed to parse AI response as JSON')\n    }\n\n    } catch (error) {\n      console.error(`Attempt ${attempt} failed:`, error)\n      lastError = error instanceof Error ? error : new Error('Unknown error')\n\n      if (attempt < maxRetries) {\n        console.log(`Retrying in 2 seconds...`)\n        await new Promise(resolve => setTimeout(resolve, 2000))\n        continue\n      }\n    }\n  }\n\n  // If all attempts failed, throw the last error\n  if (lastError) {\n    console.error('All attempts failed. Error processing LinkedIn screenshot with AIML API:', lastError)\n\n    // Provide more detailed error information\n    if (lastError instanceof Error) {\n      console.error('Error message:', lastError.message)\n      console.error('Error stack:', lastError.stack)\n    }\n\n    // Check if it's an API-related error\n    if (lastError && typeof lastError === 'object' && 'response' in lastError) {\n      console.error('API response error:', lastError)\n    }\n\n    throw new Error(`Failed to process LinkedIn screenshot with AI after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : 'Unknown error'}`)\n  }\n\n  // This should never be reached, but TypeScript requires it\n  throw new Error('Unexpected error in processLinkedInScreenshot')\n}\n\n// Helper function to convert file to base64\nexport function fileToBase64(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader()\n    reader.readAsDataURL(file)\n    reader.onload = () => {\n      const result = reader.result as string\n      // Remove the data URL prefix to get just the base64 string\n      const base64 = result.split(',')[1]\n      resolve(base64)\n    }\n    reader.onerror = error => reject(error)\n  })\n}\n\n// Validate extracted profile data\nexport function validateProfileData(profile: LinkedInProfile): boolean {\n  return !!(\n    profile.personalInfo?.name &&\n    profile.personalInfo?.title &&\n    (profile.experience?.length > 0 || profile.education?.length > 0)\n  )\n}\n\n// Clean and enhance extracted profile data\nexport function cleanProfileData(profile: LinkedInProfile): LinkedInProfile {\n  return {\n    personalInfo: {\n      name: cleanText(profile.personalInfo?.name || ''),\n      title: cleanText(profile.personalInfo?.title || ''),\n      location: cleanText(profile.personalInfo?.location || ''),\n      email: cleanEmail(profile.personalInfo?.email || ''),\n      phone: cleanPhone(profile.personalInfo?.phone || ''),\n      linkedin: cleanUrl(profile.personalInfo?.linkedin || '')\n    },\n    summary: cleanText(profile.summary || ''),\n    experience: (profile.experience || []).map(exp => ({\n      company: cleanText(exp.company || ''),\n      position: cleanText(exp.position || ''),\n      duration: cleanDuration(exp.duration || ''),\n      description: cleanText(exp.description || ''),\n      location: cleanText(exp.location || '')\n    })),\n    education: (profile.education || []).map(edu => ({\n      institution: cleanText(edu.institution || ''),\n      degree: cleanText(edu.degree || ''),\n      field: cleanText(edu.field || ''),\n      duration: cleanDuration(edu.duration || ''),\n      location: cleanText(edu.location || '')\n    })),\n    skills: (profile.skills || []).map(skill => cleanText(skill)).filter(skill => skill.length > 0),\n    certifications: (profile.certifications || []).map(cert => ({\n      name: cleanText(cert.name || ''),\n      issuer: cleanText(cert.issuer || ''),\n      date: cleanText(cert.date || '')\n    })),\n    languages: (profile.languages || []).map(lang => ({\n      language: cleanText(lang.language || ''),\n      proficiency: cleanText(lang.proficiency || '')\n    }))\n  }\n}\n\n// Helper functions for data cleaning\nfunction cleanText(text: string): string {\n  return text.trim().replace(/\\s+/g, ' ').replace(/[\"\"]/g, '\"').replace(/['']/g, \"'\")\n}\n\nfunction cleanEmail(email: string): string {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email.trim()) ? email.trim().toLowerCase() : ''\n}\n\nfunction cleanPhone(phone: string): string {\n  const cleaned = phone.replace(/[^\\d+\\-\\(\\)\\s]/g, '').trim()\n  return cleaned.length >= 10 ? cleaned : ''\n}\n\nfunction cleanUrl(url: string): string {\n  if (!url) return ''\n  if (url.startsWith('http')) return url\n  if (url.includes('linkedin.com')) return `https://${url}`\n  return url\n}\n\nfunction cleanDuration(duration: string): string {\n  return duration.trim().replace(/\\s+/g, ' ')\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;;AAEA,qEAAqE;AACrE,MAAM,aAAa,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC5B,QAAQ,QAAQ,GAAG,CAAC,YAAY;IAChC,SAAS;AACX;AAwCO,eAAe,0BAA0B,WAAmB;IACjE,MAAM,aAAa;IACnB,IAAI,YAA0B;IAE9B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,CAAC,EAAE,WAAW,IAAI,CAAC;YAE7E,6BAA6B;YAE7B,MAAM,WAAW,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC1D,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM,CAAC,+DAA+D,CAAC;4BACzE;4BACA;gCACE,MAAM;gCACN,WAAW;oCACT,KAAK,CAAC,uBAAuB,EAAE,aAAa;oCAC5C,QAAQ;gCACV;4BACF;yBACD;oBACH;iBACD;gBACD,YAAY;gBACZ,aAAa;YACf;YAEA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,oBAAoB,KAAK,SAAS,CAAC,UAAU,MAAM;YAE/D,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,QAAQ,GAAG,CAAC,4BAA4B,SAAS,UAAU;YAC3D,QAAQ,GAAG,CAAC,6BAA6B,SAAS,UAAU,GAAG;YAC/D,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,SAAS,CAAC,GAAG,OAAO;YAE5D,iCAAiC;YACjC,IAAI;gBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAClC,QAAQ,GAAG,CAAC;gBAEZ,uCAAuC;gBACvC,MAAM,qBAAqB,iBAAiB;gBAC5C,QAAQ,GAAG,CAAC;gBAEZ,OAAO;YACT,EAAE,OAAO,YAAY;gBACnB,QAAQ,GAAG,CAAC;gBAEZ,+DAA+D;gBAC/D,gEAAgE;gBAChE,IAAI,YAAY,QAAQ,KAAK,CAAC;gBAC9B,IAAI,CAAC,WAAW;oBACd,YAAY,QAAQ,KAAK,CAAC;gBAC5B;gBACA,IAAI,CAAC,WAAW;oBACd,YAAY,QAAQ,KAAK,CAAC;gBAC5B;gBAEA,IAAI,WAAW;oBACb,IAAI;wBACF,MAAM,cAAc,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;wBAChD,QAAQ,GAAG,CAAC,mBAAmB,YAAY,SAAS,CAAC,GAAG,OAAO;wBAC/D,MAAM,iBAAiB,KAAK,KAAK,CAAC;wBAClC,QAAQ,GAAG,CAAC;wBAEZ,uCAAuC;wBACvC,MAAM,qBAAqB,iBAAiB;wBAC5C,QAAQ,GAAG,CAAC;wBAEZ,OAAO;oBACT,EAAE,OAAO,kBAAkB;wBACzB,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF;gBAEA,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,MAAM,IAAI,MAAM;YAClB;QAEA,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAAE;YAC5C,YAAY,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;YAEvD,IAAI,UAAU,YAAY;gBACxB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,CAAC;gBACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD;YACF;QACF;IACF;IAEA,+CAA+C;IAC/C,IAAI,WAAW;QACb,QAAQ,KAAK,CAAC,4EAA4E;QAE1F,0CAA0C;QAC1C,IAAI,qBAAqB,OAAO;YAC9B,QAAQ,KAAK,CAAC,kBAAkB,UAAU,OAAO;YACjD,QAAQ,KAAK,CAAC,gBAAgB,UAAU,KAAK;QAC/C;QAEA,qCAAqC;QACrC,IAAI,aAAa,OAAO,cAAc,YAAY,cAAc,WAAW;YACzE,QAAQ,KAAK,CAAC,uBAAuB;QACvC;QAEA,MAAM,IAAI,MAAM,CAAC,oDAAoD,EAAE,WAAW,WAAW,EAAE,qBAAqB,QAAQ,UAAU,OAAO,GAAG,iBAAiB;IACnK;IAEA,2DAA2D;IAC3D,MAAM,IAAI,MAAM;AAClB;AAGO,SAAS,aAAa,IAAU;IACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,aAAa,CAAC;QACrB,OAAO,MAAM,GAAG;YACd,MAAM,SAAS,OAAO,MAAM;YAC5B,2DAA2D;YAC3D,MAAM,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;YACnC,QAAQ;QACV;QACA,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;IACnC;AACF;AAGO,SAAS,oBAAoB,OAAwB;IAC1D,OAAO,CAAC,CAAC,CACP,QAAQ,YAAY,EAAE,QACtB,QAAQ,YAAY,EAAE,SACtB,CAAC,QAAQ,UAAU,EAAE,SAAS,KAAK,QAAQ,SAAS,EAAE,SAAS,CAAC,CAClE;AACF;AAGO,SAAS,iBAAiB,OAAwB;IACvD,OAAO;QACL,cAAc;YACZ,MAAM,UAAU,QAAQ,YAAY,EAAE,QAAQ;YAC9C,OAAO,UAAU,QAAQ,YAAY,EAAE,SAAS;YAChD,UAAU,UAAU,QAAQ,YAAY,EAAE,YAAY;YACtD,OAAO,WAAW,QAAQ,YAAY,EAAE,SAAS;YACjD,OAAO,WAAW,QAAQ,YAAY,EAAE,SAAS;YACjD,UAAU,SAAS,QAAQ,YAAY,EAAE,YAAY;QACvD;QACA,SAAS,UAAU,QAAQ,OAAO,IAAI;QACtC,YAAY,CAAC,QAAQ,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;gBACjD,SAAS,UAAU,IAAI,OAAO,IAAI;gBAClC,UAAU,UAAU,IAAI,QAAQ,IAAI;gBACpC,UAAU,cAAc,IAAI,QAAQ,IAAI;gBACxC,aAAa,UAAU,IAAI,WAAW,IAAI;gBAC1C,UAAU,UAAU,IAAI,QAAQ,IAAI;YACtC,CAAC;QACD,WAAW,CAAC,QAAQ,SAAS,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC/C,aAAa,UAAU,IAAI,WAAW,IAAI;gBAC1C,QAAQ,UAAU,IAAI,MAAM,IAAI;gBAChC,OAAO,UAAU,IAAI,KAAK,IAAI;gBAC9B,UAAU,cAAc,IAAI,QAAQ,IAAI;gBACxC,UAAU,UAAU,IAAI,QAAQ,IAAI;YACtC,CAAC;QACD,QAAQ,CAAC,QAAQ,MAAM,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,QAAS,UAAU,QAAQ,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,GAAG;QAC7F,gBAAgB,CAAC,QAAQ,cAAc,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC1D,MAAM,UAAU,KAAK,IAAI,IAAI;gBAC7B,QAAQ,UAAU,KAAK,MAAM,IAAI;gBACjC,MAAM,UAAU,KAAK,IAAI,IAAI;YAC/B,CAAC;QACD,WAAW,CAAC,QAAQ,SAAS,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAChD,UAAU,UAAU,KAAK,QAAQ,IAAI;gBACrC,aAAa,UAAU,KAAK,WAAW,IAAI;YAC7C,CAAC;IACH;AACF;AAEA,qCAAqC;AACrC,SAAS,UAAU,IAAY;IAC7B,OAAO,KAAK,IAAI,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS;AACjF;AAEA,SAAS,WAAW,KAAa;IAC/B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM,IAAI,GAAG,WAAW,KAAK;AACtE;AAEA,SAAS,WAAW,KAAa;IAC/B,MAAM,UAAU,MAAM,OAAO,CAAC,mBAAmB,IAAI,IAAI;IACzD,OAAO,QAAQ,MAAM,IAAI,KAAK,UAAU;AAC1C;AAEA,SAAS,SAAS,GAAW;IAC3B,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI,IAAI,UAAU,CAAC,SAAS,OAAO;IACnC,IAAI,IAAI,QAAQ,CAAC,iBAAiB,OAAO,CAAC,QAAQ,EAAE,KAAK;IACzD,OAAO;AACT;AAEA,SAAS,cAAc,QAAgB;IACrC,OAAO,SAAS,IAAI,GAAG,OAAO,CAAC,QAAQ;AACzC", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/google-vision.ts"], "sourcesContent": ["import { LinkedInProfile } from './openai'\nimport { GoogleAuth } from 'google-auth-library'\nimport path from 'path'\n\n// Google Vision API configuration\nconst GOOGLE_SERVICE_ACCOUNT = process.env.GOOGLE_SERVICE_ACCOUNT\nconst GOOGLE_VISION_ENDPOINT = 'https://vision.googleapis.com/v1/images:annotate'\nconst CREDENTIALS_PATH = path.join(process.cwd(), 'google-credentials.json')\n\ninterface GoogleVisionResponse {\n  responses: Array<{\n    textAnnotations?: Array<{\n      description: string\n      boundingPoly?: {\n        vertices: Array<{ x: number; y: number }>\n      }\n    }>\n    fullTextAnnotation?: {\n      text: string\n    }\n    error?: {\n      code: number\n      message: string\n    }\n  }>\n}\n\nexport async function extractTextWithGoogleVision(imageBase64: string): Promise<string> {\n  try {\n    console.log('Starting Google Vision API text extraction with service account...')\n\n    // Create Google Auth client using credentials file\n    const auth = new GoogleAuth({\n      keyFile: CREDENTIALS_PATH,\n      scopes: ['https://www.googleapis.com/auth/cloud-platform']\n    })\n\n    // Get access token\n    const authClient = await auth.getClient()\n    const accessToken = await authClient.getAccessToken()\n\n    if (!accessToken.token) {\n      throw new Error('Failed to get access token')\n    }\n\n    console.log('Successfully obtained access token')\n\n    const requestBody = {\n      requests: [\n        {\n          image: {\n            content: imageBase64\n          },\n          features: [\n            {\n              type: 'TEXT_DETECTION',\n              maxResults: 1\n            },\n            {\n              type: 'DOCUMENT_TEXT_DETECTION',\n              maxResults: 1\n            }\n          ]\n        }\n      ]\n    }\n\n    const response = await fetch(GOOGLE_VISION_ENDPOINT, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${accessToken.token}`\n      },\n      body: JSON.stringify(requestBody)\n    })\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error('Google Vision API error response:', errorText)\n      throw new Error(`Google Vision API error: ${response.status} ${response.statusText} - ${errorText}`)\n    }\n\n    const data: GoogleVisionResponse = await response.json()\n    console.log('Google Vision API response received')\n\n    if (data.responses[0]?.error) {\n      throw new Error(`Google Vision API error: ${data.responses[0].error.message}`)\n    }\n\n    // Get the full text from the response\n    const fullText = data.responses[0]?.fullTextAnnotation?.text || \n                    data.responses[0]?.textAnnotations?.[0]?.description || ''\n\n    console.log('Extracted text length:', fullText.length)\n    console.log('Text preview:', fullText.substring(0, 200) + '...')\n\n    return fullText\n\n  } catch (error) {\n    console.error('Google Vision API error:', error)\n    throw new Error(`Failed to extract text with Google Vision: ${error instanceof Error ? error.message : 'Unknown error'}`)\n  }\n}\n\nexport function parseLinkedInText(extractedText: string): LinkedInProfile {\n  console.log('Parsing LinkedIn text...')\n  \n  // Split text into lines for easier parsing\n  const lines = extractedText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n  \n  // Initialize profile structure\n  const profile: LinkedInProfile = {\n    personalInfo: {\n      name: '',\n      title: '',\n      location: '',\n      email: '',\n      phone: '',\n      linkedin: ''\n    },\n    summary: '',\n    experience: [],\n    education: [],\n    skills: [],\n    certifications: [],\n    languages: []\n  }\n\n  // Parse personal information (usually at the top)\n  if (lines.length > 0) {\n    // First non-empty line is usually the name\n    profile.personalInfo.name = lines[0]\n    \n    // Look for title/headline (usually second line or after name)\n    if (lines.length > 1) {\n      profile.personalInfo.title = lines[1]\n    }\n    \n    // Look for location (often contains city, state, country)\n    for (let i = 0; i < Math.min(5, lines.length); i++) {\n      const line = lines[i].toLowerCase()\n      if (line.includes(',') && (line.includes('ca') || line.includes('ny') || line.includes('usa') || \n          line.includes('united states') || line.includes('san francisco') || line.includes('new york'))) {\n        profile.personalInfo.location = lines[i]\n        break\n      }\n    }\n  }\n\n  // Look for email addresses\n  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/g\n  const emailMatch = extractedText.match(emailRegex)\n  if (emailMatch) {\n    profile.personalInfo.email = emailMatch[0]\n  }\n\n  // Look for phone numbers\n  const phoneRegex = /(\\+?1?[-.\\s]?)?\\(?([0-9]{3})\\)?[-.\\s]?([0-9]{3})[-.\\s]?([0-9]{4})/g\n  const phoneMatch = extractedText.match(phoneRegex)\n  if (phoneMatch) {\n    profile.personalInfo.phone = phoneMatch[0]\n  }\n\n  // Look for LinkedIn URL\n  const linkedinRegex = /linkedin\\.com\\/in\\/[a-zA-Z0-9-]+/g\n  const linkedinMatch = extractedText.match(linkedinRegex)\n  if (linkedinMatch) {\n    profile.personalInfo.linkedin = 'https://' + linkedinMatch[0]\n  }\n\n  // Look for About/Summary section\n  const aboutIndex = extractedText.toLowerCase().indexOf('about')\n  if (aboutIndex !== -1) {\n    const aboutSection = extractedText.substring(aboutIndex)\n    const nextSectionIndex = aboutSection.toLowerCase().search(/(experience|education|skills)/i)\n    if (nextSectionIndex !== -1) {\n      profile.summary = aboutSection.substring(5, nextSectionIndex).trim()\n    } else {\n      profile.summary = aboutSection.substring(5, Math.min(500, aboutSection.length)).trim()\n    }\n  }\n\n  // Parse Experience section\n  const experienceIndex = extractedText.toLowerCase().indexOf('experience')\n  if (experienceIndex !== -1) {\n    const experienceSection = extractedText.substring(experienceIndex)\n    const nextSectionIndex = experienceSection.toLowerCase().search(/(education|skills|certifications)/i)\n    const expText = nextSectionIndex !== -1 ? \n      experienceSection.substring(10, nextSectionIndex) : \n      experienceSection.substring(10, Math.min(1000, experienceSection.length))\n    \n    // Simple parsing - look for company patterns\n    const expLines = expText.split('\\n').filter(line => line.trim().length > 0)\n    let currentExp: any = null\n    \n    for (const line of expLines) {\n      if (line.includes('·') || line.includes('•') || /\\d{4}/.test(line)) {\n        if (currentExp) {\n          profile.experience.push(currentExp)\n        }\n        currentExp = {\n          company: '',\n          position: line.trim(),\n          duration: '',\n          description: '',\n          location: ''\n        }\n      } else if (currentExp && line.trim().length > 0) {\n        if (!currentExp.company) {\n          currentExp.company = line.trim()\n        } else {\n          currentExp.description += line.trim() + ' '\n        }\n      }\n    }\n    if (currentExp) {\n      profile.experience.push(currentExp)\n    }\n  }\n\n  // Parse Education section\n  const educationIndex = extractedText.toLowerCase().indexOf('education')\n  if (educationIndex !== -1) {\n    const educationSection = extractedText.substring(educationIndex)\n    const nextSectionIndex = educationSection.toLowerCase().search(/(skills|certifications|languages)/i)\n    const eduText = nextSectionIndex !== -1 ? \n      educationSection.substring(9, nextSectionIndex) : \n      educationSection.substring(9, Math.min(500, educationSection.length))\n    \n    const eduLines = eduText.split('\\n').filter(line => line.trim().length > 0)\n    if (eduLines.length > 0) {\n      profile.education.push({\n        institution: eduLines[0] || '',\n        degree: eduLines[1] || '',\n        field: eduLines[2] || '',\n        duration: eduLines.find(line => /\\d{4}/.test(line)) || '',\n        location: ''\n      })\n    }\n  }\n\n  // Parse Skills section\n  const skillsIndex = extractedText.toLowerCase().indexOf('skills')\n  if (skillsIndex !== -1) {\n    const skillsSection = extractedText.substring(skillsIndex)\n    const nextSectionIndex = skillsSection.toLowerCase().search(/(certifications|languages|recommendations)/i)\n    const skillsText = nextSectionIndex !== -1 ? \n      skillsSection.substring(6, nextSectionIndex) : \n      skillsSection.substring(6, Math.min(300, skillsSection.length))\n    \n    // Split by common delimiters and clean up\n    const skillsList = skillsText.split(/[,•·\\n]/)\n      .map(skill => skill.trim())\n      .filter(skill => skill.length > 0 && skill.length < 50)\n      .slice(0, 20) // Limit to 20 skills\n    \n    profile.skills = skillsList\n  }\n\n  console.log('Parsing completed:', {\n    name: profile.personalInfo.name,\n    title: profile.personalInfo.title,\n    experienceCount: profile.experience.length,\n    skillsCount: profile.skills.length\n  })\n\n  return profile\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAEA,kCAAkC;AAClC,MAAM,yBAAyB,QAAQ,GAAG,CAAC,sBAAsB;AACjE,MAAM,yBAAyB;AAC/B,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAoB3C,eAAe,4BAA4B,WAAmB;IACnE,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,mDAAmD;QACnD,MAAM,OAAO,IAAI,oKAAA,CAAA,aAAU,CAAC;YAC1B,SAAS;YACT,QAAQ;gBAAC;aAAiD;QAC5D;QAEA,mBAAmB;QACnB,MAAM,aAAa,MAAM,KAAK,SAAS;QACvC,MAAM,cAAc,MAAM,WAAW,cAAc;QAEnD,IAAI,CAAC,YAAY,KAAK,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC;QAEZ,MAAM,cAAc;YAClB,UAAU;gBACR;oBACE,OAAO;wBACL,SAAS;oBACX;oBACA,UAAU;wBACR;4BACE,MAAM;4BACN,YAAY;wBACd;wBACA;4BACE,MAAM;4BACN,YAAY;wBACd;qBACD;gBACH;aACD;QACH;QAEA,MAAM,WAAW,MAAM,MAAM,wBAAwB;YACnD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,YAAY,KAAK,EAAE;YAChD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;QACrG;QAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;QACtD,QAAQ,GAAG,CAAC;QAEZ,IAAI,KAAK,SAAS,CAAC,EAAE,EAAE,OAAO;YAC5B,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE;QAC/E;QAEA,sCAAsC;QACtC,MAAM,WAAW,KAAK,SAAS,CAAC,EAAE,EAAE,oBAAoB,QACxC,KAAK,SAAS,CAAC,EAAE,EAAE,iBAAiB,CAAC,EAAE,EAAE,eAAe;QAExE,QAAQ,GAAG,CAAC,0BAA0B,SAAS,MAAM;QACrD,QAAQ,GAAG,CAAC,iBAAiB,SAAS,SAAS,CAAC,GAAG,OAAO;QAE1D,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM,IAAI,MAAM,CAAC,2CAA2C,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC1H;AACF;AAEO,SAAS,kBAAkB,aAAqB;IACrD,QAAQ,GAAG,CAAC;IAEZ,2CAA2C;IAC3C,MAAM,QAAQ,cAAc,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAE9F,+BAA+B;IAC/B,MAAM,UAA2B;QAC/B,cAAc;YACZ,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QACA,SAAS;QACT,YAAY,EAAE;QACd,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,WAAW,EAAE;IACf;IAEA,kDAAkD;IAClD,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,2CAA2C;QAC3C,QAAQ,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;QAEpC,8DAA8D;QAC9D,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,QAAQ,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE;QACvC;QAEA,0DAA0D;QAC1D,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,IAAK;YAClD,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,WAAW;YACjC,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,UACnF,KAAK,QAAQ,CAAC,oBAAoB,KAAK,QAAQ,CAAC,oBAAoB,KAAK,QAAQ,CAAC,WAAW,GAAG;gBAClG,QAAQ,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACxC;YACF;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,aAAa;IACnB,MAAM,aAAa,cAAc,KAAK,CAAC;IACvC,IAAI,YAAY;QACd,QAAQ,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE;IAC5C;IAEA,yBAAyB;IACzB,MAAM,aAAa;IACnB,MAAM,aAAa,cAAc,KAAK,CAAC;IACvC,IAAI,YAAY;QACd,QAAQ,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE;IAC5C;IAEA,wBAAwB;IACxB,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,cAAc,KAAK,CAAC;IAC1C,IAAI,eAAe;QACjB,QAAQ,YAAY,CAAC,QAAQ,GAAG,aAAa,aAAa,CAAC,EAAE;IAC/D;IAEA,iCAAiC;IACjC,MAAM,aAAa,cAAc,WAAW,GAAG,OAAO,CAAC;IACvD,IAAI,eAAe,CAAC,GAAG;QACrB,MAAM,eAAe,cAAc,SAAS,CAAC;QAC7C,MAAM,mBAAmB,aAAa,WAAW,GAAG,MAAM,CAAC;QAC3D,IAAI,qBAAqB,CAAC,GAAG;YAC3B,QAAQ,OAAO,GAAG,aAAa,SAAS,CAAC,GAAG,kBAAkB,IAAI;QACpE,OAAO;YACL,QAAQ,OAAO,GAAG,aAAa,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,aAAa,MAAM,GAAG,IAAI;QACtF;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,cAAc,WAAW,GAAG,OAAO,CAAC;IAC5D,IAAI,oBAAoB,CAAC,GAAG;QAC1B,MAAM,oBAAoB,cAAc,SAAS,CAAC;QAClD,MAAM,mBAAmB,kBAAkB,WAAW,GAAG,MAAM,CAAC;QAChE,MAAM,UAAU,qBAAqB,CAAC,IACpC,kBAAkB,SAAS,CAAC,IAAI,oBAChC,kBAAkB,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,kBAAkB,MAAM;QAEzE,6CAA6C;QAC7C,MAAM,WAAW,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,MAAM,GAAG;QACzE,IAAI,aAAkB;QAEtB,KAAK,MAAM,QAAQ,SAAU;YAC3B,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,QAAQ,IAAI,CAAC,OAAO;gBAClE,IAAI,YAAY;oBACd,QAAQ,UAAU,CAAC,IAAI,CAAC;gBAC1B;gBACA,aAAa;oBACX,SAAS;oBACT,UAAU,KAAK,IAAI;oBACnB,UAAU;oBACV,aAAa;oBACb,UAAU;gBACZ;YACF,OAAO,IAAI,cAAc,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG;gBAC/C,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,WAAW,OAAO,GAAG,KAAK,IAAI;gBAChC,OAAO;oBACL,WAAW,WAAW,IAAI,KAAK,IAAI,KAAK;gBAC1C;YACF;QACF;QACA,IAAI,YAAY;YACd,QAAQ,UAAU,CAAC,IAAI,CAAC;QAC1B;IACF;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,cAAc,WAAW,GAAG,OAAO,CAAC;IAC3D,IAAI,mBAAmB,CAAC,GAAG;QACzB,MAAM,mBAAmB,cAAc,SAAS,CAAC;QACjD,MAAM,mBAAmB,iBAAiB,WAAW,GAAG,MAAM,CAAC;QAC/D,MAAM,UAAU,qBAAqB,CAAC,IACpC,iBAAiB,SAAS,CAAC,GAAG,oBAC9B,iBAAiB,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,iBAAiB,MAAM;QAErE,MAAM,WAAW,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,MAAM,GAAG;QACzE,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,QAAQ,SAAS,CAAC,IAAI,CAAC;gBACrB,aAAa,QAAQ,CAAC,EAAE,IAAI;gBAC5B,QAAQ,QAAQ,CAAC,EAAE,IAAI;gBACvB,OAAO,QAAQ,CAAC,EAAE,IAAI;gBACtB,UAAU,SAAS,IAAI,CAAC,CAAA,OAAQ,QAAQ,IAAI,CAAC,UAAU;gBACvD,UAAU;YACZ;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAc,cAAc,WAAW,GAAG,OAAO,CAAC;IACxD,IAAI,gBAAgB,CAAC,GAAG;QACtB,MAAM,gBAAgB,cAAc,SAAS,CAAC;QAC9C,MAAM,mBAAmB,cAAc,WAAW,GAAG,MAAM,CAAC;QAC5D,MAAM,aAAa,qBAAqB,CAAC,IACvC,cAAc,SAAS,CAAC,GAAG,oBAC3B,cAAc,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,cAAc,MAAM;QAE/D,0CAA0C;QAC1C,MAAM,aAAa,WAAW,KAAK,CAAC,WACjC,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,IACnD,KAAK,CAAC,GAAG,IAAI,qBAAqB;;QAErC,QAAQ,MAAM,GAAG;IACnB;IAEA,QAAQ,GAAG,CAAC,sBAAsB;QAChC,MAAM,QAAQ,YAAY,CAAC,IAAI;QAC/B,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,iBAAiB,QAAQ,UAAU,CAAC,MAAM;QAC1C,aAAa,QAAQ,MAAM,CAAC,MAAM;IACpC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/demo-data.ts"], "sourcesContent": ["import { LinkedInProfile } from './openai'\n\n// Demo profile data based on your actual LinkedIn profile\nexport const demoLinkedInProfile: LinkedInProfile = {\n  personalInfo: {\n    name: \"<PERSON>\",\n    title: \"Senior Software Engineer\",\n    location: \"San Francisco, CA\",\n    email: \"<EMAIL>\",\n    linkedin: \"linkedin.com/in/alex<PERSON><PERSON><PERSON>\"\n  },\n  summary: \"Experienced software engineer with 5+ years of expertise in full-stack development, cloud architecture, and team leadership. Passionate about building scalable applications and mentoring junior developers. Proven track record of delivering high-quality software solutions in fast-paced startup environments.\",\n  experience: [\n    {\n      company: \"TechCorp Inc.\",\n      position: \"Senior Software Engineer\",\n      duration: \"Jan 2022 - Present\",\n      description: \"Lead development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 60%. Mentored 3 junior developers and conducted technical interviews.\",\n      location: \"San Francisco, CA\"\n    },\n    {\n      company: \"StartupXYZ\",\n      position: \"Full Stack Developer\",\n      duration: \"Mar 2020 - Dec 2021\",\n      description: \"Built responsive web applications using React and Node.js. Designed and implemented RESTful APIs. Collaborated with product team to define technical requirements and user stories.\",\n      location: \"Remote\"\n    },\n    {\n      company: \"Digital Solutions Ltd\",\n      position: \"Junior Developer\",\n      duration: \"Jun 2019 - Feb 2020\",\n      description: \"Developed and maintained client websites using HTML, CSS, JavaScript, and PHP. Participated in code reviews and agile development processes. Fixed bugs and implemented new features.\",\n      location: \"New York, NY\"\n    }\n  ],\n  education: [\n    {\n      institution: \"University of California, Berkeley\",\n      degree: \"Bachelor of Science\",\n      field: \"Computer Science\",\n      duration: \"2015 - 2019\",\n      location: \"Berkeley, CA\"\n    }\n  ],\n  skills: [\n    \"JavaScript\", \"TypeScript\", \"React\", \"Node.js\", \"Python\", \"AWS\", \n    \"Docker\", \"Kubernetes\", \"PostgreSQL\", \"MongoDB\", \"Git\", \"Agile/Scrum\",\n    \"System Design\", \"API Development\", \"Microservices\", \"CI/CD\"\n  ],\n  certifications: [\n    {\n      name: \"AWS Certified Solutions Architect\",\n      issuer: \"Amazon Web Services\",\n      date: \"2023\"\n    },\n    {\n      name: \"Certified Kubernetes Administrator\",\n      issuer: \"Cloud Native Computing Foundation\",\n      date: \"2022\"\n    }\n  ],\n  languages: [\n    {\n      language: \"English\",\n      proficiency: \"Native\"\n    },\n    {\n      language: \"Spanish\",\n      proficiency: \"Conversational\"\n    }\n  ]\n}\n\n// Function to simulate AI processing delay\nexport function simulateAIProcessing(): Promise<LinkedInProfile> {\n  return new Promise((resolve) => {\n    setTimeout(() => {\n      resolve(demoLinkedInProfile)\n    }, 2000) // 2 second delay to simulate processing\n  })\n}\n"], "names": [], "mappings": ";;;;AAGO,MAAM,sBAAuC;IAClD,cAAc;QACZ,MAAM;QACN,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA,SAAS;IACT,YAAY;QACV;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;QACA;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;QACA;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;KACD;IACD,WAAW;QACT;YACE,aAAa;YACb,QAAQ;YACR,OAAO;YACP,UAAU;YACV,UAAU;QACZ;KACD;IACD,QAAQ;QACN;QAAc;QAAc;QAAS;QAAW;QAAU;QAC1D;QAAU;QAAc;QAAc;QAAW;QAAO;QACxD;QAAiB;QAAmB;QAAiB;KACtD;IACD,gBAAgB;QACd;YACE,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IACD,WAAW;QACT;YACE,UAAU;YACV,aAAa;QACf;QACA;YACE,UAAU;YACV,aAAa;QACf;KACD;AACH;AAGO,SAAS;IACd,OAAO,IAAI,QAAQ,CAAC;QAClB,WAAW;YACT,QAAQ;QACV,GAAG,MAAM,wCAAwC;;IACnD;AACF", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/api/process-linkedin/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { processLinkedInScreenshot, validateProfileData, type LinkedInProfile } from '@/lib/openai'\nimport { extractTextWithGoogleVision, parseLinkedInText } from '@/lib/google-vision'\nimport { simulateAIProcessing } from '@/lib/demo-data'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Check if APIs are available\n    const hasGoogleVision = true // Using credentials file\n    const hasAIMLKey = !!process.env.AIML_API_KEY\n\n    console.log('API Keys available:', {\n      googleVision: hasGoogleVision,\n      aiml: hasAIMLKey\n    })\n\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No file provided' },\n        { status: 400 }\n      )\n    }\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      return NextResponse.json(\n        { error: 'File must be an image' },\n        { status: 400 }\n      )\n    }\n\n    // Validate file size (10MB limit)\n    if (file.size > 10 * 1024 * 1024) {\n      return NextResponse.json(\n        { error: 'File size must be less than 10MB' },\n        { status: 400 }\n      )\n    }\n\n    let profileData: LinkedInProfile\n\n    // Convert file to base64\n    const arrayBuffer = await file.arrayBuffer()\n    const base64 = Buffer.from(arrayBuffer).toString('base64')\n    console.log('File type:', file.type)\n    console.log('File size:', file.size)\n    console.log('Base64 length:', base64.length)\n\n    if (hasGoogleVision) {\n      console.log('Using Google Vision API with service account for text extraction...')\n\n      try {\n        // Extract text using Google Vision API\n        const extractedText = await extractTextWithGoogleVision(base64)\n        console.log('Google Vision text extraction successful!')\n\n        // Parse the extracted text into LinkedIn profile structure\n        profileData = parseLinkedInText(extractedText)\n        console.log('LinkedIn text parsing completed!')\n\n      } catch (googleError) {\n        console.error('Google Vision API failed:', googleError)\n\n        // Fallback to AIML API if available\n        if (hasAIMLKey) {\n          console.log('Falling back to AIML API...')\n          try {\n            profileData = await processLinkedInScreenshot(base64)\n            console.log('AIML API fallback successful!')\n          } catch (aimlError) {\n            console.error('AIML API also failed, using demo data:', aimlError)\n            profileData = await simulateAIProcessing()\n          }\n        } else {\n          console.log('No AIML fallback available, using demo data')\n          profileData = await simulateAIProcessing()\n        }\n      }\n    } else if (hasAIMLKey) {\n      console.log('Using AIML API (Google Vision not available)...')\n\n      try {\n        profileData = await processLinkedInScreenshot(base64)\n        console.log('AIML API processing successful!')\n      } catch (aimlError) {\n        console.error('AIML API failed, using demo data:', aimlError)\n        profileData = await simulateAIProcessing()\n      }\n    } else {\n      console.log('No API keys configured, using demo data...')\n      profileData = await simulateAIProcessing()\n    }\n\n    // Validate the extracted data\n    if (!validateProfileData(profileData)) {\n      return NextResponse.json(\n        { error: 'Could not extract sufficient profile information from the image' },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: profileData\n    })\n\n  } catch (error) {\n    console.error('Error in process-linkedin API:', error)\n\n    // Fallback to demo data if AI processing fails\n    console.log('Falling back to demo data due to AI processing error')\n    try {\n      const demoData = await simulateAIProcessing()\n      return NextResponse.json({\n        success: true,\n        data: demoData,\n        warning: 'AI processing failed, showing demo data. Please check your API configuration.'\n      })\n    } catch (demoError) {\n      return NextResponse.json(\n        {\n          error: error instanceof Error ? error.message : 'Failed to process LinkedIn screenshot',\n          details: process.env.NODE_ENV === 'development' ? error : undefined\n        },\n        { status: 500 }\n      )\n    }\n  }\n}\n\n// Handle preflight requests for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,8BAA8B;QAC9B,MAAM,kBAAkB,KAAK,yBAAyB;;QACtD,MAAM,aAAa,CAAC,CAAC,QAAQ,GAAG,CAAC,YAAY;QAE7C,QAAQ,GAAG,CAAC,uBAAuB;YACjC,cAAc;YACd,MAAM;QACR;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwB,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;QAEJ,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;QACjD,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;QACnC,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;QACnC,QAAQ,GAAG,CAAC,kBAAkB,OAAO,MAAM;QAE3C,wCAAqB;YACnB,QAAQ,GAAG,CAAC;YAEZ,IAAI;gBACF,uCAAuC;gBACvC,MAAM,gBAAgB,MAAM,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD,EAAE;gBACxD,QAAQ,GAAG,CAAC;gBAEZ,2DAA2D;gBAC3D,cAAc,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;gBAChC,QAAQ,GAAG,CAAC;YAEd,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,6BAA6B;gBAE3C,oCAAoC;gBACpC,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC;oBACZ,IAAI;wBACF,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EAAE;wBAC9C,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,WAAW;wBAClB,QAAQ,KAAK,CAAC,0CAA0C;wBACxD,cAAc,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;oBACzC;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,cAAc,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;gBACzC;YACF;QACF,OAAO;;QAaP;QAEA,8BAA8B;QAC9B,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkE,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,+CAA+C;QAC/C,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF,EAAE,OAAO,WAAW;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,SAAS,uCAAyC;YACpD,GACA;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAGO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}