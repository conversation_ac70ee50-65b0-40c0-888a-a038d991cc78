{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/api/test-google-vision/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { extractTextWithGoogleVision } from '../../../lib/google-vision'\n\nexport async function GET() {\n  try {\n    if (!process.env.GOOGLE_VISION_API_KEY) {\n      return NextResponse.json({\n        error: 'Google Vision API key not configured',\n        instructions: 'Add GOOGLE_VISION_API_KEY to your .env.local file'\n      })\n    }\n\n    // Test with a simple image (1x1 pixel PNG with some text)\n    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='\n\n    console.log('Testing Google Vision API...')\n\n    const extractedText = await extractTextWithGoogleVision(testImageBase64)\n\n    return NextResponse.json({\n      success: true,\n      message: 'Google Vision API is working',\n      extractedText: extractedText || 'No text found in test image (this is normal)',\n      apiKeyConfigured: true\n    })\n\n  } catch (error) {\n    console.error('Google Vision API test failed:', error)\n    \n    return NextResponse.json({\n      error: 'Google Vision API test failed',\n      details: error instanceof Error ? error.message : 'Unknown error',\n      instructions: [\n        '1. Get API key from Google Cloud Console',\n        '2. Enable Vision API in your Google Cloud project',\n        '3. Add GOOGLE_VISION_API_KEY to .env.local',\n        '4. Make sure billing is enabled for your Google Cloud project'\n      ]\n    }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    if (!process.env.GOOGLE_VISION_API_KEY) {\n      return NextResponse.json({\n        error: 'Google Vision API key not configured'\n      })\n    }\n\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n\n    if (!file) {\n      return NextResponse.json({\n        error: 'No file provided'\n      }, { status: 400 })\n    }\n\n    // Convert file to base64\n    const arrayBuffer = await file.arrayBuffer()\n    const base64 = Buffer.from(arrayBuffer).toString('base64')\n\n    console.log('Testing Google Vision with uploaded file...')\n    console.log('File type:', file.type)\n    console.log('File size:', file.size)\n\n    const extractedText = await extractTextWithGoogleVision(base64)\n\n    return NextResponse.json({\n      success: true,\n      message: 'Google Vision text extraction successful',\n      extractedText: extractedText,\n      textLength: extractedText.length,\n      preview: extractedText.substring(0, 500) + (extractedText.length > 500 ? '...' : '')\n    })\n\n  } catch (error) {\n    console.error('Google Vision file test failed:', error)\n    \n    return NextResponse.json({\n      error: 'Google Vision file test failed',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;AAGO,eAAe;IACpB,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,qBAAqB,EAAE;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;gBACP,cAAc;YAChB;QACF;QAEA,0DAA0D;QAC1D,MAAM,kBAAkB;QAExB,QAAQ,GAAG,CAAC;QAEZ,MAAM,gBAAgB,MAAM,4BAA4B;QAExD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,eAAe,iBAAiB;YAChC,kBAAkB;QACpB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;QACH,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,qBAAqB,EAAE;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT;QACF;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;QAEjD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;QACnC,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;QAEnC,MAAM,gBAAgB,MAAM,4BAA4B;QAExD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,eAAe;YACf,YAAY,cAAc,MAAM;YAChC,SAAS,cAAc,SAAS,CAAC,GAAG,OAAO,CAAC,cAAc,MAAM,GAAG,MAAM,QAAQ,EAAE;QACrF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}