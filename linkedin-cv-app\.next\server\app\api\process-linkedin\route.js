const CHUNK_PUBLIC_PATH = "server/app/api/process-linkedin/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__95aa5dd2._.js");
runtime.loadChunk("server/chunks/node_modules_next_6d86badc._.js");
runtime.loadChunk("server/chunks/node_modules_openai_ba354692._.js");
runtime.loadChunk("server/chunks/node_modules_google-auth-library_6afb2aab._.js");
runtime.loadChunk("server/chunks/node_modules_fef93e42._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__c1a8ada7._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/process-linkedin/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/process-linkedin/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/process-linkedin/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
