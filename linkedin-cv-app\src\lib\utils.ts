import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Neobrutalism color utilities
export const neoColors = {
  black: '#000000',
  white: '#ffffff',
  yellow: '#ffff00',
  pink: '#ff00ff',
  cyan: '#00ffff',
  green: '#00ff00',
  red: '#ff0000',
  blue: '#0000ff',
  orange: '#ff8000',
  purple: '#8000ff',
} as const

export type NeoColor = keyof typeof neoColors

// Generate random neobrutalism color
export function getRandomNeoColor(): NeoColor {
  const colors = Object.keys(neoColors) as NeoColor[]
  return colors[Math.floor(Math.random() * colors.length)]
}

// File validation utilities
export function validateImageFile(file: File): boolean {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  const maxSize = 10 * 1024 * 1024 // 10MB
  
  return validTypes.includes(file.type) && file.size <= maxSize
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
