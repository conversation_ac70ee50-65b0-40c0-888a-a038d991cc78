{"version": 3, "file": "subchannel-interface.js", "sourceRoot": "", "sources": ["../../src/subchannel-interface.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAwDH,MAAsB,qBAAqB;IAGzC,YAAsB,KAA0B;QAA1B,UAAK,GAAL,KAAK,CAAqB;QAFxC,YAAO,GAAG,IAAI,CAAC;QACf,oBAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;QAEvD,KAAK,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAE;YACzC;oEACwD;YACxD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB;QAC3B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,oBAAoB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;IAC3C,CAAC;IACD,4BAA4B,CAAC,QAAmC;QAC9D,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;IACpD,CAAC;IACD,+BAA+B,CAAC,QAAmC;QACjE,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,QAAQ,CAAC,CAAC;IACvD,CAAC;IACD,eAAe;QACb,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IAC/B,CAAC;IACD,UAAU;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;IACjC,CAAC;IACD,iBAAiB,CAAC,gBAAwB;QACxC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;IACjD,CAAC;IACD,GAAG;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IACnB,CAAC;IACD,KAAK;QACH,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IACD,cAAc;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;IACrC,CAAC;IACD,SAAS;QACP,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;IAChD,CAAC;IACD,qBAAqB,CAAC,QAAwB;QAC5C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IACD,wBAAwB,CAAC,QAAwB;QAC/C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IACS,UAAU,CAAC,OAAgB;QACnC,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB;gEACoD;YACpD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC;gBAC3B,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;IACH,CAAC;IACD,iBAAiB;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;IACxC,CAAC;IACD,oBAAoB,CAAC,KAA0B;QAC7C,OAAO,IAAI,CAAC,iBAAiB,EAAE,KAAK,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAChE,CAAC;IACD,kBAAkB;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;IACzC,CAAC;CACF;AA1ED,sDA0EC"}