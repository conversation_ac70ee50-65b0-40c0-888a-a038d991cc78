'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import { Download, Edit, ArrowLeft, CheckCircle, FileText, User, Briefcase, GraduationCap, Award } from 'lucide-react'
import { LinkedInProfile } from '@/lib/openai'

export default function ResultsPage() {
  const [isDownloading, setIsDownloading] = useState(false)
  const [profileData, setProfileData] = useState<LinkedInProfile | null>(null)
  const router = useRouter()

  useEffect(() => {
    // Get the profile data from sessionStorage
    const storedData = sessionStorage.getItem('linkedinProfile')
    if (storedData) {
      try {
        setProfileData(JSON.parse(storedData))
      } catch (error) {
        console.error('Failed to parse profile data:', error)
        router.push('/upload')
      }
    } else {
      // No data found, redirect to upload
      router.push('/upload')
    }
  }, [router])

  const handleDownloadPDF = async () => {
    setIsDownloading(true)
    
    // TODO: Implement PDF generation
    // For now, simulate download time
    setTimeout(() => {
      setIsDownloading(false)
      // In a real app, this would trigger the PDF download
      alert('PDF download would start here!')
    }, 2000)
  }

  const handleEditCV = () => {
    // TODO: Navigate to CV editor
    alert('CV editor would open here!')
  }

  const goBack = () => {
    router.push('/upload')
  }

  const startOver = () => {
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="neo-border-thick border-t-0 border-l-0 border-r-0 bg-green-400 p-6">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="secondary" size="sm" onClick={goBack}>
              <ArrowLeft size={16} className="mr-2" />
              Back
            </Button>
            <h1 className="text-3xl font-bold uppercase tracking-wide">
              Your CV is Ready!
            </h1>
          </div>
          <div className="text-sm font-bold uppercase">
            Step 4 of 4
          </div>
        </div>
      </header>

      {/* Success Message */}
      <section className="py-12 px-6">
        <div className="max-w-4xl mx-auto">
          <Card className="mb-8 text-center" color="yellow">
            <CardContent>
              <div className="flex justify-center mb-6">
                <div className="neo-border neo-shadow-lg p-6 bg-green-400">
                  <CheckCircle size={48} className="text-black" />
                </div>
              </div>
              <h2 className="text-4xl font-bold uppercase tracking-wide mb-4">
                Success! Your CV is Ready
              </h2>
              <p className="text-xl font-medium mb-6">
                Our AI has successfully processed your LinkedIn profile and created a professional CV.
              </p>
            </CardContent>
          </Card>

          {/* CV Preview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* CV Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <FileText size={24} />
                  Extracted Profile Data
                </CardTitle>
              </CardHeader>
              <CardContent>
                {profileData ? (
                  <div className="neo-border neo-shadow bg-gray-50 p-6 max-h-96 overflow-y-auto">
                    {/* Personal Info */}
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-3">
                        <User size={20} />
                        <h3 className="font-bold uppercase">Personal Information</h3>
                      </div>
                      <div className="space-y-1 text-sm">
                        <p><strong>Name:</strong> {profileData.personalInfo.name}</p>
                        <p><strong>Title:</strong> {profileData.personalInfo.title}</p>
                        <p><strong>Location:</strong> {profileData.personalInfo.location}</p>
                        {profileData.personalInfo.email && (
                          <p><strong>Email:</strong> {profileData.personalInfo.email}</p>
                        )}
                      </div>
                    </div>

                    {/* Experience */}
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-3">
                        <Briefcase size={20} />
                        <h3 className="font-bold uppercase">Experience</h3>
                      </div>
                      <div className="space-y-3">
                        {profileData.experience.slice(0, 2).map((exp, index) => (
                          <div key={index} className="text-sm">
                            <p className="font-bold">{exp.position}</p>
                            <p className="font-medium">{exp.company} • {exp.duration}</p>
                          </div>
                        ))}
                        {profileData.experience.length > 2 && (
                          <p className="text-sm text-gray-600">
                            +{profileData.experience.length - 2} more positions
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Education */}
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-3">
                        <GraduationCap size={20} />
                        <h3 className="font-bold uppercase">Education</h3>
                      </div>
                      <div className="space-y-2">
                        {profileData.education.map((edu, index) => (
                          <div key={index} className="text-sm">
                            <p className="font-bold">{edu.degree} in {edu.field}</p>
                            <p className="font-medium">{edu.institution}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Skills */}
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <Award size={20} />
                        <h3 className="font-bold uppercase">Skills</h3>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {profileData.skills.slice(0, 8).map((skill, index) => (
                          <span key={index} className="neo-border px-2 py-1 text-xs font-medium bg-white">
                            {skill}
                          </span>
                        ))}
                        {profileData.skills.length > 8 && (
                          <span className="text-xs text-gray-600">
                            +{profileData.skills.length - 8} more
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="neo-border neo-shadow bg-gray-50 p-6 min-h-96">
                    <div className="text-center text-gray-500">
                      <FileText size={64} className="mx-auto mb-4 opacity-50" />
                      <p className="font-bold uppercase">Loading Profile Data...</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="space-y-6">
              <Card color="cyan">
                <CardHeader>
                  <CardTitle>Download Your CV</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-medium mb-4">
                    Your professional CV is ready for download as a PDF file.
                  </p>
                  <Button
                    size="lg"
                    color="pink"
                    onClick={handleDownloadPDF}
                    disabled={isDownloading}
                    className="w-full flex items-center justify-center gap-3"
                  >
                    {isDownloading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-black border-t-transparent"></div>
                        Generating PDF...
                      </>
                    ) : (
                      <>
                        <Download size={20} />
                        Download PDF
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              <Card color="orange">
                <CardHeader>
                  <CardTitle>Customize Your CV</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-medium mb-4">
                    Want to make changes? Edit your CV before downloading.
                  </p>
                  <Button
                    size="lg"
                    variant="secondary"
                    onClick={handleEditCV}
                    className="w-full flex items-center justify-center gap-3"
                  >
                    <Edit size={20} />
                    Edit CV
                  </Button>
                </CardContent>
              </Card>

              <Card color="purple">
                <CardHeader>
                  <CardTitle>Start Over</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-medium mb-4">
                    Want to create another CV? Start with a new LinkedIn screenshot.
                  </p>
                  <Button
                    size="lg"
                    variant="secondary"
                    onClick={startOver}
                    className="w-full"
                  >
                    Create New CV
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Stats */}
          {profileData && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="text-center" color="yellow">
                <CardContent>
                  <div className="text-3xl font-bold uppercase mb-2">💼</div>
                  <h3 className="font-bold uppercase mb-1">Work Experience</h3>
                  <p className="font-medium">{profileData.experience.length} positions</p>
                </CardContent>
              </Card>

              <Card className="text-center" color="cyan">
                <CardContent>
                  <div className="text-3xl font-bold uppercase mb-2">🎓</div>
                  <h3 className="font-bold uppercase mb-1">Education</h3>
                  <p className="font-medium">{profileData.education.length} degrees</p>
                </CardContent>
              </Card>

              <Card className="text-center" color="pink">
                <CardContent>
                  <div className="text-3xl font-bold uppercase mb-2">⚡</div>
                  <h3 className="font-bold uppercase mb-1">Skills Identified</h3>
                  <p className="font-medium">{profileData.skills.length} skills</p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </section>
    </div>
  )
}
