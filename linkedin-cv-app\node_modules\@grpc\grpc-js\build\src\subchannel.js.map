{"version": 3, "file": "subchannel.js", "sourceRoot": "", "sources": ["../../src/subchannel.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAKH,6DAAyD;AACzD,uDAAmE;AACnE,qCAAqC;AACrC,2CAAmD;AACnD,6CAAoD;AACpD,6DAG8B;AAC9B,yCAWoB;AAUpB,MAAM,WAAW,GAAG,YAAY,CAAC;AAEjC;;qBAEqB;AACrB,MAAM,qBAAqB,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAEzC,MAAa,UAAU;IAkDrB;;;;;;;;;OASG;IACH,YACU,aAAsB,EACtB,iBAAoC,EACpC,OAAuB,EAC/B,WAA+B,EACvB,SAA8B;;QAJ9B,kBAAa,GAAb,aAAa,CAAS;QACtB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,YAAO,GAAP,OAAO,CAAgB;QAEvB,cAAS,GAAT,SAAS,CAAqB;QAhExC;;;WAGG;QACK,sBAAiB,GAAsB,sCAAiB,CAAC,IAAI,CAAC;QACtE;;WAEG;QACK,cAAS,GAAqB,IAAI,CAAC;QAC3C;;;WAGG;QACK,uBAAkB,GAAG,KAAK,CAAC;QACnC;;;;WAIG;QACK,mBAAc,GAAmC,IAAI,GAAG,EAAE,CAAC;QAKnE;;WAEG;QACK,aAAQ,GAAG,CAAC,CAAC;QAOrB,gBAAgB;QACC,oBAAe,GAAY,IAAI,CAAC;QA+B/C,MAAM,cAAc,GAAmB;YACrC,YAAY,EAAE,OAAO,CAAC,mCAAmC,CAAC;YAC1D,QAAQ,EAAE,OAAO,CAAC,+BAA+B,CAAC;SACnD,CAAC;QACF,IAAI,CAAC,cAAc,GAAG,IAAI,gCAAc,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC,EAAE,cAAc,CAAC,CAAC;QACnB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,uBAAuB,GAAG,IAAA,8CAAyB,EAAC,iBAAiB,CAAC,CAAC;QAE5E,IAAI,CAAC,aAAa,GAAG,MAAA,OAAO,CAAC,wBAAwB,CAAC,mCAAI,CAAC,CAAC,CAAC;QAE7D,IAAI,OAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,4BAAiB,EAAE,CAAC;YAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,kCAAuB,EAAE,CAAC;YACjD,IAAI,CAAC,eAAe,GAAG,IAAI,sCAA2B,EAAE,CAAC;YACzD,IAAI,CAAC,aAAa,GAAG,IAAI,kCAAuB,EAAE,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,IAAI,wBAAa,EAAE,CAAC;YACzC,IAAI,CAAC,WAAW,GAAG,IAAI,8BAAmB,EAAE,CAAC;YAC7C,IAAI,CAAC,eAAe,GAAG,IAAI,kCAAuB,EAAE,CAAC;YACrD,IAAI,CAAC,aAAa,GAAG,IAAI,8BAAmB,EAAE,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAA,qCAA0B,EAC3C,IAAI,CAAC,uBAAuB,EAC5B,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,EAC5B,IAAI,CAAC,eAAe,CACrB,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;QAC7D,IAAI,CAAC,KAAK,CACR,sCAAsC;YACpC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,CACxC,CAAC;QACF,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,sBAAsB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IACpF,CAAC;IAEO,eAAe;QACrB,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,iBAAiB;YAC7B,KAAK,EAAE,IAAI,CAAC,aAAa;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;YAC9C,MAAM,EAAE,IAAI,CAAC,uBAAuB;SACrC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,IAAY;QACxB,OAAO,CAAC,KAAK,CACX,wBAAY,CAAC,KAAK,EAClB,WAAW,EACX,GAAG;YACD,IAAI,CAAC,WAAW,CAAC,EAAE;YACnB,IAAI;YACJ,IAAI,CAAC,uBAAuB;YAC5B,GAAG;YACH,IAAI,CACP,CAAC;IACJ,CAAC;IAEO,QAAQ,CAAC,IAAY;QAC3B,OAAO,CAAC,KAAK,CACX,wBAAY,CAAC,KAAK,EAClB,qBAAqB,EACrB,GAAG;YACD,IAAI,CAAC,WAAW,CAAC,EAAE;YACnB,IAAI;YACJ,IAAI,CAAC,uBAAuB;YAC5B,GAAG;YACH,IAAI,CACP,CAAC;IACJ,CAAC;IAEO,kBAAkB;QACxB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CACpB,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,EACrC,sCAAiB,CAAC,UAAU,CAC7B,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,iBAAiB,CACpB,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,EACrC,sCAAiB,CAAC,IAAI,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAEO,uBAAuB;QAC7B,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,IAAI,OAAO,CAAC,wBAAwB,CAAC,EAAE,CAAC;YACtC,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CACpC,IAAI,CAAC,aAAa,EAClB,qBAAqB,CACtB,CAAC;YACF,OAAO,mCAAQ,OAAO,KAAE,wBAAwB,EAAE,qBAAqB,GAAE,CAAC;QAC5E,CAAC;QACD,IAAI,CAAC,SAAS;aACX,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;aAC9D,IAAI,CACH,SAAS,CAAC,EAAE;YACV,IACE,IAAI,CAAC,iBAAiB,CACpB,CAAC,sCAAiB,CAAC,UAAU,CAAC,EAC9B,sCAAiB,CAAC,KAAK,CACxB,EACD,CAAC;gBACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;gBAC3B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACzB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC;gBAC5D,CAAC;gBACD,SAAS,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAE;oBAC7C,IAAI,CAAC,iBAAiB,CACpB,CAAC,sCAAiB,CAAC,KAAK,CAAC,EACzB,sCAAiB,CAAC,IAAI,CACvB,CAAC;oBACF,IAAI,YAAY,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;wBAC3C,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC;wBACxB,OAAO,CAAC,GAAG,CACT,wBAAY,CAAC,KAAK,EAClB,iBAAiB,IAAA,wBAAW,EAAC,IAAI,CAAC,aAAa,CAAC,OAC9C,IAAI,CAAC,uBACP,4EACE,IAAI,CAAC,aACP,KAAK,CACN,CAAC;oBACJ,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN;4EAC4D;gBAC5D,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvB,CAAC;QACH,CAAC,EACD,KAAK,CAAC,EAAE;YACN,IAAI,CAAC,iBAAiB,CACpB,CAAC,sCAAiB,CAAC,UAAU,CAAC,EAC9B,sCAAiB,CAAC,iBAAiB,EACnC,GAAG,KAAK,EAAE,CACX,CAAC;QACJ,CAAC,CACF,CAAC;IACN,CAAC;IAED;;;;;;OAMG;IACK,iBAAiB,CACvB,SAA8B,EAC9B,QAA2B,EAC3B,YAAqB;;QAErB,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,KAAK,CACR,sCAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACvC,MAAM;gBACN,sCAAiB,CAAC,QAAQ,CAAC;gBAC3B,eAAe,GAAG,YAAY,GAAG,GAAG,CACvC,CAAC;QAEJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CACR,sCAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACvC,MAAM;gBACN,sCAAiB,CAAC,QAAQ,CAAC,CAC9B,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,aAAa,CAAC,QAAQ,CACzB,SAAS,EACT,+BAA+B,GAAG,sCAAiB,CAAC,QAAQ,CAAC,CAC9D,CAAC;QACJ,CAAC;QACD,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC7C,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;QAClC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,sCAAiB,CAAC,KAAK;gBAC1B,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,MAAM;YACR,KAAK,sCAAiB,CAAC,UAAU;gBAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;gBAChC,MAAM;YACR,KAAK,sCAAiB,CAAC,iBAAiB;gBACtC,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC3C,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC;gBACnE,CAAC;gBACD,MAAA,IAAI,CAAC,SAAS,0CAAE,QAAQ,EAAE,CAAC;gBAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB;;uFAEuE;gBACvE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,CAAC;oBACrC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;wBACpB,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;YACR,KAAK,sCAAiB,CAAC,IAAI;gBACzB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC3C,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC;gBACnE,CAAC;gBACD,MAAA,IAAI,CAAC,SAAS,0CAAE,QAAQ,EAAE,CAAC;gBAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,4CAA4C,QAAQ,EAAE,CAAC,CAAC;QAC5E,CAAC;QACD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC3C,QAAQ,CAAC,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;QAC5E,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,GAAG;QACD,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;IACrB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACnB,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;YACxD,IAAA,gCAAqB,EAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC/B,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;gBACpB,IAAI,CAAC,iBAAiB,CACpB,CAAC,sCAAiB,CAAC,UAAU,EAAE,sCAAiB,CAAC,KAAK,CAAC,EACvD,sCAAiB,CAAC,IAAI,CACvB,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,aAAa;QACX,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,UAAU,CACR,QAAkB,EAClB,IAAY,EACZ,MAAc,EACd,QAA4C;QAE5C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,YAAuC,CAAC;QAC5C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YACpC,YAAY,GAAG;gBACb,SAAS,EAAE,MAAM,CAAC,EAAE;oBAClB,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAM,CAAC,EAAE,EAAE,CAAC;wBAC9B,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;oBACtC,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;oBACnC,CAAC;gBACH,CAAC;aACF,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,YAAY,GAAG,EAAE,CAAC;QACpB,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAC9B,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,YAAY,CACb,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,eAAe;QACb,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;YACpB;;;+EAGmE;YACnE,IACE,CAAC,IAAI,CAAC,iBAAiB,CACrB,CAAC,sCAAiB,CAAC,IAAI,CAAC,EACxB,sCAAiB,CAAC,UAAU,CAC7B,EACD,CAAC;gBACD,IAAI,IAAI,CAAC,iBAAiB,KAAK,sCAAiB,CAAC,iBAAiB,EAAE,CAAC;oBACnE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,4BAA4B,CAAC,QAAmC;QAC9D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,+BAA+B,CAAC,QAAmC;QACjE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CACpB,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,EACrC,sCAAiB,CAAC,UAAU,CAC7B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC;IACd,CAAC;IAED,qBAAqB,CAAC,QAAoC;QACxD,+BAA+B;IACjC,CAAC;IAED,wBAAwB,CAAC,QAAoC;QAC3D,+BAA+B;IACjC,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IAED,oBAAoB,CAAC,KAA0B;QAC7C,OAAO,KAAK,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC;IAC5C,CAAC;IAED,iBAAiB,CAAC,gBAAwB;QACxC,IAAI,gBAAgB,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC1C,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC;QACxC,CAAC;IACH,CAAC;IACD,kBAAkB;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;IACnD,CAAC;CACF;AAhdD,gCAgdC"}