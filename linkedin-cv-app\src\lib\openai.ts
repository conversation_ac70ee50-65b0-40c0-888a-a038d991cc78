import OpenAI from 'openai'

// Initialize AIML API client (using OpenAI SDK with custom base URL)
const aimlClient = new OpenAI({
  apiKey: process.env.AIML_API_KEY,
  baseURL: 'https://api.aimlapi.com',
})

// AIML API provides access to GPT-4o and other advanced vision models

export interface LinkedInProfile {
  personalInfo: {
    name: string
    title: string
    location: string
    email?: string
    phone?: string
    linkedin?: string
  }
  summary: string
  experience: Array<{
    company: string
    position: string
    duration: string
    description: string
    location?: string
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    duration: string
    location?: string
  }>
  skills: string[]
  certifications?: Array<{
    name: string
    issuer: string
    date?: string
  }>
  languages?: Array<{
    language: string
    proficiency: string
  }>
}

export async function processLinkedInScreenshot(imageBase64: string): Promise<LinkedInProfile> {
  try {
    console.log('Starting AIML API request...')

    const response = await aimlClient.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `You are a professional CV extraction specialist. Analyze this LinkedIn profile screenshot and extract ALL visible professional information with high accuracy.

EXTRACT THE FOLLOWING DATA:

1. **Personal Information:**
   - Full name (exactly as shown)
   - Current job title/headline
   - Location (city, state/country)
   - Contact information (email, phone if visible)
   - LinkedIn URL (if visible)

2. **Professional Summary:**
   - Complete "About" section text
   - Professional headline/tagline

3. **Work Experience:** (For each position)
   - Company name
   - Job title/position
   - Employment duration (start - end dates)
   - Job description/responsibilities
   - Location (if shown)

4. **Education:** (For each degree)
   - Institution name
   - Degree type and field of study
   - Duration/graduation year
   - Location (if shown)

5. **Skills:**
   - All listed skills (technical and soft skills)
   - Endorsement counts (if visible)

6. **Certifications:** (if any)
   - Certification name
   - Issuing organization
   - Date obtained

7. **Languages:** (if any)
   - Language name
   - Proficiency level

**CRITICAL REQUIREMENTS:**
- Return ONLY valid JSON - no explanations, no markdown, no code blocks
- Do not wrap the JSON in code blocks or any other formatting
- Start your response with { and end with }
- Use the exact structure provided below
- Extract text exactly as written (preserve professional language)
- If information is not visible, use empty string "" or empty array []
- Be thorough - this data will generate someone's professional CV

**REQUIRED JSON STRUCTURE (return exactly this format):**
{
  "personalInfo": {
    "name": "",
    "title": "",
    "location": "",
    "email": "",
    "phone": "",
    "linkedin": ""
  },
  "summary": "",
  "experience": [
    {
      "company": "",
      "position": "",
      "duration": "",
      "description": "",
      "location": ""
    }
  ],
  "education": [
    {
      "institution": "",
      "degree": "",
      "field": "",
      "duration": "",
      "location": ""
    }
  ],
  "skills": [],
  "certifications": [
    {
      "name": "",
      "issuer": "",
      "date": ""
    }
  ],
  "languages": [
    {
      "language": "",
      "proficiency": ""
    }
  ]
}`
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: 4000,
      temperature: 0.1, // Low temperature for consistent, accurate extraction
      top_p: 0.9, // Focus on most likely tokens for accuracy
    })

    console.log('AIML API response received')
    console.log('Response object:', JSON.stringify(response, null, 2))

    const content = response.choices[0]?.message?.content
    console.log('Response content length:', content?.length || 0)
    console.log('Response content preview:', content?.substring(0, 200))
    if (!content) {
      throw new Error('No response from AIML API')
    }

    console.log('Raw AI response:', content.substring(0, 500) + '...')

    // Try to parse the JSON response
    try {
      const profileData = JSON.parse(content) as LinkedInProfile
      console.log('Successfully parsed JSON response')
      return profileData
    } catch (parseError) {
      console.log('Initial JSON parse failed, trying to extract JSON...')

      // If JSON parsing fails, try to extract JSON from the response
      // Look for JSON between ```json and ``` or just between { and }
      let jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
      if (!jsonMatch) {
        jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/)
      }
      if (!jsonMatch) {
        jsonMatch = content.match(/(\{[\s\S]*\})/)
      }

      if (jsonMatch) {
        try {
          const cleanedJson = jsonMatch[1] || jsonMatch[0]
          console.log('Extracted JSON:', cleanedJson.substring(0, 200) + '...')
          const profileData = JSON.parse(cleanedJson) as LinkedInProfile
          console.log('Successfully parsed extracted JSON')
          return profileData
        } catch (secondParseError) {
          console.error('Failed to parse extracted JSON:', secondParseError)
        }
      }

      console.error('Raw response that failed to parse:', content)
      throw new Error('Failed to parse AI response as JSON')
    }

  } catch (error) {
    console.error('Error processing LinkedIn screenshot with AIML API:', error)

    // Provide more detailed error information
    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }

    // Check if it's an API-related error
    if (error && typeof error === 'object' && 'response' in error) {
      console.error('API response error:', error)
    }

    throw new Error(`Failed to process LinkedIn screenshot with AI: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Helper function to convert file to base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Validate extracted profile data
export function validateProfileData(profile: LinkedInProfile): boolean {
  return !!(
    profile.personalInfo?.name &&
    profile.personalInfo?.title &&
    (profile.experience?.length > 0 || profile.education?.length > 0)
  )
}
