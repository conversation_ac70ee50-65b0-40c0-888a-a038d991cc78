import OpenAI from 'openai'

// Initialize AIML API client (using OpenAI SDK with custom base URL)
const aimlClient = new OpenAI({
  apiKey: process.env.AIML_API_KEY,
  baseURL: 'https://api.aimlapi.com',
})

// AIML API provides access to GPT-4o and other advanced vision models

export interface LinkedInProfile {
  personalInfo: {
    name: string
    title: string
    location: string
    email?: string
    phone?: string
    linkedin?: string
  }
  summary: string
  experience: Array<{
    company: string
    position: string
    duration: string
    description: string
    location?: string
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    duration: string
    location?: string
  }>
  skills: string[]
  certifications?: Array<{
    name: string
    issuer: string
    date?: string
  }>
  languages?: Array<{
    language: string
    proficiency: string
  }>
}

export async function processLinkedInScreenshot(imageBase64: string): Promise<LinkedInProfile> {
  const maxRetries = 2
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Starting AIML API request (attempt ${attempt}/${maxRetries})...`)

      const response = await aimlClient.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `You are an expert LinkedIn profile analyzer. Carefully examine this LinkedIn profile screenshot and extract ALL visible professional information with maximum accuracy and detail.

EXTRACTION REQUIREMENTS:
1. Read ALL text visible in the image carefully
2. Extract information exactly as written (preserve original wording)
3. Include ALL work experiences, education entries, and skills shown
4. Capture complete job descriptions and summaries
5. Note any certifications, languages, or additional sections visible

RETURN FORMAT: JSON object only, no explanations or formatting.

{
  "personalInfo": {
    "name": "Extract full name exactly as shown",
    "title": "Current headline/job title from top of profile",
    "location": "Geographic location shown",
    "email": "Email if visible in contact info",
    "phone": "Phone if visible in contact info",
    "linkedin": "LinkedIn URL if visible"
  },
  "summary": "Complete About/Summary section text - extract the full paragraph(s) exactly as written",
  "experience": [
    {
      "company": "Company name exactly as shown",
      "position": "Job title exactly as shown",
      "duration": "Employment dates (e.g., 'Jan 2020 - Present' or 'Jun 2018 - Dec 2019')",
      "description": "Complete job description - extract all bullet points and details exactly as written",
      "location": "Job location if shown"
    }
  ],
  "education": [
    {
      "institution": "School/University name exactly as shown",
      "degree": "Degree type (e.g., 'Bachelor of Science', 'Master of Arts')",
      "field": "Field of study exactly as shown",
      "duration": "Years attended or graduation year",
      "location": "School location if shown"
    }
  ],
  "skills": ["Extract ALL skills shown - include every skill listed in the skills section"],
  "certifications": [
    {
      "name": "Certification name exactly as shown",
      "issuer": "Issuing organization",
      "date": "Date obtained if shown"
    }
  ],
  "languages": [
    {
      "language": "Language name",
      "proficiency": "Proficiency level if shown (e.g., 'Native', 'Professional', 'Conversational')"
    }
  ]
}

CRITICAL: Extract information exactly as it appears. Be thorough and include ALL visible details.`
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: 6000,
      temperature: 0.05, // Very low temperature for maximum accuracy
      top_p: 0.95, // Focus on most likely tokens for accuracy
    })

    console.log('AIML API response received')
    console.log('Response object:', JSON.stringify(response, null, 2))

    const content = response.choices[0]?.message?.content
    console.log('Response content length:', content?.length || 0)
    console.log('Response content preview:', content?.substring(0, 200))
    if (!content) {
      throw new Error('No response from AIML API')
    }

    console.log('Raw AI response:', content.substring(0, 500) + '...')

    // Try to parse the JSON response
    try {
      const rawProfileData = JSON.parse(content) as LinkedInProfile
      console.log('Successfully parsed JSON response')

      // Clean and enhance the extracted data
      const cleanedProfileData = cleanProfileData(rawProfileData)
      console.log('Data cleaning completed')

      return cleanedProfileData
    } catch (parseError) {
      console.log('Initial JSON parse failed, trying to extract JSON...')

      // If JSON parsing fails, try to extract JSON from the response
      // Look for JSON between ```json and ``` or just between { and }
      let jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
      if (!jsonMatch) {
        jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/)
      }
      if (!jsonMatch) {
        jsonMatch = content.match(/(\{[\s\S]*\})/)
      }

      if (jsonMatch) {
        try {
          const cleanedJson = jsonMatch[1] || jsonMatch[0]
          console.log('Extracted JSON:', cleanedJson.substring(0, 200) + '...')
          const rawProfileData = JSON.parse(cleanedJson) as LinkedInProfile
          console.log('Successfully parsed extracted JSON')

          // Clean and enhance the extracted data
          const cleanedProfileData = cleanProfileData(rawProfileData)
          console.log('Data cleaning completed')

          return cleanedProfileData
        } catch (secondParseError) {
          console.error('Failed to parse extracted JSON:', secondParseError)
        }
      }

      console.error('Raw response that failed to parse:', content)
      throw new Error('Failed to parse AI response as JSON')
    }

    } catch (error) {
      console.error(`Attempt ${attempt} failed:`, error)
      lastError = error instanceof Error ? error : new Error('Unknown error')

      if (attempt < maxRetries) {
        console.log(`Retrying in 2 seconds...`)
        await new Promise(resolve => setTimeout(resolve, 2000))
        continue
      }
    }
  }

  // If all attempts failed, throw the last error
  if (lastError) {
    console.error('All attempts failed. Error processing LinkedIn screenshot with AIML API:', lastError)

    // Provide more detailed error information
    if (lastError instanceof Error) {
      console.error('Error message:', lastError.message)
      console.error('Error stack:', lastError.stack)
    }

    // Check if it's an API-related error
    if (lastError && typeof lastError === 'object' && 'response' in lastError) {
      console.error('API response error:', lastError)
    }

    throw new Error(`Failed to process LinkedIn screenshot with AI after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : 'Unknown error'}`)
  }

  // This should never be reached, but TypeScript requires it
  throw new Error('Unexpected error in processLinkedInScreenshot')
}

// Helper function to convert file to base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Validate extracted profile data
export function validateProfileData(profile: LinkedInProfile): boolean {
  return !!(
    profile.personalInfo?.name &&
    profile.personalInfo?.title &&
    (profile.experience?.length > 0 || profile.education?.length > 0)
  )
}

// Clean and enhance extracted profile data
export function cleanProfileData(profile: LinkedInProfile): LinkedInProfile {
  return {
    personalInfo: {
      name: cleanText(profile.personalInfo?.name || ''),
      title: cleanText(profile.personalInfo?.title || ''),
      location: cleanText(profile.personalInfo?.location || ''),
      email: cleanEmail(profile.personalInfo?.email || ''),
      phone: cleanPhone(profile.personalInfo?.phone || ''),
      linkedin: cleanUrl(profile.personalInfo?.linkedin || '')
    },
    summary: cleanText(profile.summary || ''),
    experience: (profile.experience || []).map(exp => ({
      company: cleanText(exp.company || ''),
      position: cleanText(exp.position || ''),
      duration: cleanDuration(exp.duration || ''),
      description: cleanText(exp.description || ''),
      location: cleanText(exp.location || '')
    })),
    education: (profile.education || []).map(edu => ({
      institution: cleanText(edu.institution || ''),
      degree: cleanText(edu.degree || ''),
      field: cleanText(edu.field || ''),
      duration: cleanDuration(edu.duration || ''),
      location: cleanText(edu.location || '')
    })),
    skills: (profile.skills || []).map(skill => cleanText(skill)).filter(skill => skill.length > 0),
    certifications: (profile.certifications || []).map(cert => ({
      name: cleanText(cert.name || ''),
      issuer: cleanText(cert.issuer || ''),
      date: cleanText(cert.date || '')
    })),
    languages: (profile.languages || []).map(lang => ({
      language: cleanText(lang.language || ''),
      proficiency: cleanText(lang.proficiency || '')
    }))
  }
}

// Helper functions for data cleaning
function cleanText(text: string): string {
  return text.trim().replace(/\s+/g, ' ').replace(/[""]/g, '"').replace(/['']/g, "'")
}

function cleanEmail(email: string): string {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.trim()) ? email.trim().toLowerCase() : ''
}

function cleanPhone(phone: string): string {
  const cleaned = phone.replace(/[^\d+\-\(\)\s]/g, '').trim()
  return cleaned.length >= 10 ? cleaned : ''
}

function cleanUrl(url: string): string {
  if (!url) return ''
  if (url.startsWith('http')) return url
  if (url.includes('linkedin.com')) return `https://${url}`
  return url
}

function cleanDuration(duration: string): string {
  return duration.trim().replace(/\s+/g, ' ')
}
