import OpenAI from 'openai'

// Initialize AIML API client (using OpenAI SDK with custom base URL)
const aimlClient = new OpenAI({
  apiKey: process.env.AIML_API_KEY,
  baseURL: 'https://api.aimlapi.com',
})

// AIML API provides access to GPT-4o and other advanced vision models

export interface LinkedInProfile {
  personalInfo: {
    name: string
    title: string
    location: string
    email?: string
    phone?: string
    linkedin?: string
  }
  summary: string
  experience: Array<{
    company: string
    position: string
    duration: string
    description: string
    location?: string
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    duration: string
    location?: string
  }>
  skills: string[]
  certifications?: Array<{
    name: string
    issuer: string
    date?: string
  }>
  languages?: Array<{
    language: string
    proficiency: string
  }>
}

export async function processLinkedInScreenshot(imageBase64: string): Promise<LinkedInProfile> {
  try {
    const response = await aimlClient.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `You are a professional CV extraction specialist. Analyze this LinkedIn profile screenshot and extract ALL visible professional information with high accuracy.

EXTRACT THE FOLLOWING DATA:

1. **Personal Information:**
   - Full name (exactly as shown)
   - Current job title/headline
   - Location (city, state/country)
   - Contact information (email, phone if visible)
   - LinkedIn URL (if visible)

2. **Professional Summary:**
   - Complete "About" section text
   - Professional headline/tagline

3. **Work Experience:** (For each position)
   - Company name
   - Job title/position
   - Employment duration (start - end dates)
   - Job description/responsibilities
   - Location (if shown)

4. **Education:** (For each degree)
   - Institution name
   - Degree type and field of study
   - Duration/graduation year
   - Location (if shown)

5. **Skills:**
   - All listed skills (technical and soft skills)
   - Endorsement counts (if visible)

6. **Certifications:** (if any)
   - Certification name
   - Issuing organization
   - Date obtained

7. **Languages:** (if any)
   - Language name
   - Proficiency level

**IMPORTANT REQUIREMENTS:**
- Return ONLY valid JSON format
- Use the exact structure provided below
- Extract text exactly as written (preserve professional language)
- If information is not visible, use empty string "" or empty array []
- Be thorough - this data will generate someone's professional CV

**JSON STRUCTURE:**
{
  "personalInfo": {
    "name": "",
    "title": "",
    "location": "",
    "email": "",
    "phone": "",
    "linkedin": ""
  },
  "summary": "",
  "experience": [
    {
      "company": "",
      "position": "",
      "duration": "",
      "description": "",
      "location": ""
    }
  ],
  "education": [
    {
      "institution": "",
      "degree": "",
      "field": "",
      "duration": "",
      "location": ""
    }
  ],
  "skills": [],
  "certifications": [
    {
      "name": "",
      "issuer": "",
      "date": ""
    }
  ],
  "languages": [
    {
      "language": "",
      "proficiency": ""
    }
  ]
}`
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: 4000,
      temperature: 0.1, // Low temperature for consistent, accurate extraction
      top_p: 0.9, // Focus on most likely tokens for accuracy
    })

    const content = response.choices[0]?.message?.content
    if (!content) {
      throw new Error('No response from OpenAI')
    }

    // Try to parse the JSON response
    try {
      const profileData = JSON.parse(content) as LinkedInProfile
      return profileData
    } catch (parseError) {
      // If JSON parsing fails, try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const profileData = JSON.parse(jsonMatch[0]) as LinkedInProfile
        return profileData
      }
      throw new Error('Failed to parse AI response as JSON')
    }

  } catch (error) {
    console.error('Error processing LinkedIn screenshot with AIML API:', error)
    throw new Error('Failed to process LinkedIn screenshot with AI')
  }
}

// Helper function to convert file to base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Validate extracted profile data
export function validateProfileData(profile: LinkedInProfile): boolean {
  return !!(
    profile.personalInfo?.name &&
    profile.personalInfo?.title &&
    (profile.experience?.length > 0 || profile.education?.length > 0)
  )
}
