import OpenAI from 'openai'

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface LinkedInProfile {
  personalInfo: {
    name: string
    title: string
    location: string
    email?: string
    phone?: string
    linkedin?: string
  }
  summary: string
  experience: Array<{
    company: string
    position: string
    duration: string
    description: string
    location?: string
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    duration: string
    location?: string
  }>
  skills: string[]
  certifications?: Array<{
    name: string
    issuer: string
    date?: string
  }>
  languages?: Array<{
    language: string
    proficiency: string
  }>
}

export async function processLinkedInScreenshot(imageBase64: string): Promise<LinkedInProfile> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Analyze this LinkedIn profile screenshot and extract all professional information in a structured format. 

Please extract:
1. Personal Information (name, current title, location, contact info if visible)
2. Professional Summary/About section
3. Work Experience (company, position, duration, description, location)
4. Education (institution, degree, field of study, duration)
5. Skills (list all visible skills)
6. Certifications (if any)
7. Languages (if any)

Return the data in a clean, structured JSON format that can be used to generate a professional CV. Be thorough and accurate - this will be used to create someone's resume.

Focus on extracting text content accurately, maintaining professional language, and organizing information logically.`
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: 4000,
      temperature: 0.1, // Low temperature for consistent, accurate extraction
    })

    const content = response.choices[0]?.message?.content
    if (!content) {
      throw new Error('No response from OpenAI')
    }

    // Try to parse the JSON response
    try {
      const profileData = JSON.parse(content) as LinkedInProfile
      return profileData
    } catch (parseError) {
      // If JSON parsing fails, try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const profileData = JSON.parse(jsonMatch[0]) as LinkedInProfile
        return profileData
      }
      throw new Error('Failed to parse AI response as JSON')
    }

  } catch (error) {
    console.error('Error processing LinkedIn screenshot:', error)
    throw new Error('Failed to process LinkedIn screenshot with AI')
  }
}

// Helper function to convert file to base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Validate extracted profile data
export function validateProfileData(profile: LinkedInProfile): boolean {
  return !!(
    profile.personalInfo?.name &&
    profile.personalInfo?.title &&
    (profile.experience?.length > 0 || profile.education?.length > 0)
  )
}
