import OpenAI from 'openai'

// Initialize AIML API client (using OpenAI SDK with custom base URL)
const aimlClient = new OpenAI({
  apiKey: process.env.AIML_API_KEY,
  baseURL: 'https://api.aimlapi.com',
})

// AIML API provides access to GPT-4o and other advanced vision models

export interface LinkedInProfile {
  personalInfo: {
    name: string
    title: string
    location: string
    email?: string
    phone?: string
    linkedin?: string
  }
  summary: string
  experience: Array<{
    company: string
    position: string
    duration: string
    description: string
    location?: string
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    duration: string
    location?: string
  }>
  skills: string[]
  certifications?: Array<{
    name: string
    issuer: string
    date?: string
  }>
  languages?: Array<{
    language: string
    proficiency: string
  }>
}

export async function processLinkedInScreenshot(imageBase64: string): Promise<LinkedInProfile> {
  const maxRetries = 2
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Starting AIML API request (attempt ${attempt}/${maxRetries})...`)

      // Process the image directly

      const response = await aimlClient.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Please analyze this professional profile image and extract the text information visible in the image. Read all text carefully and return the information in JSON format.

Extract the following information exactly as it appears in the image:

{
  "personalInfo": {
    "name": "Full name visible in the image",
    "title": "Professional title/headline shown",
    "location": "Location information shown",
    "email": "Email address if visible",
    "phone": "Phone number if visible",
    "linkedin": "Profile URL if visible"
  },
  "summary": "About/summary section text if present",
  "experience": [
    {
      "company": "Company name",
      "position": "Job title",
      "duration": "Employment dates",
      "description": "Job description text",
      "location": "Work location"
    }
  ],
  "education": [
    {
      "institution": "School/university name",
      "degree": "Degree type",
      "field": "Field of study",
      "duration": "Years or graduation date",
      "location": "School location"
    }
  ],
  "skills": ["List of skills shown"],
  "certifications": [],
  "languages": []
}

Return only the JSON object with the actual text from the image. If information is not visible, use empty strings or arrays.`
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: 6000,
      temperature: 0.05, // Very low temperature for maximum accuracy
      top_p: 0.95, // Focus on most likely tokens for accuracy
    })

    console.log('AIML API response received')
    console.log('Response object:', JSON.stringify(response, null, 2))

    const content = response.choices[0]?.message?.content
    console.log('Response content length:', content?.length || 0)
    console.log('Response content preview:', content?.substring(0, 200))
    if (!content) {
      throw new Error('No response from AIML API')
    }

    console.log('Raw AI response:', content.substring(0, 500) + '...')

    // Try to parse the JSON response
    try {
      const rawProfileData = JSON.parse(content) as LinkedInProfile
      console.log('Successfully parsed JSON response')

      // Clean and enhance the extracted data
      const cleanedProfileData = cleanProfileData(rawProfileData)
      console.log('Data cleaning completed')

      return cleanedProfileData
    } catch (parseError) {
      console.log('Initial JSON parse failed, trying to extract JSON...')

      // If JSON parsing fails, try to extract JSON from the response
      // Look for JSON between ```json and ``` or just between { and }
      let jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
      if (!jsonMatch) {
        jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/)
      }
      if (!jsonMatch) {
        jsonMatch = content.match(/(\{[\s\S]*\})/)
      }

      if (jsonMatch) {
        try {
          const cleanedJson = jsonMatch[1] || jsonMatch[0]
          console.log('Extracted JSON:', cleanedJson.substring(0, 200) + '...')
          const rawProfileData = JSON.parse(cleanedJson) as LinkedInProfile
          console.log('Successfully parsed extracted JSON')

          // Clean and enhance the extracted data
          const cleanedProfileData = cleanProfileData(rawProfileData)
          console.log('Data cleaning completed')

          return cleanedProfileData
        } catch (secondParseError) {
          console.error('Failed to parse extracted JSON:', secondParseError)
        }
      }

      console.error('Raw response that failed to parse:', content)
      throw new Error('Failed to parse AI response as JSON')
    }

    } catch (error) {
      console.error(`Attempt ${attempt} failed:`, error)
      lastError = error instanceof Error ? error : new Error('Unknown error')

      if (attempt < maxRetries) {
        console.log(`Retrying in 2 seconds...`)
        await new Promise(resolve => setTimeout(resolve, 2000))
        continue
      }
    }
  }

  // If all attempts failed, throw the last error
  if (lastError) {
    console.error('All attempts failed. Error processing LinkedIn screenshot with AIML API:', lastError)

    // Provide more detailed error information
    if (lastError instanceof Error) {
      console.error('Error message:', lastError.message)
      console.error('Error stack:', lastError.stack)
    }

    // Check if it's an API-related error
    if (lastError && typeof lastError === 'object' && 'response' in lastError) {
      console.error('API response error:', lastError)
    }

    throw new Error(`Failed to process LinkedIn screenshot with AI after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : 'Unknown error'}`)
  }

  // This should never be reached, but TypeScript requires it
  throw new Error('Unexpected error in processLinkedInScreenshot')
}

// Helper function to convert file to base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Validate extracted profile data
export function validateProfileData(profile: LinkedInProfile): boolean {
  return !!(
    profile.personalInfo?.name &&
    profile.personalInfo?.title &&
    (profile.experience?.length > 0 || profile.education?.length > 0)
  )
}

// Clean and enhance extracted profile data
export function cleanProfileData(profile: LinkedInProfile): LinkedInProfile {
  return {
    personalInfo: {
      name: cleanText(profile.personalInfo?.name || ''),
      title: cleanText(profile.personalInfo?.title || ''),
      location: cleanText(profile.personalInfo?.location || ''),
      email: cleanEmail(profile.personalInfo?.email || ''),
      phone: cleanPhone(profile.personalInfo?.phone || ''),
      linkedin: cleanUrl(profile.personalInfo?.linkedin || '')
    },
    summary: cleanText(profile.summary || ''),
    experience: (profile.experience || []).map(exp => ({
      company: cleanText(exp.company || ''),
      position: cleanText(exp.position || ''),
      duration: cleanDuration(exp.duration || ''),
      description: cleanText(exp.description || ''),
      location: cleanText(exp.location || '')
    })),
    education: (profile.education || []).map(edu => ({
      institution: cleanText(edu.institution || ''),
      degree: cleanText(edu.degree || ''),
      field: cleanText(edu.field || ''),
      duration: cleanDuration(edu.duration || ''),
      location: cleanText(edu.location || '')
    })),
    skills: (profile.skills || []).map(skill => cleanText(skill)).filter(skill => skill.length > 0),
    certifications: (profile.certifications || []).map(cert => ({
      name: cleanText(cert.name || ''),
      issuer: cleanText(cert.issuer || ''),
      date: cleanText(cert.date || '')
    })),
    languages: (profile.languages || []).map(lang => ({
      language: cleanText(lang.language || ''),
      proficiency: cleanText(lang.proficiency || '')
    }))
  }
}

// Helper functions for data cleaning
function cleanText(text: string): string {
  return text.trim().replace(/\s+/g, ' ').replace(/[""]/g, '"').replace(/['']/g, "'")
}

function cleanEmail(email: string): string {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.trim()) ? email.trim().toLowerCase() : ''
}

function cleanPhone(phone: string): string {
  const cleaned = phone.replace(/[^\d+\-\(\)\s]/g, '').trim()
  return cleaned.length >= 10 ? cleaned : ''
}

function cleanUrl(url: string): string {
  if (!url) return ''
  if (url.startsWith('http')) return url
  if (url.includes('linkedin.com')) return `https://${url}`
  return url
}

function cleanDuration(duration: string): string {
  return duration.trim().replace(/\s+/g, ' ')
}
