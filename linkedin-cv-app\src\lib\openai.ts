import OpenAI from 'openai'

// Initialize AIML API client (using OpenAI SDK with custom base URL)
const aimlClient = new OpenAI({
  apiKey: process.env.AIML_API_KEY,
  baseURL: 'https://api.aimlapi.com',
})

// AIML API provides access to GPT-4o and other advanced vision models

export interface LinkedInProfile {
  personalInfo: {
    name: string
    title: string
    location: string
    email?: string
    phone?: string
    linkedin?: string
  }
  summary: string
  experience: Array<{
    company: string
    position: string
    duration: string
    description: string
    location?: string
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    duration: string
    location?: string
  }>
  skills: string[]
  certifications?: Array<{
    name: string
    issuer: string
    date?: string
  }>
  languages?: Array<{
    language: string
    proficiency: string
  }>
}

export async function processLinkedInScreenshot(imageBase64: string): Promise<LinkedInProfile> {
  try {
    console.log('Starting AIML API request...')

    // First, test with a simple vision model that's more likely to work
    const response = await aimlClient.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Analyze this LinkedIn profile screenshot and extract the professional information. Return ONLY a JSON object with this exact structure:

{
  "personalInfo": {
    "name": "Full name from profile",
    "title": "Current job title",
    "location": "Location shown",
    "email": "",
    "phone": "",
    "linkedin": ""
  },
  "summary": "About section text",
  "experience": [
    {
      "company": "Company name",
      "position": "Job title",
      "duration": "Date range",
      "description": "Job description",
      "location": ""
    }
  ],
  "education": [
    {
      "institution": "School name",
      "degree": "Degree type",
      "field": "Field of study",
      "duration": "Years",
      "location": ""
    }
  ],
  "skills": ["skill1", "skill2", "skill3"],
  "certifications": [],
  "languages": []
}

IMPORTANT: Return ONLY the JSON object, no explanations, no markdown formatting.`
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`,
                detail: "low"
              }
            }
          ]
        }
      ],
      max_tokens: 4000,
      temperature: 0.1, // Low temperature for consistent, accurate extraction
      top_p: 0.9, // Focus on most likely tokens for accuracy
    })

    console.log('AIML API response received')
    console.log('Response object:', JSON.stringify(response, null, 2))

    const content = response.choices[0]?.message?.content
    console.log('Response content length:', content?.length || 0)
    console.log('Response content preview:', content?.substring(0, 200))
    if (!content) {
      throw new Error('No response from AIML API')
    }

    console.log('Raw AI response:', content.substring(0, 500) + '...')

    // Try to parse the JSON response
    try {
      const profileData = JSON.parse(content) as LinkedInProfile
      console.log('Successfully parsed JSON response')
      return profileData
    } catch (parseError) {
      console.log('Initial JSON parse failed, trying to extract JSON...')

      // If JSON parsing fails, try to extract JSON from the response
      // Look for JSON between ```json and ``` or just between { and }
      let jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
      if (!jsonMatch) {
        jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/)
      }
      if (!jsonMatch) {
        jsonMatch = content.match(/(\{[\s\S]*\})/)
      }

      if (jsonMatch) {
        try {
          const cleanedJson = jsonMatch[1] || jsonMatch[0]
          console.log('Extracted JSON:', cleanedJson.substring(0, 200) + '...')
          const profileData = JSON.parse(cleanedJson) as LinkedInProfile
          console.log('Successfully parsed extracted JSON')
          return profileData
        } catch (secondParseError) {
          console.error('Failed to parse extracted JSON:', secondParseError)
        }
      }

      console.error('Raw response that failed to parse:', content)
      throw new Error('Failed to parse AI response as JSON')
    }

  } catch (error) {
    console.error('Error processing LinkedIn screenshot with AIML API:', error)

    // Provide more detailed error information
    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }

    // Check if it's an API-related error
    if (error && typeof error === 'object' && 'response' in error) {
      console.error('API response error:', error)
    }

    throw new Error(`Failed to process LinkedIn screenshot with AI: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Helper function to convert file to base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Validate extracted profile data
export function validateProfileData(profile: LinkedInProfile): boolean {
  return !!(
    profile.personalInfo?.name &&
    profile.personalInfo?.title &&
    (profile.experience?.length > 0 || profile.education?.length > 0)
  )
}
