// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.api.expr.v1beta1;

option cc_enable_arenas = true;
option go_package = "google.golang.org/genproto/googleapis/api/expr/v1beta1;expr";
option java_multiple_files = true;
option java_outer_classname = "SourceProto";
option java_package = "com.google.api.expr.v1beta1";

// Source information collected at parse time.
message SourceInfo {
  // The location name. All position information attached to an expression is
  // relative to this location.
  //
  // The location could be a file, UI element, or similar. For example,
  // `acme/app/AnvilPolicy.cel`.
  string location = 2;

  // Monotonically increasing list of character offsets where newlines appear.
  //
  // The line number of a given position is the index `i` where for a given
  // `id` the `line_offsets[i] < id_positions[id] < line_offsets[i+1]`. The
  // column may be derivd from `id_positions[id] - line_offsets[i]`.
  repeated int32 line_offsets = 3;

  // A map from the parse node id (e.g. `Expr.id`) to the character offset
  // within source.
  map<int32, int32> positions = 4;
}

// A specific position in source.
message SourcePosition {
  // The soucre location name (e.g. file name).
  string location = 1;

  // The character offset.
  int32 offset = 2;

  // The 1-based index of the starting line in the source text
  // where the issue occurs, or 0 if unknown.
  int32 line = 3;

  // The 0-based index of the starting position within the line of source text
  // where the issue occurs.  Only meaningful if line is nonzer..
  int32 column = 4;
}
