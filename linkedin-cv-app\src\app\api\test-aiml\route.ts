import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

export async function GET() {
  try {
    // Check if AIML API key is configured
    if (!process.env.AIML_API_KEY) {
      return NextResponse.json({
        status: 'error',
        message: 'AIML API key not configured',
        instructions: 'Add AIML_API_KEY to your .env.local file'
      })
    }

    // Initialize AIML API client
    const aimlClient = new OpenAI({
      apiKey: process.env.AIML_API_KEY,
      baseURL: 'https://api.aimlapi.com',
    })

    // Test the connection with a simple request
    const response = await aimlClient.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: "Hello! Please respond with 'AIML API connection successful' to confirm the integration is working."
        }
      ],
      max_tokens: 50,
      temperature: 0.1,
    })

    const content = response.choices[0]?.message?.content

    return NextResponse.json({
      status: 'success',
      message: 'AIML API connection successful',
      response: content,
      model: 'gpt-4o',
      baseURL: 'https://api.aimlapi.com'
    })

  } catch (error) {
    console.error('AIML API test error:', error)
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to connect to AIML API',
      error: error instanceof Error ? error.message : 'Unknown error',
      instructions: 'Check your AIML_API_KEY and internet connection'
    }, { status: 500 })
  }
}
