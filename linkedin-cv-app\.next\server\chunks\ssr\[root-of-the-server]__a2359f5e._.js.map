{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Neobrutalism color utilities\nexport const neoColors = {\n  black: '#000000',\n  white: '#ffffff',\n  yellow: '#ffff00',\n  pink: '#ff00ff',\n  cyan: '#00ffff',\n  green: '#00ff00',\n  red: '#ff0000',\n  blue: '#0000ff',\n  orange: '#ff8000',\n  purple: '#8000ff',\n} as const\n\nexport type NeoColor = keyof typeof neoColors\n\n// Generate random neobrutalism color\nexport function getRandomNeoColor(): NeoColor {\n  const colors = Object.keys(neoColors) as NeoColor[]\n  return colors[Math.floor(Math.random() * colors.length)]\n}\n\n// File validation utilities\nexport function validateImageFile(file: File): boolean {\n  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']\n  const maxSize = 10 * 1024 * 1024 // 10MB\n  \n  return validTypes.includes(file.type) && file.size <= maxSize\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAKO,SAAS;IACd,MAAM,SAAS,OAAO,IAAI,CAAC;IAC3B,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAGO,SAAS,kBAAkB,IAAU;IAC1C,MAAM,aAAa;QAAC;QAAc;QAAa;QAAa;KAAa;IACzE,MAAM,UAAU,KAAK,OAAO,KAAK,OAAO;;IAExC,OAAO,WAAW,QAAQ,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI;AACxD;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn, type NeoColor, neoColors } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  color?: NeoColor\n  children: React.ReactNode\n}\n\nexport function Button({ \n  variant = 'primary', \n  size = 'md', \n  color,\n  className, \n  children, \n  ...props \n}: ButtonProps) {\n  const getVariantStyles = () => {\n    switch (variant) {\n      case 'primary':\n        return 'bg-white text-black hover:bg-yellow-400'\n      case 'secondary':\n        return 'bg-black text-white hover:bg-gray-800'\n      case 'danger':\n        return 'bg-red-500 text-white hover:bg-red-600'\n      default:\n        return 'bg-white text-black hover:bg-yellow-400'\n    }\n  }\n\n  const getSizeStyles = () => {\n    switch (size) {\n      case 'sm':\n        return 'px-3 py-2 text-sm sm:px-4'\n      case 'md':\n        return 'px-4 py-3 text-sm sm:px-6 sm:text-base'\n      case 'lg':\n        return 'px-6 py-3 text-base sm:px-8 sm:py-4 sm:text-lg'\n      default:\n        return 'px-4 py-3 text-sm sm:px-6 sm:text-base'\n    }\n  }\n\n  const colorStyle = color ? { backgroundColor: neoColors[color] } : {}\n\n  return (\n    <button\n      className={cn(\n        'neo-button',\n        getVariantStyles(),\n        getSizeStyles(),\n        className\n      )}\n      style={colorStyle}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AASO,SAAS,OAAO,EACrB,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,KAAK,EACL,SAAS,EACT,QAAQ,EACR,GAAG,OACS;IACZ,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,QAAQ;QAAE,iBAAiB,mHAAA,CAAA,YAAS,CAAC,MAAM;IAAC,IAAI,CAAC;IAEpE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cACA,oBACA,iBACA;QAEF,OAAO;QACN,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/components/ui/FileUpload.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback, useRef } from 'react'\nimport { Upload, X, Image as ImageIcon, AlertCircle } from 'lucide-react'\nimport { cn, validateImageFile, formatFileSize } from '@/lib/utils'\nimport { Button } from './Button'\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void\n  onFileRemove: () => void\n  selectedFile?: File | null\n  className?: string\n}\n\nexport function FileUpload({ onFileSelect, onFileRemove, selectedFile, className }: FileUploadProps) {\n  const [isDragOver, setIsDragOver] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [preview, setPreview] = useState<string | null>(null)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(true)\n  }, [])\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(false)\n  }, [])\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(false)\n    \n    const files = Array.from(e.dataTransfer.files)\n    if (files.length > 0) {\n      handleFileSelection(files[0])\n    }\n  }, [])\n\n  const handleFileSelection = (file: File) => {\n    setError(null)\n    \n    if (!validateImageFile(file)) {\n      setError('Please upload a valid image file (JPEG, PNG, WebP) under 10MB')\n      return\n    }\n\n    // Create preview\n    const reader = new FileReader()\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string)\n    }\n    reader.readAsDataURL(file)\n\n    onFileSelect(file)\n  }\n\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files\n    if (files && files.length > 0) {\n      handleFileSelection(files[0])\n    }\n  }\n\n  const handleRemoveFile = () => {\n    setPreview(null)\n    setError(null)\n    onFileRemove()\n    if (fileInputRef.current) {\n      fileInputRef.current.value = ''\n    }\n  }\n\n  const openFileDialog = () => {\n    fileInputRef.current?.click()\n  }\n\n  return (\n    <div className={cn('w-full', className)}>\n      {!selectedFile ? (\n        <div\n          className={cn(\n            'neo-border neo-shadow-lg p-8 bg-white transition-all duration-200 cursor-pointer',\n            isDragOver && 'bg-yellow-100 transform translate-x-1 translate-y-1',\n            error && 'border-red-500'\n          )}\n          onDragOver={handleDragOver}\n          onDragLeave={handleDragLeave}\n          onDrop={handleDrop}\n          onClick={openFileDialog}\n        >\n          <div className=\"text-center\">\n            <div className=\"flex justify-center mb-6\">\n              <div className=\"neo-border neo-shadow p-6 bg-cyan-400\">\n                <Upload size={48} className=\"text-black\" />\n              </div>\n            </div>\n            \n            <h3 className=\"text-2xl font-bold uppercase tracking-wide mb-4\">\n              Upload LinkedIn Screenshot\n            </h3>\n            \n            <p className=\"text-lg font-medium mb-6\">\n              Drag & drop your LinkedIn profile screenshot here, or click to browse\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-6\">\n              <Button size=\"lg\" color=\"pink\">\n                Choose File\n              </Button>\n              <span className=\"font-bold uppercase text-sm\">\n                or drag & drop\n              </span>\n            </div>\n            \n            <div className=\"text-sm font-medium text-gray-600\">\n              <p>Supported formats: JPEG, PNG, WebP</p>\n              <p>Maximum file size: 10MB</p>\n            </div>\n          </div>\n          \n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={handleFileInputChange}\n            className=\"hidden\"\n          />\n        </div>\n      ) : (\n        <div className=\"neo-border neo-shadow-lg bg-white p-6\">\n          <div className=\"flex items-start justify-between mb-4\">\n            <h3 className=\"text-xl font-bold uppercase tracking-wide\">\n              LinkedIn Screenshot Preview\n            </h3>\n            <Button\n              size=\"sm\"\n              variant=\"danger\"\n              onClick={handleRemoveFile}\n              className=\"flex items-center gap-2\"\n            >\n              <X size={16} />\n              Remove\n            </Button>\n          </div>\n          \n          {preview && (\n            <div className=\"neo-border neo-shadow mb-4 bg-gray-50 p-4\">\n              <img\n                src={preview}\n                alt=\"LinkedIn screenshot preview\"\n                className=\"max-w-full h-auto max-h-96 mx-auto neo-border\"\n              />\n            </div>\n          )}\n          \n          <div className=\"flex items-center gap-4 p-4 neo-border bg-gray-50\">\n            <ImageIcon size={24} className=\"text-gray-600\" />\n            <div className=\"flex-1\">\n              <p className=\"font-bold text-sm uppercase\">\n                {selectedFile.name}\n              </p>\n              <p className=\"text-sm font-medium text-gray-600\">\n                {formatFileSize(selectedFile.size)}\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      {error && (\n        <div className=\"mt-4 neo-border bg-red-100 border-red-500 p-4 flex items-center gap-3\">\n          <AlertCircle size={20} className=\"text-red-500\" />\n          <p className=\"font-medium text-red-700\">\n            {error}\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAcO,SAAS,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAmB;IACjG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,oBAAoB,KAAK,CAAC,EAAE;QAC9B;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,SAAS;QAET,IAAI,CAAC,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;YAC5B,SAAS;YACT;QACF;QAEA,iBAAiB;QACjB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,WAAW,EAAE,MAAM,EAAE;QACvB;QACA,OAAO,aAAa,CAAC;QAErB,aAAa;IACf;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,oBAAoB,KAAK,CAAC,EAAE;QAC9B;IACF;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QACT;QACA,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,aAAa,OAAO,EAAE;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,CAAC,6BACA,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA,cAAc,uDACd,SAAS;gBAEX,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,SAAS;;kCAET,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;;;;;0CAIhC,8OAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAIhE,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CAIxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,OAAM;kDAAO;;;;;;kDAG/B,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;0CAKhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;kCAIP,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;;;;;;qCAId,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;;kDAEV,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;oCAAM;;;;;;;;;;;;;oBAKlB,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK;4BACL,KAAI;4BACJ,WAAU;;;;;;;;;;;kCAKhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAS;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,aAAa,IAAI;;;;;;kDAEpB,8OAAC;wCAAE,WAAU;kDACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;YAO1C,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCACjC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn, type NeoColor, neoColors } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  color?: NeoColor\n  variant?: 'default' | 'elevated' | 'flat'\n}\n\nexport function Card({ children, className, color, variant = 'default' }: CardProps) {\n  const getVariantStyles = () => {\n    switch (variant) {\n      case 'elevated':\n        return 'neo-shadow-xl'\n      case 'flat':\n        return 'shadow-none'\n      default:\n        return 'neo-shadow-lg'\n    }\n  }\n\n  const colorStyle = color ? { backgroundColor: neoColors[color] } : {}\n\n  return (\n    <div\n      className={cn(\n        'neo-card',\n        getVariantStyles(),\n        className\n      )}\n      style={colorStyle}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface CardHeaderProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function CardHeader({ children, className }: CardHeaderProps) {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface CardTitleProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function CardTitle({ children, className }: CardTitleProps) {\n  return (\n    <h3 className={cn('text-2xl font-bold uppercase tracking-wide', className)}>\n      {children}\n    </h3>\n  )\n}\n\ninterface CardContentProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function CardContent({ children, className }: CardContentProps) {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AASO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,SAAS,EAAa;IACjF,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,QAAQ;QAAE,iBAAiB,mHAAA,CAAA,YAAS,CAAC,MAAM;IAAC,IAAI,CAAC;IAEpE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,YACA,oBACA;QAEF,OAAO;kBAEN;;;;;;AAGP;AAOO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAmB;IACjE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;AAOO,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAkB;IAC/D,qBACE,8OAAC;QAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;kBAC7D;;;;;;AAGP;AAOO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAoB;IACnE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/upload/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { FileUpload } from '@/components/ui/FileUpload'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card'\nimport { ArrowLeft, ArrowRight, Sparkles, AlertCircle } from 'lucide-react'\nimport { useLinkedInProcessor } from '@/hooks/useLinkedInProcessor'\n\nexport default function UploadPage() {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null)\n  const [isProcessing, setIsProcessing] = useState(false)\n  const router = useRouter()\n\n  const handleFileSelect = (file: File) => {\n    setSelectedFile(file)\n  }\n\n  const handleFileRemove = () => {\n    setSelectedFile(null)\n  }\n\n  const handleProcessFile = async () => {\n    if (!selectedFile) return\n\n    setIsProcessing(true)\n    \n    // TODO: Implement AI processing\n    // For now, simulate processing time\n    setTimeout(() => {\n      setIsProcessing(false)\n      // Navigate to results page\n      router.push('/results')\n    }, 3000)\n  }\n\n  const goBack = () => {\n    router.push('/')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Header */}\n      <header className=\"neo-border-thick border-t-0 border-l-0 border-r-0 bg-yellow-400 p-6\">\n        <div className=\"max-w-6xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <Button variant=\"secondary\" size=\"sm\" onClick={goBack}>\n              <ArrowLeft size={16} className=\"mr-2\" />\n              Back\n            </Button>\n            <h1 className=\"text-3xl font-bold uppercase tracking-wide\">\n              Upload LinkedIn Screenshot\n            </h1>\n          </div>\n          <div className=\"text-sm font-bold uppercase\">\n            Step 1 of 4\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"py-12 px-6\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Instructions */}\n          <Card className=\"mb-8\" color=\"cyan\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3\">\n                <Sparkles size={24} />\n                How to Get the Best Results\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h4 className=\"font-bold uppercase mb-2\">✅ Do Include:</h4>\n                  <ul className=\"space-y-1 font-medium\">\n                    <li>• Your full profile summary</li>\n                    <li>• Work experience section</li>\n                    <li>• Education details</li>\n                    <li>• Skills section</li>\n                    <li>• Clear, readable text</li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-bold uppercase mb-2\">❌ Avoid:</h4>\n                  <ul className=\"space-y-1 font-medium\">\n                    <li>• Blurry or low-quality images</li>\n                    <li>• Cropped important sections</li>\n                    <li>• Dark mode (harder to read)</li>\n                    <li>• Multiple profiles in one image</li>\n                    <li>• Private/confidential information</li>\n                  </ul>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* File Upload */}\n          <Card className=\"mb-8\">\n            <CardContent>\n              <FileUpload\n                onFileSelect={handleFileSelect}\n                onFileRemove={handleFileRemove}\n                selectedFile={selectedFile}\n              />\n            </CardContent>\n          </Card>\n\n          {/* Action Buttons */}\n          {selectedFile && (\n            <div className=\"flex justify-center\">\n              <Button\n                size=\"lg\"\n                color=\"green\"\n                onClick={handleProcessFile}\n                disabled={isProcessing}\n                className=\"flex items-center gap-3\"\n              >\n                {isProcessing ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-2 border-black border-t-transparent\"></div>\n                    Processing with AI...\n                  </>\n                ) : (\n                  <>\n                    <Sparkles size={20} />\n                    Process with AI\n                    <ArrowRight size={20} />\n                  </>\n                )}\n              </Button>\n            </div>\n          )}\n\n          {/* Processing Status */}\n          {isProcessing && (\n            <Card className=\"mt-8\" color=\"yellow\">\n              <CardContent>\n                <div className=\"text-center\">\n                  <div className=\"flex justify-center mb-4\">\n                    <div className=\"neo-border neo-shadow p-4 bg-white\">\n                      <div className=\"animate-spin rounded-full h-8 w-8 border-4 border-black border-t-transparent\"></div>\n                    </div>\n                  </div>\n                  <h3 className=\"text-xl font-bold uppercase mb-2\">\n                    AI is Processing Your LinkedIn Profile\n                  </h3>\n                  <p className=\"font-medium\">\n                    Our AI is extracting your professional information and creating your CV...\n                  </p>\n                  <div className=\"mt-4 neo-border bg-white p-2\">\n                    <div className=\"h-2 bg-gray-200 neo-border\">\n                      <div className=\"h-full bg-green-400 animate-pulse\" style={{ width: '60%' }}></div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;QAEnB,gBAAgB;QAEhB,gCAAgC;QAChC,oCAAoC;QACpC,WAAW;YACT,gBAAgB;YAChB,2BAA2B;YAC3B,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,MAAM,SAAS;QACb,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,MAAK;oCAAK,SAAS;;sDAC7C,8OAAC,gNAAA,CAAA,YAAS;4CAAC,MAAM;4CAAI,WAAU;;;;;;wCAAS;;;;;;;8CAG1C,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;;;;;;;sCAI7D,8OAAC;4BAAI,WAAU;sCAA8B;;;;;;;;;;;;;;;;;0BAOjD,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;4BAAO,OAAM;;8CAC3B,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;8CAI1B,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2B;;;;;;kEACzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2B;;;;;;kEACzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,sIAAA,CAAA,aAAU;oCACT,cAAc;oCACd,cAAc;oCACd,cAAc;;;;;;;;;;;;;;;;wBAMnB,8BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,OAAM;gCACN,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,6BACC;;sDACE,8OAAC;4CAAI,WAAU;;;;;;wCAAqF;;iEAItG;;sDACE,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;wCAAM;sDAEtB,8OAAC,kNAAA,CAAA,aAAU;4CAAC,MAAM;;;;;;;;;;;;;;;;;;wBAQ3B,8BACC,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;4BAAO,OAAM;sCAC3B,cAAA,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;sDAGnB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,8OAAC;4CAAE,WAAU;sDAAc;;;;;;sDAG3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAoC,OAAO;wDAAE,OAAO;oDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/F", "debugId": null}}]}