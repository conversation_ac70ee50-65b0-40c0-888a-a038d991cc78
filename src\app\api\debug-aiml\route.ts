import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

export async function GET() {
  try {
    if (!process.env.AIML_API_KEY) {
      return NextResponse.json({ error: 'No API key' })
    }

    const aimlClient = new OpenAI({
      apiKey: process.env.AIML_API_KEY,
      baseURL: 'https://api.aimlapi.com',
    })

    console.log('Testing AIML API with different models...')

    // Test 1: Simple text model
    try {
      const textResponse = await aimlClient.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [{ role: "user", content: "Say hello" }],
        max_tokens: 10
      })
      console.log('Text model works:', textResponse.choices[0]?.message?.content)
    } catch (textError) {
      console.log('Text model failed:', textError)
    }

    // Test 2: Try different vision model names
    const visionModels = [
      "gpt-4-vision-preview",
      "gpt-4o",
      "gpt-4o-2024-08-06",
      "gpt-4-turbo",
      "gpt-4-turbo-vision"
    ]

    for (const model of visionModels) {
      try {
        console.log(`Testing vision model: ${model}`)
        const visionResponse = await aimlClient.chat.completions.create({
          model: model,
          messages: [{
            role: "user",
            content: [
              { type: "text", text: "What do you see?" },
              { 
                type: "image_url", 
                image_url: { 
                  url: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
                  detail: "low"
                }
              }
            ]
          }],
          max_tokens: 20
        })
        console.log(`${model} works:`, visionResponse.choices[0]?.message?.content)
        
        return NextResponse.json({
          success: true,
          workingModel: model,
          response: visionResponse.choices[0]?.message?.content
        })
      } catch (visionError) {
        console.log(`${model} failed:`, visionError)
      }
    }

    return NextResponse.json({
      error: 'No working vision models found',
      message: 'All vision models failed'
    })

  } catch (error) {
    console.error('Debug test failed:', error)
    return NextResponse.json({
      error: 'Debug test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
