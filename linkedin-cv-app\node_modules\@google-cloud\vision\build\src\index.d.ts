import * as v1 from './v1';
import * as v1p1beta1 from './v1p1beta1';
import * as v1p2beta1 from './v1p2beta1';
import * as v1p3beta1 from './v1p3beta1';
import * as v1p4beta1 from './v1p4beta1';
declare const ImageAnnotatorClient: typeof v1.ImageAnnotatorClient;
type ImageAnnotatorClient = v1.ImageAnnotatorClient;
declare const ProductSearchClient: typeof v1.ProductSearchClient;
type ProductSearchClient = v1.ProductSearchClient;
export { v1, v1p1beta1, v1p2beta1, v1p3beta1, v1p4beta1, ImageAnnotatorClient, ProductSearchClient, };
declare const _default: {
    v1: typeof v1;
    v1p1beta1: typeof v1p1beta1;
    v1p2beta1: typeof v1p2beta1;
    v1p3beta1: typeof v1p3beta1;
    v1p4beta1: typeof v1p4beta1;
    ImageAnnotatorClient: typeof v1.ImageAnnotatorClient;
    ProductSearchClient: typeof v1.ProductSearchClient;
};
export default _default;
import * as protos from '../protos/protos';
export { protos };
