import jsPDF from 'jspdf'
import { LinkedInProfile } from './openai'

interface CVTemplate {
  id: string
  name: string
  primaryColor: string
  secondaryColor: string
  fontFamily: string
}

const templates: Record<string, CVTemplate> = {
  modern: {
    id: 'modern',
    name: 'Modern',
    primaryColor: '#2563eb',
    secondaryColor: '#f3f4f6',
    fontFamily: 'helvetica'
  },
  classic: {
    id: 'classic',
    name: 'Classic',
    primaryColor: '#374151',
    secondaryColor: '#f9fafb',
    fontFamily: 'times'
  },
  creative: {
    id: 'creative',
    name: 'Creative',
    primaryColor: '#7c3aed',
    secondaryColor: '#faf5ff',
    fontFamily: 'helvetica'
  },
  minimal: {
    id: 'minimal',
    name: 'Minimal',
    primaryColor: '#059669',
    secondaryColor: '#f0fdf4',
    fontFamily: 'helvetica'
  }
}

export async function generatePDF(profile: LinkedInProfile, templateId: string): Promise<Blob> {
  const template = templates[templateId] || templates.modern
  const pdf = new jsPDF('p', 'mm', 'a4')
  
  // Page dimensions
  const pageWidth = pdf.internal.pageSize.getWidth()
  const pageHeight = pdf.internal.pageSize.getHeight()
  const margin = 20
  const contentWidth = pageWidth - (margin * 2)
  
  let currentY = margin

  // Helper function to add text with word wrapping
  const addText = (text: string, x: number, y: number, maxWidth: number, fontSize: number = 10, isBold: boolean = false) => {
    pdf.setFontSize(fontSize)
    pdf.setFont(template.fontFamily, isBold ? 'bold' : 'normal')
    
    const lines = pdf.splitTextToSize(text, maxWidth)
    pdf.text(lines, x, y)
    return y + (lines.length * fontSize * 0.4)
  }

  // Helper function to add section header
  const addSectionHeader = (title: string, y: number) => {
    pdf.setFillColor(template.primaryColor)
    pdf.rect(margin, y - 5, contentWidth, 8, 'F')
    pdf.setTextColor(255, 255, 255)
    pdf.setFontSize(12)
    pdf.setFont(template.fontFamily, 'bold')
    pdf.text(title.toUpperCase(), margin + 2, y)
    pdf.setTextColor(0, 0, 0)
    return y + 10
  }

  // Header Section
  pdf.setFillColor(template.secondaryColor)
  pdf.rect(0, 0, pageWidth, 60, 'F')
  
  // Name
  pdf.setTextColor(template.primaryColor)
  pdf.setFontSize(24)
  pdf.setFont(template.fontFamily, 'bold')
  pdf.text(profile.personalInfo.name, margin, 25)
  
  // Title
  pdf.setTextColor(0, 0, 0)
  pdf.setFontSize(14)
  pdf.setFont(template.fontFamily, 'normal')
  pdf.text(profile.personalInfo.title, margin, 35)
  
  // Contact Info
  pdf.setFontSize(10)
  let contactY = 45
  if (profile.personalInfo.location) {
    pdf.text(`📍 ${profile.personalInfo.location}`, margin, contactY)
    contactY += 5
  }
  if (profile.personalInfo.email) {
    pdf.text(`✉️ ${profile.personalInfo.email}`, margin, contactY)
    contactY += 5
  }
  if (profile.personalInfo.phone) {
    pdf.text(`📞 ${profile.personalInfo.phone}`, margin, contactY)
    contactY += 5
  }
  if (profile.personalInfo.linkedin) {
    pdf.text(`🔗 ${profile.personalInfo.linkedin}`, margin, contactY)
  }

  currentY = 70

  // Professional Summary
  if (profile.summary) {
    currentY = addSectionHeader('Professional Summary', currentY)
    currentY = addText(profile.summary, margin, currentY, contentWidth, 10) + 5
  }

  // Work Experience
  if (profile.experience.length > 0) {
    currentY = addSectionHeader('Work Experience', currentY)
    
    for (const exp of profile.experience) {
      // Check if we need a new page
      if (currentY > pageHeight - 40) {
        pdf.addPage()
        currentY = margin
      }
      
      // Job Title and Company
      pdf.setFontSize(12)
      pdf.setFont(template.fontFamily, 'bold')
      pdf.text(exp.position, margin, currentY)
      currentY += 6
      
      pdf.setFontSize(10)
      pdf.setFont(template.fontFamily, 'bold')
      pdf.setTextColor(template.primaryColor)
      pdf.text(exp.company, margin, currentY)
      pdf.setTextColor(0, 0, 0)
      
      // Duration and Location
      if (exp.duration || exp.location) {
        const rightText = `${exp.duration}${exp.duration && exp.location ? ' | ' : ''}${exp.location}`
        const textWidth = pdf.getTextWidth(rightText)
        pdf.text(rightText, pageWidth - margin - textWidth, currentY)
      }
      currentY += 8
      
      // Description
      if (exp.description) {
        pdf.setFont(template.fontFamily, 'normal')
        currentY = addText(exp.description, margin, currentY, contentWidth, 9) + 3
      }
      
      currentY += 5
    }
  }

  // Education
  if (profile.education.length > 0) {
    // Check if we need a new page
    if (currentY > pageHeight - 60) {
      pdf.addPage()
      currentY = margin
    }
    
    currentY = addSectionHeader('Education', currentY)
    
    for (const edu of profile.education) {
      pdf.setFontSize(11)
      pdf.setFont(template.fontFamily, 'bold')
      pdf.text(`${edu.degree}${edu.field ? ` in ${edu.field}` : ''}`, margin, currentY)
      currentY += 6
      
      pdf.setFontSize(10)
      pdf.setFont(template.fontFamily, 'normal')
      pdf.setTextColor(template.primaryColor)
      pdf.text(edu.institution, margin, currentY)
      pdf.setTextColor(0, 0, 0)
      
      if (edu.duration) {
        const textWidth = pdf.getTextWidth(edu.duration)
        pdf.text(edu.duration, pageWidth - margin - textWidth, currentY)
      }
      currentY += 8
    }
  }

  // Skills
  if (profile.skills.length > 0) {
    // Check if we need a new page
    if (currentY > pageHeight - 40) {
      pdf.addPage()
      currentY = margin
    }
    
    currentY = addSectionHeader('Skills', currentY)
    
    // Group skills into lines
    const skillsPerLine = 4
    const skillGroups = []
    for (let i = 0; i < profile.skills.length; i += skillsPerLine) {
      skillGroups.push(profile.skills.slice(i, i + skillsPerLine))
    }
    
    for (const group of skillGroups) {
      const skillsText = group.join(' • ')
      currentY = addText(skillsText, margin, currentY, contentWidth, 10) + 2
    }
  }

  // Certifications
  if (profile.certifications.length > 0) {
    // Check if we need a new page
    if (currentY > pageHeight - 40) {
      pdf.addPage()
      currentY = margin
    }
    
    currentY = addSectionHeader('Certifications', currentY)
    
    for (const cert of profile.certifications) {
      pdf.setFontSize(10)
      pdf.setFont(template.fontFamily, 'bold')
      pdf.text(cert.name, margin, currentY)
      currentY += 5
      
      if (cert.issuer) {
        pdf.setFont(template.fontFamily, 'normal')
        pdf.setTextColor(template.primaryColor)
        pdf.text(cert.issuer, margin, currentY)
        pdf.setTextColor(0, 0, 0)
        
        if (cert.date) {
          const textWidth = pdf.getTextWidth(cert.date)
          pdf.text(cert.date, pageWidth - margin - textWidth, currentY)
        }
        currentY += 6
      }
    }
  }

  // Languages
  if (profile.languages.length > 0) {
    // Check if we need a new page
    if (currentY > pageHeight - 30) {
      pdf.addPage()
      currentY = margin
    }
    
    currentY = addSectionHeader('Languages', currentY)
    
    const languagesText = profile.languages.map(lang => 
      `${lang.language}${lang.proficiency ? ` (${lang.proficiency})` : ''}`
    ).join(' • ')
    
    currentY = addText(languagesText, margin, currentY, contentWidth, 10)
  }

  return pdf.output('blob')
}

export function downloadPDF(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}
