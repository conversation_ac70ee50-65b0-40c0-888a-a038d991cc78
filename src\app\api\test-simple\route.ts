import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

export async function GET() {
  try {
    // Check if AIML API key is configured
    if (!process.env.AIML_API_KEY) {
      return NextResponse.json({
        status: 'error',
        message: 'AIML API key not configured'
      })
    }

    // Initialize AIML API client
    const aimlClient = new OpenAI({
      apiKey: process.env.AIML_API_KEY,
      baseURL: 'https://api.aimlapi.com',
    })

    console.log('Testing simple text completion...')

    // Test with a simple text model first
    const response = await aimlClient.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: "Return only this JSON object: {\"test\": \"success\", \"message\": \"AIML API working\"}"
        }
      ],
      max_tokens: 100,
      temperature: 0.1,
    })

    console.log('Simple test response:', response)

    const content = response.choices[0]?.message?.content

    return NextResponse.json({
      status: 'success',
      message: 'Simple AIML API test successful',
      response: content,
      model: 'gpt-3.5-turbo'
    })

  } catch (error) {
    console.error('Simple AIML API test error:', error)
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to connect to AIML API',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
