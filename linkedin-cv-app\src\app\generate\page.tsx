'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { LinkedInProfile } from '@/lib/openai'
import { generatePDF, downloadPDF } from '@/lib/pdf-generator'

export default function GeneratePage() {
  const router = useRouter()
  const [profile, setProfile] = useState<LinkedInProfile | null>(null)
  const [template, setTemplate] = useState<string>('')
  const [generating, setGenerating] = useState(false)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    // Get profile and template data
    const finalProfile = localStorage.getItem('finalProfile')
    const selectedTemplate = localStorage.getItem('selectedTemplate')
    
    if (finalProfile && selectedTemplate) {
      setProfile(JSON.parse(finalProfile))
      setTemplate(selectedTemplate)
      startGeneration()
    } else {
      router.push('/upload')
    }
  }, [router])

  const startGeneration = async () => {
    setGenerating(true)
    
    // Simulate PDF generation progress
    const intervals = [
      { progress: 20, message: 'Preparing CV data...' },
      { progress: 40, message: 'Applying template...' },
      { progress: 60, message: 'Formatting content...' },
      { progress: 80, message: 'Generating PDF...' },
      { progress: 100, message: 'Complete!' }
    ]

    for (const interval of intervals) {
      await new Promise(resolve => setTimeout(resolve, 800))
      setProgress(interval.progress)
    }

    // Generate and download actual PDF
    setTimeout(async () => {
      setGenerating(false)
      await generateAndDownloadPDF()
    }, 1000)
  }

  const generateAndDownloadPDF = async () => {
    if (!profile || !template) return

    try {
      const pdfBlob = await generatePDF(profile, template)
      const filename = `${profile.personalInfo.name.replace(/\s+/g, '_')}_CV.pdf`
      downloadPDF(pdfBlob, filename)
    } catch (error) {
      console.error('PDF generation failed:', error)
      alert('Failed to generate PDF. Please try again.')
    }
  }

  const createNewCV = () => {
    // Clear all stored data
    localStorage.removeItem('extractedProfile')
    localStorage.removeItem('editedProfile')
    localStorage.removeItem('finalProfile')
    localStorage.removeItem('selectedTemplate')
    router.push('/upload')
  }

  if (!profile || !template) {
    return (
      <div className="min-h-screen bg-yellow-300 flex items-center justify-center">
        <div className="text-2xl font-black">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-yellow-300 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white border-4 border-black p-6 mb-6 shadow-brutal">
          <h1 className="text-4xl font-black mb-2">GENERATING YOUR CV</h1>
          <p className="text-lg font-bold">Creating your professional CV with {template.toUpperCase()} template</p>
        </div>

        {generating ? (
          /* Generation Progress */
          <div className="bg-white border-4 border-black p-8 shadow-brutal text-center">
            <div className="mb-8">
              <div className="w-32 h-32 border-4 border-black mx-auto mb-4 relative overflow-hidden">
                <div 
                  className="bg-green-500 transition-all duration-500 ease-out"
                  style={{ 
                    height: `${progress}%`,
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0
                  }}
                ></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-2xl font-black">{progress}%</span>
                </div>
              </div>
              
              <h2 className="text-2xl font-black mb-4">CREATING YOUR CV...</h2>
              <p className="text-lg font-bold text-gray-700">
                {progress === 20 && 'Preparing CV data...'}
                {progress === 40 && 'Applying template...'}
                {progress === 60 && 'Formatting content...'}
                {progress === 80 && 'Generating PDF...'}
                {progress === 100 && 'Complete!'}
              </p>
            </div>

            {/* CV Preview */}
            <div className="border-4 border-black bg-white p-6 max-w-md mx-auto">
              <h3 className="text-xl font-black mb-2">{profile.personalInfo.name}</h3>
              <p className="font-bold text-gray-700 mb-1">{profile.personalInfo.title}</p>
              <p className="text-sm text-gray-600 mb-4">{profile.personalInfo.location}</p>
              
              <div className="text-left space-y-2">
                {profile.summary && (
                  <div>
                    <h4 className="font-black text-xs mb-1">SUMMARY</h4>
                    <p className="text-xs text-gray-700 leading-tight">
                      {profile.summary.substring(0, 80)}...
                    </p>
                  </div>
                )}
                
                {profile.experience.length > 0 && (
                  <div>
                    <h4 className="font-black text-xs mb-1">EXPERIENCE</h4>
                    <div className="text-xs">
                      <p className="font-bold">{profile.experience[0].position}</p>
                      <p className="text-gray-600">{profile.experience[0].company}</p>
                    </div>
                  </div>
                )}
                
                {profile.skills.length > 0 && (
                  <div>
                    <h4 className="font-black text-xs mb-1">SKILLS</h4>
                    <div className="flex flex-wrap gap-1">
                      {profile.skills.slice(0, 4).map((skill, index) => (
                        <span key={index} className="bg-gray-200 px-1 py-0.5 text-xs font-bold border border-gray-400">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          /* Generation Complete */
          <div className="space-y-6">
            {/* Success Message */}
            <div className="bg-green-100 border-4 border-black p-8 shadow-brutal text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-black mb-4 text-green-700">CV GENERATED SUCCESSFULLY!</h2>
              <p className="text-lg font-bold text-green-600">
                Your professional CV has been created and downloaded
              </p>
            </div>

            {/* CV Summary */}
            <div className="bg-white border-4 border-black p-6 shadow-brutal">
              <h3 className="text-2xl font-black mb-4">CV SUMMARY</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="bg-blue-500 text-white w-16 h-16 border-4 border-black mx-auto mb-2 flex items-center justify-center">
                    <span className="text-2xl font-black">{profile.experience.length}</span>
                  </div>
                  <p className="font-black">WORK EXPERIENCES</p>
                </div>
                
                <div className="text-center">
                  <div className="bg-purple-500 text-white w-16 h-16 border-4 border-black mx-auto mb-2 flex items-center justify-center">
                    <span className="text-2xl font-black">{profile.education.length}</span>
                  </div>
                  <p className="font-black">EDUCATION ENTRIES</p>
                </div>
                
                <div className="text-center">
                  <div className="bg-orange-500 text-white w-16 h-16 border-4 border-black mx-auto mb-2 flex items-center justify-center">
                    <span className="text-2xl font-black">{profile.skills.length}</span>
                  </div>
                  <p className="font-black">SKILLS LISTED</p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={generateAndDownloadPDF}
                className="bg-green-500 text-white px-8 py-4 border-4 border-black font-black text-xl hover:bg-green-600 transition-colors shadow-brutal"
              >
                DOWNLOAD AGAIN
              </button>
              
              <button
                onClick={() => router.push('/templates')}
                className="bg-blue-500 text-white px-8 py-4 border-4 border-black font-black text-xl hover:bg-blue-600 transition-colors shadow-brutal"
              >
                TRY DIFFERENT TEMPLATE
              </button>
              
              <button
                onClick={createNewCV}
                className="bg-purple-500 text-white px-8 py-4 border-4 border-black font-black text-xl hover:bg-purple-600 transition-colors shadow-brutal"
              >
                CREATE NEW CV
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
