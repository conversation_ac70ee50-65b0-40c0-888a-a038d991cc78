'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import { Upload, Zap, FileText, Download } from 'lucide-react'

export default function Home() {
  const [currentStep, setCurrentStep] = useState(0)
  const router = useRouter()

  const steps = [
    { icon: Upload, title: 'Upload LinkedIn Screenshot', description: 'Drop your LinkedIn profile screenshot' },
    { icon: Zap, title: 'AI Processing', description: 'Our AI extracts your professional info' },
    { icon: FileText, title: 'CV Generation', description: 'We create a professional CV layout' },
    { icon: Download, title: 'Download PDF', description: 'Get your polished CV instantly' }
  ]

  const handleGetStarted = () => {
    router.push('/upload')
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="neo-border-thick border-t-0 border-l-0 border-r-0 bg-yellow-400 p-6">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <h1 className="text-3xl font-bold uppercase tracking-wide">
            LinkedIn AI CV Builder
          </h1>
          <div className="flex gap-4">
            <Button variant="secondary" size="sm">
              About
            </Button>
            <Button variant="primary" size="sm" onClick={handleGetStarted}>
              Get Started
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-6xl font-bold uppercase tracking-wide mb-6 leading-tight">
            Transform Your
            <span className="block text-pink-500">LinkedIn Profile</span>
            Into a Professional CV
          </h2>
          <p className="text-xl font-medium mb-12 max-w-2xl mx-auto">
            Upload a screenshot of your LinkedIn profile and let our AI create a stunning,
            professional CV in seconds. No manual data entry required!
          </p>

          <div className="flex gap-6 justify-center mb-16">
            <Button size="lg" color="cyan" onClick={handleGetStarted}>
              Start Building CV
            </Button>
            <Button variant="secondary" size="lg">
              See Examples
            </Button>
          </div>

          {/* Process Steps */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16">
            {steps.map((step, index) => {
              const Icon = step.icon
              return (
                <Card
                  key={index}
                  className={`text-center transition-all duration-300 ${
                    index === currentStep ? 'transform -translate-y-2' : ''
                  }`}
                  color={index === 0 ? 'yellow' : index === 1 ? 'cyan' : index === 2 ? 'pink' : 'green'}
                >
                  <CardContent>
                    <div className="flex justify-center mb-4">
                      <div className="neo-border neo-shadow p-4 bg-white">
                        <Icon size={32} className="text-black" />
                      </div>
                    </div>
                    <h3 className="font-bold text-lg uppercase mb-2">
                      {step.title}
                    </h3>
                    <p className="text-sm font-medium">
                      {step.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6 bg-black text-white">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold uppercase text-center mb-16">
            Why Choose Our AI CV Builder?
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-yellow-400 text-black">
              <CardHeader>
                <CardTitle>⚡ Lightning Fast</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="font-medium">
                  Generate professional CVs in under 30 seconds. No more hours of formatting!
                </p>
              </CardContent>
            </Card>

            <Card className="bg-pink-500 text-white">
              <CardHeader>
                <CardTitle>🎯 AI Powered</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="font-medium">
                  Advanced AI extracts and organizes your professional information intelligently.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-cyan-400 text-black">
              <CardHeader>
                <CardTitle>📄 Professional</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="font-medium">
                  Clean, ATS-friendly formats that get you noticed by recruiters.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="neo-border-thick border-b-0 border-l-0 border-r-0 bg-white p-8">
        <div className="max-w-6xl mx-auto text-center">
          <p className="font-bold uppercase tracking-wide">
            Built with ❤️ using AI & Neobrutalism Design
          </p>
        </div>
      </footer>
    </div>
  )
}
