import React from 'react'
import { cn, type NeoColor, neoColors } from '@/lib/utils'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  color?: NeoColor
  children: React.ReactNode
}

export function Button({ 
  variant = 'primary', 
  size = 'md', 
  color,
  className, 
  children, 
  ...props 
}: ButtonProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return 'bg-white text-black hover:bg-yellow-400'
      case 'secondary':
        return 'bg-black text-white hover:bg-gray-800'
      case 'danger':
        return 'bg-red-500 text-white hover:bg-red-600'
      default:
        return 'bg-white text-black hover:bg-yellow-400'
    }
  }

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2 text-sm sm:px-4'
      case 'md':
        return 'px-4 py-3 text-sm sm:px-6 sm:text-base'
      case 'lg':
        return 'px-6 py-3 text-base sm:px-8 sm:py-4 sm:text-lg'
      default:
        return 'px-4 py-3 text-sm sm:px-6 sm:text-base'
    }
  }

  const colorStyle = color ? { backgroundColor: neoColors[color] } : {}

  return (
    <button
      className={cn(
        'neo-button',
        getVariantStyles(),
        getSizeStyles(),
        className
      )}
      style={colorStyle}
      {...props}
    >
      {children}
    </button>
  )
}
