module.exports = {

"[project]/.next-internal/server/app/api/test-google-vision/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/process [external] (process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("process", () => require("process"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/google-vision.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractTextWithGoogleVision": (()=>extractTextWithGoogleVision),
    "parseLinkedInText": (()=>parseLinkedInText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$google$2d$auth$2d$library$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/google-auth-library/build/src/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
// Google Vision API configuration
const GOOGLE_SERVICE_ACCOUNT = process.env.GOOGLE_SERVICE_ACCOUNT;
const GOOGLE_VISION_ENDPOINT = 'https://vision.googleapis.com/v1/images:annotate';
const CREDENTIALS_PATH = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'google-credentials.json');
async function extractTextWithGoogleVision(imageBase64) {
    try {
        console.log('Starting Google Vision API text extraction with service account...');
        // Create Google Auth client using credentials file
        const auth = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$google$2d$auth$2d$library$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleAuth"]({
            keyFile: CREDENTIALS_PATH,
            scopes: [
                'https://www.googleapis.com/auth/cloud-platform'
            ]
        });
        // Get access token
        const authClient = await auth.getClient();
        const accessToken = await authClient.getAccessToken();
        if (!accessToken.token) {
            throw new Error('Failed to get access token');
        }
        console.log('Successfully obtained access token');
        const requestBody = {
            requests: [
                {
                    image: {
                        content: imageBase64
                    },
                    features: [
                        {
                            type: 'TEXT_DETECTION',
                            maxResults: 1
                        },
                        {
                            type: 'DOCUMENT_TEXT_DETECTION',
                            maxResults: 1
                        }
                    ]
                }
            ]
        };
        const response = await fetch(GOOGLE_VISION_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken.token}`
            },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Google Vision API error response:', errorText);
            throw new Error(`Google Vision API error: ${response.status} ${response.statusText} - ${errorText}`);
        }
        const data = await response.json();
        console.log('Google Vision API response received');
        if (data.responses[0]?.error) {
            throw new Error(`Google Vision API error: ${data.responses[0].error.message}`);
        }
        // Get the full text from the response
        const fullText = data.responses[0]?.fullTextAnnotation?.text || data.responses[0]?.textAnnotations?.[0]?.description || '';
        console.log('Extracted text length:', fullText.length);
        console.log('Text preview:', fullText.substring(0, 200) + '...');
        return fullText;
    } catch (error) {
        console.error('Google Vision API error:', error);
        throw new Error(`Failed to extract text with Google Vision: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
function parseLinkedInText(extractedText) {
    console.log('Parsing LinkedIn text...');
    // Split text into lines for easier parsing
    const lines = extractedText.split('\n').map((line)=>line.trim()).filter((line)=>line.length > 0);
    // Initialize profile structure
    const profile = {
        personalInfo: {
            name: '',
            title: '',
            location: '',
            email: '',
            phone: '',
            linkedin: ''
        },
        summary: '',
        experience: [],
        education: [],
        skills: [],
        certifications: [],
        languages: []
    };
    // Parse personal information (usually at the top)
    if (lines.length > 0) {
        // First non-empty line is usually the name
        profile.personalInfo.name = lines[0];
        // Look for title/headline (usually second line or after name)
        if (lines.length > 1) {
            profile.personalInfo.title = lines[1];
        }
        // Look for location (often contains city, state, country)
        for(let i = 0; i < Math.min(5, lines.length); i++){
            const line = lines[i].toLowerCase();
            if (line.includes(',') && (line.includes('ca') || line.includes('ny') || line.includes('usa') || line.includes('united states') || line.includes('san francisco') || line.includes('new york'))) {
                profile.personalInfo.location = lines[i];
                break;
            }
        }
    }
    // Look for email addresses
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
    const emailMatch = extractedText.match(emailRegex);
    if (emailMatch) {
        profile.personalInfo.email = emailMatch[0];
    }
    // Look for phone numbers
    const phoneRegex = /(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
    const phoneMatch = extractedText.match(phoneRegex);
    if (phoneMatch) {
        profile.personalInfo.phone = phoneMatch[0];
    }
    // Look for LinkedIn URL
    const linkedinRegex = /linkedin\.com\/in\/[a-zA-Z0-9-]+/g;
    const linkedinMatch = extractedText.match(linkedinRegex);
    if (linkedinMatch) {
        profile.personalInfo.linkedin = 'https://' + linkedinMatch[0];
    }
    // Look for About/Summary section
    const aboutIndex = extractedText.toLowerCase().indexOf('about');
    if (aboutIndex !== -1) {
        const aboutSection = extractedText.substring(aboutIndex);
        const nextSectionIndex = aboutSection.toLowerCase().search(/(experience|education|skills)/i);
        if (nextSectionIndex !== -1) {
            profile.summary = aboutSection.substring(5, nextSectionIndex).trim();
        } else {
            profile.summary = aboutSection.substring(5, Math.min(500, aboutSection.length)).trim();
        }
    }
    // Parse Experience section
    const experienceIndex = extractedText.toLowerCase().indexOf('experience');
    if (experienceIndex !== -1) {
        const experienceSection = extractedText.substring(experienceIndex);
        const nextSectionIndex = experienceSection.toLowerCase().search(/(education|skills|certifications)/i);
        const expText = nextSectionIndex !== -1 ? experienceSection.substring(10, nextSectionIndex) : experienceSection.substring(10, Math.min(1000, experienceSection.length));
        // Simple parsing - look for company patterns
        const expLines = expText.split('\n').filter((line)=>line.trim().length > 0);
        let currentExp = null;
        for (const line of expLines){
            if (line.includes('·') || line.includes('•') || /\d{4}/.test(line)) {
                if (currentExp) {
                    profile.experience.push(currentExp);
                }
                currentExp = {
                    company: '',
                    position: line.trim(),
                    duration: '',
                    description: '',
                    location: ''
                };
            } else if (currentExp && line.trim().length > 0) {
                if (!currentExp.company) {
                    currentExp.company = line.trim();
                } else {
                    currentExp.description += line.trim() + ' ';
                }
            }
        }
        if (currentExp) {
            profile.experience.push(currentExp);
        }
    }
    // Parse Education section
    const educationIndex = extractedText.toLowerCase().indexOf('education');
    if (educationIndex !== -1) {
        const educationSection = extractedText.substring(educationIndex);
        const nextSectionIndex = educationSection.toLowerCase().search(/(skills|certifications|languages)/i);
        const eduText = nextSectionIndex !== -1 ? educationSection.substring(9, nextSectionIndex) : educationSection.substring(9, Math.min(500, educationSection.length));
        const eduLines = eduText.split('\n').filter((line)=>line.trim().length > 0);
        if (eduLines.length > 0) {
            profile.education.push({
                institution: eduLines[0] || '',
                degree: eduLines[1] || '',
                field: eduLines[2] || '',
                duration: eduLines.find((line)=>/\d{4}/.test(line)) || '',
                location: ''
            });
        }
    }
    // Parse Skills section
    const skillsIndex = extractedText.toLowerCase().indexOf('skills');
    if (skillsIndex !== -1) {
        const skillsSection = extractedText.substring(skillsIndex);
        const nextSectionIndex = skillsSection.toLowerCase().search(/(certifications|languages|recommendations)/i);
        const skillsText = nextSectionIndex !== -1 ? skillsSection.substring(6, nextSectionIndex) : skillsSection.substring(6, Math.min(300, skillsSection.length));
        // Split by common delimiters and clean up
        const skillsList = skillsText.split(/[,•·\n]/).map((skill)=>skill.trim()).filter((skill)=>skill.length > 0 && skill.length < 50).slice(0, 20) // Limit to 20 skills
        ;
        profile.skills = skillsList;
    }
    console.log('Parsing completed:', {
        name: profile.personalInfo.name,
        title: profile.personalInfo.title,
        experienceCount: profile.experience.length,
        skillsCount: profile.skills.length
    });
    return profile;
}
}}),
"[project]/src/app/api/test-google-vision/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$google$2d$vision$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/google-vision.ts [app-route] (ecmascript)");
;
;
async function GET() {
    try {
        // Test will use the credentials file directly
        // Test with a simple image (1x1 pixel PNG with some text)
        const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
        console.log('Testing Google Vision API...');
        const extractedText = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$google$2d$vision$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractTextWithGoogleVision"])(testImageBase64);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Google Vision API is working with service account',
            extractedText: extractedText || 'No text found in test image (this is normal)',
            serviceAccountConfigured: true
        });
    } catch (error) {
        console.error('Google Vision API test failed:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Google Vision API test failed',
            details: error instanceof Error ? error.message : 'Unknown error',
            instructions: [
                '1. Get API key from Google Cloud Console',
                '2. Enable Vision API in your Google Cloud project',
                '3. Add GOOGLE_VISION_API_KEY to .env.local',
                '4. Make sure billing is enabled for your Google Cloud project'
            ]
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        // Test will use the credentials file directly
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        // Convert file to base64
        const arrayBuffer = await file.arrayBuffer();
        const base64 = Buffer.from(arrayBuffer).toString('base64');
        console.log('Testing Google Vision with uploaded file...');
        console.log('File type:', file.type);
        console.log('File size:', file.size);
        const extractedText = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$google$2d$vision$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractTextWithGoogleVision"])(base64);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Google Vision text extraction successful',
            extractedText: extractedText,
            textLength: extractedText.length,
            preview: extractedText.substring(0, 500) + (extractedText.length > 500 ? '...' : '')
        });
    } catch (error) {
        console.error('Google Vision file test failed:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Google Vision file test failed',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__aa7a83dd._.js.map