import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

export async function GET() {
  try {
    if (!process.env.AIML_API_KEY) {
      return NextResponse.json({ error: 'No API key' })
    }

    const aimlClient = new OpenAI({
      apiKey: process.env.AIML_API_KEY,
      baseURL: 'https://api.aimlapi.com',
    })

    console.log('Testing basic text completion...')

    const response = await aimlClient.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: "Hello! Please respond with 'Text API is working' if you can see this."
        }
      ],
      max_tokens: 20,
      temperature: 0.1,
    })

    const content = response.choices[0]?.message?.content

    return NextResponse.json({
      success: true,
      message: 'Text API working',
      response: content
    })

  } catch (error) {
    console.error('Text API test failed:', error)
    
    return NextResponse.json({
      error: 'Text API failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      status: (error as any)?.status || 'unknown'
    }, { status: 500 })
  }
}
