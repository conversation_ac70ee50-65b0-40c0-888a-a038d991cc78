'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { FileUpload } from '@/components/ui/FileUpload'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import { ArrowLeft, ArrowRight, Sparkles, AlertCircle } from 'lucide-react'
import { useLinkedInProcessor } from '@/hooks/useLinkedInProcessor'

export default function UploadPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const router = useRouter()
  const { isProcessing, progress, status, error, processFile, reset } = useLinkedInProcessor()

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
    reset() // Reset any previous processing state
  }

  const handleFileRemove = () => {
    setSelectedFile(null)
    reset()
  }

  const handleProcessFile = async () => {
    if (!selectedFile) return

    const result = await processFile(selectedFile)

    if (result) {
      // Store the result in sessionStorage for the results page
      sessionStorage.setItem('linkedinProfile', JSON.stringify(result))
      router.push('/results')
    }
    // If there's an error, it will be shown in the UI via the error state
  }

  const goBack = () => {
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="neo-border-thick border-t-0 border-l-0 border-r-0 bg-yellow-400 p-6">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="secondary" size="sm" onClick={goBack}>
              <ArrowLeft size={16} className="mr-2" />
              Back
            </Button>
            <h1 className="text-3xl font-bold uppercase tracking-wide">
              Upload LinkedIn Screenshot
            </h1>
          </div>
          <div className="text-sm font-bold uppercase">
            Step 1 of 4
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="py-12 px-6">
        <div className="max-w-4xl mx-auto">
          {/* Instructions */}
          <Card className="mb-8" color="cyan">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Sparkles size={24} />
                How to Get the Best Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-bold uppercase mb-2">✅ Do Include:</h4>
                  <ul className="space-y-1 font-medium">
                    <li>• Your full profile summary</li>
                    <li>• Work experience section</li>
                    <li>• Education details</li>
                    <li>• Skills section</li>
                    <li>• Clear, readable text</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-bold uppercase mb-2">❌ Avoid:</h4>
                  <ul className="space-y-1 font-medium">
                    <li>• Blurry or low-quality images</li>
                    <li>• Cropped important sections</li>
                    <li>• Dark mode (harder to read)</li>
                    <li>• Multiple profiles in one image</li>
                    <li>• Private/confidential information</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card className="mb-8">
            <CardContent>
              <FileUpload
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                selectedFile={selectedFile}
              />
            </CardContent>
          </Card>

          {/* Action Buttons */}
          {selectedFile && (
            <div className="flex justify-center">
              <Button
                size="lg"
                color="green"
                onClick={handleProcessFile}
                disabled={isProcessing}
                className="flex items-center gap-3"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-black border-t-transparent"></div>
                    Processing with AI...
                  </>
                ) : (
                  <>
                    <Sparkles size={20} />
                    Process with AI
                    <ArrowRight size={20} />
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Processing Status */}
          {isProcessing && (
            <Card className="mt-8" color="yellow">
              <CardContent>
                <div className="text-center">
                  <div className="flex justify-center mb-4">
                    <div className="neo-border neo-shadow p-4 bg-white">
                      <div className="animate-spin rounded-full h-8 w-8 border-4 border-black border-t-transparent"></div>
                    </div>
                  </div>
                  <h3 className="text-xl font-bold uppercase mb-2">
                    AI is Processing Your LinkedIn Profile
                  </h3>
                  <p className="font-medium mb-4">
                    {status}
                  </p>
                  <div className="neo-border bg-white p-2">
                    <div className="h-3 bg-gray-200 neo-border">
                      <div
                        className="h-full bg-green-400 transition-all duration-500"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                    <p className="text-sm font-bold mt-2">{progress}% Complete</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Card className="mt-8" color="red">
              <CardContent>
                <div className="flex items-center gap-3">
                  <AlertCircle size={24} className="text-red-600" />
                  <div>
                    <h3 className="font-bold uppercase mb-1">Processing Error</h3>
                    <p className="font-medium">{error}</p>
                    <Button
                      size="sm"
                      className="mt-3"
                      onClick={() => selectedFile && handleProcessFile()}
                    >
                      Try Again
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </div>
  )
}
