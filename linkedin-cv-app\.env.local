# Google Vision API Configuration (Service Account)
GOOGLE_SERVICE_ACCOUNT='{"type":"service_account","project_id":"starlit-advice-450421-g0","private_key_id":"ae0267821a336d44a2282a8329c9cb7f65782cfd","private_key":"-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDOBuorgzBItYHl\\nvrYmgxZ2zAdNmyqDsj9VOhp42TpOZCp7vwzBMB7jlcdJxqTILOP+VfZVjUafQbth\\nW1Oyv5iS708+inxShlxtOpVMRI2QoDCawPvI9n21s+y7Pc8qadhhSp8XkN/p2d55\\nvWnJxmuxkvm7A+ApoAebmgaxB/zLPGhUcE3kbBFxQrFkvj1tcq6eOrD8RiPEGCoV\\nsygvQUMR1GmKexJi6aKLLXMX+U+Qew23GQPFmFaXgR8n9sNfe2F2rAZMLg3PpyJ3\\n/MTCRcbok04eu/HeN+JSiIAm0ZG+Z7LP2Y0kVkxgd6YlOC20J2nTOlL0wJCtDsIV\\n6KKzQjL/AgMBAAECggEAN3S8Bn9W+VNGBjnJ3N7vJoXvt097Z5yMmJu9sWbDX4Nt\\nvgZkYAHcZqUT0gNyqmEO85Be8JNI/agbUG7dPiF9Z5ubyHlNUodRDKO2uSNAubus\\nqZMlkufqWYT3RUUzZLa95B5KaXT5GICo9jrqZUIQDenCZ7j41yt4sMUKZQ+B15BM\\noWN5WINkOhq9NPLxk9nOd5BXi0gkG1wxGAdJ3s0vninxT85jf4RKC+Hjuw0joutQ\\nmkBUCOu0s2HBQj5LNby4Vd+UwMLQCCtJFNJcsNLVIup1KHr6IHRxj4+qibS2NOb6\\nxg6dMR/U7upHEoTwQcbWed4LtLQuWRD57dqhM1ul5QKBgQD0lzAFfkt1ymYfohbZ\\nxOjPgz1eeWIgFHJPPbpjxLAMWor1+4rFVPyGabndB2/1j4FAtULu4+SwL1xPFWRh\\nwyU4cToN5ELB90RtoZGrkTic7Urn03GDRLdFXsbVmdBrBlfc6rxR6VJkcnikfE7v\\nBEsJUreSh8XjLo/DNpHO1kTI8wKBgQDXozcHKP/HO0jAlK9KbMPbaftAteBJUn2Z\\nXK3SOSNqqgAQc66fS62Y0Nhe7UgHsqPwnI6+Tmww3XeaH72gvrWa4ayXCETpR5Ri\\nglQ8Bm3dnZHfgcnw0TwlHIvvEkPfKIrJUdrMfierjHUezY+obQvDLlfYf2HUJLZn\\nF967dEIwxQKBgQCk1t++VAd3MxjwaMUNru9YKxLJAPVouV7wmKFta7laGgtdDFHR\\n/BzUWZyxobmKvVRWz9J0PZJ6SvDjO3+Tg3kqOOTvR87V+ldbq3AJOK321NWOt6ng\\n1gbN8bn/atJTzsqYUHrqmVy15Y/5Cu4Hl9UaS2z8jBSJr2+kGLrbsvxYkQKBgDGQ\\nup1Stzg8UESz6Hba+Un92lrNc3hnFaeivfGMYK+r08f0TzceVhDCMHnhYKUkUrqD\\nwevMauHwhgVPNm80Ztnq9ZjhtbVJVrot6t7BO4+tIFKXqqtH7OM65I9XS3KIBx6v\\nSfOnK8cqyeECrvqv1fxPMrH41avFpIed3PyJhI1hAoGASdCl5lj1fr7sXLZQkeyw\\nBZZLWAAsS9Ik+NdOqgXOmtciPnRWN1qWDyer2CZbZ6pelmYS3QrozRfLtiW8rPQu\\n0p0vvqNWxtC4DVBHOfyu75F3EVOfRPY1APTbSCw5FSq5ngt1K1W6f+efHKzVGOfs\\nr2MSIsfncqptjt6gVDu0HA8=\\n-----END PRIVATE KEY-----\\n","client_email":"*******","client_id":"113643156734261781809","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/linkedapp%40starlit-advice-450421-g0.iam.gserviceaccount.com","universe_domain":"googleapis.com"}'

# AIML API Configuration (Fallback)
AIML_API_KEY=9f4428670e5b43278e3104a956fc936b

# This file contains sensitive information and should never be committed to version control
# Make sure .env.local is in your .gitignore file
