import { LinkedInProfile } from './openai'
import { GoogleAuth } from 'google-auth-library'
import path from 'path'

// Google Vision API configuration
const GOOGLE_SERVICE_ACCOUNT = process.env.GOOGLE_SERVICE_ACCOUNT
const GOOGLE_VISION_ENDPOINT = 'https://vision.googleapis.com/v1/images:annotate'
const CREDENTIALS_PATH = path.join(process.cwd(), 'google-credentials.json')

interface GoogleVisionResponse {
  responses: Array<{
    textAnnotations?: Array<{
      description: string
      boundingPoly?: {
        vertices: Array<{ x: number; y: number }>
      }
    }>
    fullTextAnnotation?: {
      text: string
    }
    error?: {
      code: number
      message: string
    }
  }>
}

export async function extractTextWithGoogleVision(imageBase64: string): Promise<string> {
  try {
    console.log('Starting Google Vision API text extraction with service account...')

    // Create Google Auth client using credentials file
    const auth = new GoogleAuth({
      keyFile: CREDENTIALS_PATH,
      scopes: ['https://www.googleapis.com/auth/cloud-platform']
    })

    // Get access token
    const authClient = await auth.getClient()
    const accessToken = await authClient.getAccessToken()

    if (!accessToken.token) {
      throw new Error('Failed to get access token')
    }

    console.log('Successfully obtained access token')

    const requestBody = {
      requests: [
        {
          image: {
            content: imageBase64
          },
          features: [
            {
              type: 'TEXT_DETECTION',
              maxResults: 1
            },
            {
              type: 'DOCUMENT_TEXT_DETECTION',
              maxResults: 1
            }
          ]
        }
      ]
    }

    const response = await fetch(GOOGLE_VISION_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken.token}`
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Google Vision API error response:', errorText)
      throw new Error(`Google Vision API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data: GoogleVisionResponse = await response.json()
    console.log('Google Vision API response received')

    if (data.responses[0]?.error) {
      throw new Error(`Google Vision API error: ${data.responses[0].error.message}`)
    }

    // Get the full text from the response
    const fullText = data.responses[0]?.fullTextAnnotation?.text || 
                    data.responses[0]?.textAnnotations?.[0]?.description || ''

    console.log('Extracted text length:', fullText.length)
    console.log('Text preview:', fullText.substring(0, 200) + '...')

    return fullText

  } catch (error) {
    console.error('Google Vision API error:', error)
    throw new Error(`Failed to extract text with Google Vision: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Enhanced text cleaning and preprocessing
function cleanText(text: string): string {
  return text
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[""]/g, '"') // Normalize quotes
    .replace(/['']/g, "'") // Normalize apostrophes
    .replace(/\u00A0/g, ' ') // Replace non-breaking spaces
    .trim()
}

// Enhanced date parsing and formatting
function parseDate(dateStr: string): string {
  if (!dateStr) return ''

  // Common LinkedIn date patterns
  const patterns = [
    /(\w{3,9})\s+(\d{4})\s*[-–—]\s*(\w{3,9})\s+(\d{4})/i, // "Jan 2020 - Dec 2023"
    /(\w{3,9})\s+(\d{4})\s*[-–—]\s*(Present|Current)/i,    // "Jan 2020 - Present"
    /(\d{4})\s*[-–—]\s*(\d{4})/,                           // "2020 - 2023"
    /(\d{4})\s*[-–—]\s*(Present|Current)/i,                // "2020 - Present"
    /(\w{3,9})\s+(\d{4})/i                                 // "Jan 2020"
  ]

  for (const pattern of patterns) {
    const match = dateStr.match(pattern)
    if (match) {
      return match[0].trim()
    }
  }

  return dateStr.trim()
}

// Enhanced section detection
function detectSection(text: string): string | null {
  const sectionKeywords = {
    'experience': ['experience', 'work history', 'employment', 'professional experience'],
    'education': ['education', 'academic background', 'qualifications'],
    'skills': ['skills', 'technical skills', 'competencies', 'expertise'],
    'about': ['about', 'summary', 'profile', 'overview'],
    'certifications': ['certifications', 'certificates', 'licenses'],
    'languages': ['languages', 'language skills'],
    'projects': ['projects', 'key projects'],
    'achievements': ['achievements', 'accomplishments', 'awards']
  }

  const lowerText = text.toLowerCase()

  for (const [section, keywords] of Object.entries(sectionKeywords)) {
    if (keywords.some(keyword => lowerText.includes(keyword))) {
      return section
    }
  }

  return null
}

export function parseLinkedInText(extractedText: string): LinkedInProfile {
  console.log('Enhanced parsing of LinkedIn text...')
  console.log('Raw text length:', extractedText.length)

  // Clean and preprocess text
  const cleanedText = cleanText(extractedText)
  const lines = cleanedText.split('\n').map(line => line.trim()).filter(line => line.length > 0)

  console.log('Cleaned text lines:', lines.length)

  // Initialize profile structure
  const profile: LinkedInProfile = {
    personalInfo: {
      name: '',
      title: '',
      location: '',
      email: '',
      phone: '',
      linkedin: ''
    },
    summary: '',
    experience: [],
    education: [],
    skills: [],
    certifications: [],
    languages: []
  }

  // Enhanced personal information parsing
  if (lines.length > 0) {
    // Find name (usually the largest text at the top, often in first 3 lines)
    for (let i = 0; i < Math.min(3, lines.length); i++) {
      const line = lines[i]
      // Name is typically 2-4 words, contains letters, and is not a common LinkedIn element
      if (line.length > 3 && line.length < 50 &&
          /^[A-Za-z\s\-'\.]+$/.test(line) &&
          !line.toLowerCase().includes('connect') &&
          !line.toLowerCase().includes('message') &&
          !line.toLowerCase().includes('follow')) {
        profile.personalInfo.name = line
        break
      }
    }

    // Find professional title/headline (usually after name, before location)
    for (let i = 1; i < Math.min(5, lines.length); i++) {
      const line = lines[i]
      // Title often contains job-related keywords
      if (line.length > 5 && line.length < 100 &&
          (line.toLowerCase().includes('engineer') ||
           line.toLowerCase().includes('developer') ||
           line.toLowerCase().includes('manager') ||
           line.toLowerCase().includes('director') ||
           line.toLowerCase().includes('analyst') ||
           line.toLowerCase().includes('consultant') ||
           line.toLowerCase().includes('specialist') ||
           line.toLowerCase().includes('lead') ||
           line.toLowerCase().includes('senior') ||
           line.toLowerCase().includes('junior') ||
           line.toLowerCase().includes('at ') ||
           /\b(CEO|CTO|CFO|VP|President|Founder)\b/i.test(line))) {
        profile.personalInfo.title = line
        break
      }
    }

    // Enhanced location detection
    for (let i = 0; i < Math.min(8, lines.length); i++) {
      const line = lines[i].toLowerCase()
      // Look for location patterns
      if (line.includes(',') && (
          // US locations
          line.includes('ca') || line.includes('california') ||
          line.includes('ny') || line.includes('new york') ||
          line.includes('tx') || line.includes('texas') ||
          line.includes('fl') || line.includes('florida') ||
          line.includes('wa') || line.includes('washington') ||
          line.includes('usa') || line.includes('united states') ||
          // International locations
          line.includes('uk') || line.includes('united kingdom') ||
          line.includes('canada') || line.includes('australia') ||
          line.includes('germany') || line.includes('france') ||
          line.includes('india') || line.includes('singapore') ||
          // Common city patterns
          /\b(san francisco|los angeles|chicago|boston|seattle|austin|denver|miami|atlanta)\b/.test(line) ||
          /\b(london|paris|berlin|tokyo|sydney|toronto|vancouver)\b/.test(line)
      )) {
        profile.personalInfo.location = lines[i]
        break
      }
    }
  }

  // Look for email addresses
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
  const emailMatch = extractedText.match(emailRegex)
  if (emailMatch) {
    profile.personalInfo.email = emailMatch[0]
  }

  // Look for phone numbers
  const phoneRegex = /(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g
  const phoneMatch = extractedText.match(phoneRegex)
  if (phoneMatch) {
    profile.personalInfo.phone = phoneMatch[0]
  }

  // Look for LinkedIn URL
  const linkedinRegex = /linkedin\.com\/in\/[a-zA-Z0-9-]+/g
  const linkedinMatch = extractedText.match(linkedinRegex)
  if (linkedinMatch) {
    profile.personalInfo.linkedin = 'https://' + linkedinMatch[0]
  }

  // Look for About/Summary section
  const aboutIndex = extractedText.toLowerCase().indexOf('about')
  if (aboutIndex !== -1) {
    const aboutSection = extractedText.substring(aboutIndex)
    const nextSectionIndex = aboutSection.toLowerCase().search(/(experience|education|skills)/i)
    if (nextSectionIndex !== -1) {
      profile.summary = aboutSection.substring(5, nextSectionIndex).trim()
    } else {
      profile.summary = aboutSection.substring(5, Math.min(500, aboutSection.length)).trim()
    }
  }

  // Enhanced Experience section parsing
  const experienceIndex = cleanedText.toLowerCase().indexOf('experience')
  if (experienceIndex !== -1) {
    console.log('Found experience section')
    const experienceSection = cleanedText.substring(experienceIndex)
    const nextSectionIndex = experienceSection.toLowerCase().search(/(education|skills|certifications|languages|projects)/i)
    const expText = nextSectionIndex !== -1 ?
      experienceSection.substring(10, nextSectionIndex) :
      experienceSection.substring(10, Math.min(2000, experienceSection.length))

    const expLines = expText.split('\n').filter(line => line.trim().length > 0)
    let currentExp: any = null
    let lineIndex = 0

    while (lineIndex < expLines.length) {
      const line = expLines[lineIndex].trim()

      // Detect job title (often contains keywords or is followed by company)
      if (line.length > 3 && (
          line.toLowerCase().includes('engineer') ||
          line.toLowerCase().includes('developer') ||
          line.toLowerCase().includes('manager') ||
          line.toLowerCase().includes('director') ||
          line.toLowerCase().includes('analyst') ||
          line.toLowerCase().includes('consultant') ||
          line.toLowerCase().includes('specialist') ||
          line.toLowerCase().includes('lead') ||
          line.toLowerCase().includes('senior') ||
          line.toLowerCase().includes('junior') ||
          /\b(CEO|CTO|CFO|VP|President|Founder|Intern)\b/i.test(line) ||
          // Or if next line looks like a company
          (lineIndex + 1 < expLines.length &&
           (expLines[lineIndex + 1].toLowerCase().includes('inc') ||
            expLines[lineIndex + 1].toLowerCase().includes('corp') ||
            expLines[lineIndex + 1].toLowerCase().includes('ltd') ||
            expLines[lineIndex + 1].toLowerCase().includes('llc') ||
            expLines[lineIndex + 1].toLowerCase().includes('company') ||
            expLines[lineIndex + 1].toLowerCase().includes('technologies') ||
            expLines[lineIndex + 1].toLowerCase().includes('solutions')))
      )) {
        // Save previous experience
        if (currentExp && currentExp.position) {
          profile.experience.push(currentExp)
        }

        // Start new experience
        currentExp = {
          company: '',
          position: line,
          duration: '',
          description: '',
          location: ''
        }

        // Look for company in next few lines
        for (let i = lineIndex + 1; i < Math.min(lineIndex + 4, expLines.length); i++) {
          const nextLine = expLines[i].trim()
          if (nextLine.length > 2 && !parseDate(nextLine) &&
              !nextLine.toLowerCase().includes('full-time') &&
              !nextLine.toLowerCase().includes('part-time') &&
              !nextLine.toLowerCase().includes('contract') &&
              !nextLine.toLowerCase().includes('freelance')) {
            currentExp.company = nextLine
            lineIndex = i
            break
          }
        }
      }
      // Detect duration/dates
      else if (currentExp && parseDate(line) && line.length > 4) {
        currentExp.duration = parseDate(line)
      }
      // Detect location
      else if (currentExp && line.includes(',') && line.length < 50) {
        currentExp.location = line
      }
      // Add to description
      else if (currentExp && line.length > 10 &&
               !line.toLowerCase().includes('connect') &&
               !line.toLowerCase().includes('message')) {
        currentExp.description += (currentExp.description ? ' ' : '') + line
      }

      lineIndex++
    }

    // Add final experience
    if (currentExp && currentExp.position) {
      profile.experience.push(currentExp)
    }

    console.log('Parsed experiences:', profile.experience.length)
  }

  // Parse Education section
  const educationIndex = extractedText.toLowerCase().indexOf('education')
  if (educationIndex !== -1) {
    const educationSection = extractedText.substring(educationIndex)
    const nextSectionIndex = educationSection.toLowerCase().search(/(skills|certifications|languages)/i)
    const eduText = nextSectionIndex !== -1 ? 
      educationSection.substring(9, nextSectionIndex) : 
      educationSection.substring(9, Math.min(500, educationSection.length))
    
    const eduLines = eduText.split('\n').filter(line => line.trim().length > 0)
    if (eduLines.length > 0) {
      profile.education.push({
        institution: eduLines[0] || '',
        degree: eduLines[1] || '',
        field: eduLines[2] || '',
        duration: eduLines.find(line => /\d{4}/.test(line)) || '',
        location: ''
      })
    }
  }

  // Enhanced Skills section parsing
  const skillsIndex = cleanedText.toLowerCase().indexOf('skills')
  if (skillsIndex !== -1) {
    console.log('Found skills section')
    const skillsSection = cleanedText.substring(skillsIndex)
    const nextSectionIndex = skillsSection.toLowerCase().search(/(certifications|languages|recommendations|projects|achievements)/i)
    const skillsText = nextSectionIndex !== -1 ?
      skillsSection.substring(6, nextSectionIndex) :
      skillsSection.substring(6, Math.min(500, skillsSection.length))

    // Enhanced skill extraction with better filtering
    const skillsList = skillsText
      .split(/[,•·\n\|]/) // Split by various delimiters
      .map(skill => skill.trim())
      .filter(skill => {
        // Filter out non-skills
        if (skill.length < 2 || skill.length > 30) return false
        if (/^\d+$/.test(skill)) return false // Pure numbers
        if (skill.toLowerCase().includes('show more')) return false
        if (skill.toLowerCase().includes('see all')) return false
        if (skill.toLowerCase().includes('skills')) return false
        if (skill.toLowerCase().includes('endorsed')) return false
        if (skill.toLowerCase().includes('connections')) return false

        // Keep technical skills, tools, languages, frameworks
        return /^[A-Za-z0-9\s\+\#\.\-\/]+$/.test(skill)
      })
      .slice(0, 25) // Limit to 25 skills

    // Deduplicate skills
    profile.skills = [...new Set(skillsList)]
    console.log('Parsed skills:', profile.skills.length)
  }

  console.log('Parsing completed:', {
    name: profile.personalInfo.name,
    title: profile.personalInfo.title,
    experienceCount: profile.experience.length,
    skillsCount: profile.skills.length
  })

  return profile
}
