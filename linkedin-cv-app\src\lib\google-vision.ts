import { LinkedInProfile } from './openai'

// Google Vision API configuration
const GOOGLE_VISION_API_KEY = process.env.GOOGLE_VISION_API_KEY
const GOOGLE_SERVICE_ACCOUNT = process.env.GOOGLE_SERVICE_ACCOUNT
const GOOGLE_VISION_ENDPOINT = 'https://vision.googleapis.com/v1/images:annotate'

interface GoogleVisionResponse {
  responses: Array<{
    textAnnotations?: Array<{
      description: string
      boundingPoly?: {
        vertices: Array<{ x: number; y: number }>
      }
    }>
    fullTextAnnotation?: {
      text: string
    }
    error?: {
      code: number
      message: string
    }
  }>
}

export async function extractTextWithGoogleVision(imageBase64: string): Promise<string> {
  if (!GOOGLE_VISION_API_KEY) {
    throw new Error('Google Vision API key not configured')
  }

  try {
    console.log('Starting Google Vision API text extraction...')

    const requestBody = {
      requests: [
        {
          image: {
            content: imageBase64
          },
          features: [
            {
              type: 'TEXT_DETECTION',
              maxResults: 1
            },
            {
              type: 'DOCUMENT_TEXT_DETECTION',
              maxResults: 1
            }
          ]
        }
      ]
    }

    const response = await fetch(`${GOOGLE_VISION_ENDPOINT}?key=${GOOGLE_VISION_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Google Vision API error response:', errorText)
      throw new Error(`Google Vision API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data: GoogleVisionResponse = await response.json()
    console.log('Google Vision API response received')

    if (data.responses[0]?.error) {
      throw new Error(`Google Vision API error: ${data.responses[0].error.message}`)
    }

    // Get the full text from the response
    const fullText = data.responses[0]?.fullTextAnnotation?.text || 
                    data.responses[0]?.textAnnotations?.[0]?.description || ''

    console.log('Extracted text length:', fullText.length)
    console.log('Text preview:', fullText.substring(0, 200) + '...')

    return fullText

  } catch (error) {
    console.error('Google Vision API error:', error)
    throw new Error(`Failed to extract text with Google Vision: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export function parseLinkedInText(extractedText: string): LinkedInProfile {
  console.log('Parsing LinkedIn text...')
  
  // Split text into lines for easier parsing
  const lines = extractedText.split('\n').map(line => line.trim()).filter(line => line.length > 0)
  
  // Initialize profile structure
  const profile: LinkedInProfile = {
    personalInfo: {
      name: '',
      title: '',
      location: '',
      email: '',
      phone: '',
      linkedin: ''
    },
    summary: '',
    experience: [],
    education: [],
    skills: [],
    certifications: [],
    languages: []
  }

  // Parse personal information (usually at the top)
  if (lines.length > 0) {
    // First non-empty line is usually the name
    profile.personalInfo.name = lines[0]
    
    // Look for title/headline (usually second line or after name)
    if (lines.length > 1) {
      profile.personalInfo.title = lines[1]
    }
    
    // Look for location (often contains city, state, country)
    for (let i = 0; i < Math.min(5, lines.length); i++) {
      const line = lines[i].toLowerCase()
      if (line.includes(',') && (line.includes('ca') || line.includes('ny') || line.includes('usa') || 
          line.includes('united states') || line.includes('san francisco') || line.includes('new york'))) {
        profile.personalInfo.location = lines[i]
        break
      }
    }
  }

  // Look for email addresses
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
  const emailMatch = extractedText.match(emailRegex)
  if (emailMatch) {
    profile.personalInfo.email = emailMatch[0]
  }

  // Look for phone numbers
  const phoneRegex = /(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g
  const phoneMatch = extractedText.match(phoneRegex)
  if (phoneMatch) {
    profile.personalInfo.phone = phoneMatch[0]
  }

  // Look for LinkedIn URL
  const linkedinRegex = /linkedin\.com\/in\/[a-zA-Z0-9-]+/g
  const linkedinMatch = extractedText.match(linkedinRegex)
  if (linkedinMatch) {
    profile.personalInfo.linkedin = 'https://' + linkedinMatch[0]
  }

  // Look for About/Summary section
  const aboutIndex = extractedText.toLowerCase().indexOf('about')
  if (aboutIndex !== -1) {
    const aboutSection = extractedText.substring(aboutIndex)
    const nextSectionIndex = aboutSection.toLowerCase().search(/(experience|education|skills)/i)
    if (nextSectionIndex !== -1) {
      profile.summary = aboutSection.substring(5, nextSectionIndex).trim()
    } else {
      profile.summary = aboutSection.substring(5, Math.min(500, aboutSection.length)).trim()
    }
  }

  // Parse Experience section
  const experienceIndex = extractedText.toLowerCase().indexOf('experience')
  if (experienceIndex !== -1) {
    const experienceSection = extractedText.substring(experienceIndex)
    const nextSectionIndex = experienceSection.toLowerCase().search(/(education|skills|certifications)/i)
    const expText = nextSectionIndex !== -1 ? 
      experienceSection.substring(10, nextSectionIndex) : 
      experienceSection.substring(10, Math.min(1000, experienceSection.length))
    
    // Simple parsing - look for company patterns
    const expLines = expText.split('\n').filter(line => line.trim().length > 0)
    let currentExp: any = null
    
    for (const line of expLines) {
      if (line.includes('·') || line.includes('•') || /\d{4}/.test(line)) {
        if (currentExp) {
          profile.experience.push(currentExp)
        }
        currentExp = {
          company: '',
          position: line.trim(),
          duration: '',
          description: '',
          location: ''
        }
      } else if (currentExp && line.trim().length > 0) {
        if (!currentExp.company) {
          currentExp.company = line.trim()
        } else {
          currentExp.description += line.trim() + ' '
        }
      }
    }
    if (currentExp) {
      profile.experience.push(currentExp)
    }
  }

  // Parse Education section
  const educationIndex = extractedText.toLowerCase().indexOf('education')
  if (educationIndex !== -1) {
    const educationSection = extractedText.substring(educationIndex)
    const nextSectionIndex = educationSection.toLowerCase().search(/(skills|certifications|languages)/i)
    const eduText = nextSectionIndex !== -1 ? 
      educationSection.substring(9, nextSectionIndex) : 
      educationSection.substring(9, Math.min(500, educationSection.length))
    
    const eduLines = eduText.split('\n').filter(line => line.trim().length > 0)
    if (eduLines.length > 0) {
      profile.education.push({
        institution: eduLines[0] || '',
        degree: eduLines[1] || '',
        field: eduLines[2] || '',
        duration: eduLines.find(line => /\d{4}/.test(line)) || '',
        location: ''
      })
    }
  }

  // Parse Skills section
  const skillsIndex = extractedText.toLowerCase().indexOf('skills')
  if (skillsIndex !== -1) {
    const skillsSection = extractedText.substring(skillsIndex)
    const nextSectionIndex = skillsSection.toLowerCase().search(/(certifications|languages|recommendations)/i)
    const skillsText = nextSectionIndex !== -1 ? 
      skillsSection.substring(6, nextSectionIndex) : 
      skillsSection.substring(6, Math.min(300, skillsSection.length))
    
    // Split by common delimiters and clean up
    const skillsList = skillsText.split(/[,•·\n]/)
      .map(skill => skill.trim())
      .filter(skill => skill.length > 0 && skill.length < 50)
      .slice(0, 20) // Limit to 20 skills
    
    profile.skills = skillsList
  }

  console.log('Parsing completed:', {
    name: profile.personalInfo.name,
    title: profile.personalInfo.title,
    experienceCount: profile.experience.length,
    skillsCount: profile.skills.length
  })

  return profile
}
