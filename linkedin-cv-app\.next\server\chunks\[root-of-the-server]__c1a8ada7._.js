module.exports = {

"[project]/.next-internal/server/app/api/process-linkedin/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cleanProfileData": (()=>cleanProfileData),
    "fileToBase64": (()=>fileToBase64),
    "processLinkedInScreenshot": (()=>processLinkedInScreenshot),
    "validateProfileData": (()=>validateProfileData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
// Initialize AIML API client (using OpenAI SDK with custom base URL)
const aimlClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.AIML_API_KEY,
    baseURL: 'https://api.aimlapi.com'
});
async function processLinkedInScreenshot(imageBase64) {
    const maxRetries = 2;
    let lastError = null;
    for(let attempt = 1; attempt <= maxRetries; attempt++){
        try {
            console.log(`Starting AIML API request (attempt ${attempt}/${maxRetries})...`);
            // Process the image directly
            const response = await aimlClient.chat.completions.create({
                model: "gpt-4o",
                messages: [
                    {
                        role: "user",
                        content: [
                            {
                                type: "text",
                                text: `What text do you see in this image? Describe what you can read.`
                            },
                            {
                                type: "image_url",
                                image_url: {
                                    url: `data:image/jpeg;base64,${imageBase64}`,
                                    detail: "low"
                                }
                            }
                        ]
                    }
                ],
                max_tokens: 500,
                temperature: 0.1
            });
            console.log('AIML API response received');
            console.log('Response object:', JSON.stringify(response, null, 2));
            const content = response.choices[0]?.message?.content;
            console.log('Response content length:', content?.length || 0);
            console.log('Response content preview:', content?.substring(0, 200));
            if (!content) {
                throw new Error('No response from AIML API');
            }
            console.log('Raw AI response:', content.substring(0, 500) + '...');
            // Try to parse the JSON response
            try {
                const rawProfileData = JSON.parse(content);
                console.log('Successfully parsed JSON response');
                // Clean and enhance the extracted data
                const cleanedProfileData = cleanProfileData(rawProfileData);
                console.log('Data cleaning completed');
                return cleanedProfileData;
            } catch (parseError) {
                console.log('Initial JSON parse failed, trying to extract JSON...');
                // If JSON parsing fails, try to extract JSON from the response
                // Look for JSON between ```json and ``` or just between { and }
                let jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
                if (!jsonMatch) {
                    jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/);
                }
                if (!jsonMatch) {
                    jsonMatch = content.match(/(\{[\s\S]*\})/);
                }
                if (jsonMatch) {
                    try {
                        const cleanedJson = jsonMatch[1] || jsonMatch[0];
                        console.log('Extracted JSON:', cleanedJson.substring(0, 200) + '...');
                        const rawProfileData = JSON.parse(cleanedJson);
                        console.log('Successfully parsed extracted JSON');
                        // Clean and enhance the extracted data
                        const cleanedProfileData = cleanProfileData(rawProfileData);
                        console.log('Data cleaning completed');
                        return cleanedProfileData;
                    } catch (secondParseError) {
                        console.error('Failed to parse extracted JSON:', secondParseError);
                    }
                }
                console.error('Raw response that failed to parse:', content);
                throw new Error('Failed to parse AI response as JSON');
            }
        } catch (error) {
            console.error(`Attempt ${attempt} failed:`, error);
            lastError = error instanceof Error ? error : new Error('Unknown error');
            if (attempt < maxRetries) {
                console.log(`Retrying in 2 seconds...`);
                await new Promise((resolve)=>setTimeout(resolve, 2000));
                continue;
            }
        }
    }
    // If all attempts failed, throw the last error
    if (lastError) {
        console.error('All attempts failed. Error processing LinkedIn screenshot with AIML API:', lastError);
        // Provide more detailed error information
        if (lastError instanceof Error) {
            console.error('Error message:', lastError.message);
            console.error('Error stack:', lastError.stack);
        }
        // Check if it's an API-related error
        if (lastError && typeof lastError === 'object' && 'response' in lastError) {
            console.error('API response error:', lastError);
        }
        throw new Error(`Failed to process LinkedIn screenshot with AI after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : 'Unknown error'}`);
    }
    // This should never be reached, but TypeScript requires it
    throw new Error('Unexpected error in processLinkedInScreenshot');
}
function fileToBase64(file) {
    return new Promise((resolve, reject)=>{
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = ()=>{
            const result = reader.result;
            // Remove the data URL prefix to get just the base64 string
            const base64 = result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = (error)=>reject(error);
    });
}
function validateProfileData(profile) {
    return !!(profile.personalInfo?.name && profile.personalInfo?.title && (profile.experience?.length > 0 || profile.education?.length > 0));
}
function cleanProfileData(profile) {
    return {
        personalInfo: {
            name: cleanText(profile.personalInfo?.name || ''),
            title: cleanText(profile.personalInfo?.title || ''),
            location: cleanText(profile.personalInfo?.location || ''),
            email: cleanEmail(profile.personalInfo?.email || ''),
            phone: cleanPhone(profile.personalInfo?.phone || ''),
            linkedin: cleanUrl(profile.personalInfo?.linkedin || '')
        },
        summary: cleanText(profile.summary || ''),
        experience: (profile.experience || []).map((exp)=>({
                company: cleanText(exp.company || ''),
                position: cleanText(exp.position || ''),
                duration: cleanDuration(exp.duration || ''),
                description: cleanText(exp.description || ''),
                location: cleanText(exp.location || '')
            })),
        education: (profile.education || []).map((edu)=>({
                institution: cleanText(edu.institution || ''),
                degree: cleanText(edu.degree || ''),
                field: cleanText(edu.field || ''),
                duration: cleanDuration(edu.duration || ''),
                location: cleanText(edu.location || '')
            })),
        skills: (profile.skills || []).map((skill)=>cleanText(skill)).filter((skill)=>skill.length > 0),
        certifications: (profile.certifications || []).map((cert)=>({
                name: cleanText(cert.name || ''),
                issuer: cleanText(cert.issuer || ''),
                date: cleanText(cert.date || '')
            })),
        languages: (profile.languages || []).map((lang)=>({
                language: cleanText(lang.language || ''),
                proficiency: cleanText(lang.proficiency || '')
            }))
    };
}
// Helper functions for data cleaning
function cleanText(text) {
    return text.trim().replace(/\s+/g, ' ').replace(/[""]/g, '"').replace(/['']/g, "'");
}
function cleanEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim()) ? email.trim().toLowerCase() : '';
}
function cleanPhone(phone) {
    const cleaned = phone.replace(/[^\d+\-\(\)\s]/g, '').trim();
    return cleaned.length >= 10 ? cleaned : '';
}
function cleanUrl(url) {
    if (!url) return '';
    if (url.startsWith('http')) return url;
    if (url.includes('linkedin.com')) return `https://${url}`;
    return url;
}
function cleanDuration(duration) {
    return duration.trim().replace(/\s+/g, ' ');
}
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/process [external] (process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("process", () => require("process"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/google-vision.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractTextWithGoogleVision": (()=>extractTextWithGoogleVision),
    "parseLinkedInText": (()=>parseLinkedInText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$google$2d$auth$2d$library$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/google-auth-library/build/src/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
// Google Vision API configuration
const GOOGLE_SERVICE_ACCOUNT = process.env.GOOGLE_SERVICE_ACCOUNT;
const GOOGLE_VISION_ENDPOINT = 'https://vision.googleapis.com/v1/images:annotate';
const CREDENTIALS_PATH = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'google-credentials.json');
async function extractTextWithGoogleVision(imageBase64) {
    try {
        console.log('Starting Google Vision API text extraction with service account...');
        // Create Google Auth client using credentials file
        const auth = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$google$2d$auth$2d$library$2f$build$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleAuth"]({
            keyFile: CREDENTIALS_PATH,
            scopes: [
                'https://www.googleapis.com/auth/cloud-platform'
            ]
        });
        // Get access token
        const authClient = await auth.getClient();
        const accessToken = await authClient.getAccessToken();
        if (!accessToken.token) {
            throw new Error('Failed to get access token');
        }
        console.log('Successfully obtained access token');
        const requestBody = {
            requests: [
                {
                    image: {
                        content: imageBase64
                    },
                    features: [
                        {
                            type: 'TEXT_DETECTION',
                            maxResults: 1
                        },
                        {
                            type: 'DOCUMENT_TEXT_DETECTION',
                            maxResults: 1
                        }
                    ]
                }
            ]
        };
        const response = await fetch(GOOGLE_VISION_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken.token}`
            },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Google Vision API error response:', errorText);
            throw new Error(`Google Vision API error: ${response.status} ${response.statusText} - ${errorText}`);
        }
        const data = await response.json();
        console.log('Google Vision API response received');
        if (data.responses[0]?.error) {
            throw new Error(`Google Vision API error: ${data.responses[0].error.message}`);
        }
        // Get the full text from the response
        const fullText = data.responses[0]?.fullTextAnnotation?.text || data.responses[0]?.textAnnotations?.[0]?.description || '';
        console.log('Extracted text length:', fullText.length);
        console.log('Text preview:', fullText.substring(0, 200) + '...');
        return fullText;
    } catch (error) {
        console.error('Google Vision API error:', error);
        throw new Error(`Failed to extract text with Google Vision: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
function parseLinkedInText(extractedText) {
    console.log('Parsing LinkedIn text...');
    // Split text into lines for easier parsing
    const lines = extractedText.split('\n').map((line)=>line.trim()).filter((line)=>line.length > 0);
    // Initialize profile structure
    const profile = {
        personalInfo: {
            name: '',
            title: '',
            location: '',
            email: '',
            phone: '',
            linkedin: ''
        },
        summary: '',
        experience: [],
        education: [],
        skills: [],
        certifications: [],
        languages: []
    };
    // Parse personal information (usually at the top)
    if (lines.length > 0) {
        // First non-empty line is usually the name
        profile.personalInfo.name = lines[0];
        // Look for title/headline (usually second line or after name)
        if (lines.length > 1) {
            profile.personalInfo.title = lines[1];
        }
        // Look for location (often contains city, state, country)
        for(let i = 0; i < Math.min(5, lines.length); i++){
            const line = lines[i].toLowerCase();
            if (line.includes(',') && (line.includes('ca') || line.includes('ny') || line.includes('usa') || line.includes('united states') || line.includes('san francisco') || line.includes('new york'))) {
                profile.personalInfo.location = lines[i];
                break;
            }
        }
    }
    // Look for email addresses
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
    const emailMatch = extractedText.match(emailRegex);
    if (emailMatch) {
        profile.personalInfo.email = emailMatch[0];
    }
    // Look for phone numbers
    const phoneRegex = /(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
    const phoneMatch = extractedText.match(phoneRegex);
    if (phoneMatch) {
        profile.personalInfo.phone = phoneMatch[0];
    }
    // Look for LinkedIn URL
    const linkedinRegex = /linkedin\.com\/in\/[a-zA-Z0-9-]+/g;
    const linkedinMatch = extractedText.match(linkedinRegex);
    if (linkedinMatch) {
        profile.personalInfo.linkedin = 'https://' + linkedinMatch[0];
    }
    // Look for About/Summary section
    const aboutIndex = extractedText.toLowerCase().indexOf('about');
    if (aboutIndex !== -1) {
        const aboutSection = extractedText.substring(aboutIndex);
        const nextSectionIndex = aboutSection.toLowerCase().search(/(experience|education|skills)/i);
        if (nextSectionIndex !== -1) {
            profile.summary = aboutSection.substring(5, nextSectionIndex).trim();
        } else {
            profile.summary = aboutSection.substring(5, Math.min(500, aboutSection.length)).trim();
        }
    }
    // Parse Experience section
    const experienceIndex = extractedText.toLowerCase().indexOf('experience');
    if (experienceIndex !== -1) {
        const experienceSection = extractedText.substring(experienceIndex);
        const nextSectionIndex = experienceSection.toLowerCase().search(/(education|skills|certifications)/i);
        const expText = nextSectionIndex !== -1 ? experienceSection.substring(10, nextSectionIndex) : experienceSection.substring(10, Math.min(1000, experienceSection.length));
        // Simple parsing - look for company patterns
        const expLines = expText.split('\n').filter((line)=>line.trim().length > 0);
        let currentExp = null;
        for (const line of expLines){
            if (line.includes('·') || line.includes('•') || /\d{4}/.test(line)) {
                if (currentExp) {
                    profile.experience.push(currentExp);
                }
                currentExp = {
                    company: '',
                    position: line.trim(),
                    duration: '',
                    description: '',
                    location: ''
                };
            } else if (currentExp && line.trim().length > 0) {
                if (!currentExp.company) {
                    currentExp.company = line.trim();
                } else {
                    currentExp.description += line.trim() + ' ';
                }
            }
        }
        if (currentExp) {
            profile.experience.push(currentExp);
        }
    }
    // Parse Education section
    const educationIndex = extractedText.toLowerCase().indexOf('education');
    if (educationIndex !== -1) {
        const educationSection = extractedText.substring(educationIndex);
        const nextSectionIndex = educationSection.toLowerCase().search(/(skills|certifications|languages)/i);
        const eduText = nextSectionIndex !== -1 ? educationSection.substring(9, nextSectionIndex) : educationSection.substring(9, Math.min(500, educationSection.length));
        const eduLines = eduText.split('\n').filter((line)=>line.trim().length > 0);
        if (eduLines.length > 0) {
            profile.education.push({
                institution: eduLines[0] || '',
                degree: eduLines[1] || '',
                field: eduLines[2] || '',
                duration: eduLines.find((line)=>/\d{4}/.test(line)) || '',
                location: ''
            });
        }
    }
    // Parse Skills section
    const skillsIndex = extractedText.toLowerCase().indexOf('skills');
    if (skillsIndex !== -1) {
        const skillsSection = extractedText.substring(skillsIndex);
        const nextSectionIndex = skillsSection.toLowerCase().search(/(certifications|languages|recommendations)/i);
        const skillsText = nextSectionIndex !== -1 ? skillsSection.substring(6, nextSectionIndex) : skillsSection.substring(6, Math.min(300, skillsSection.length));
        // Split by common delimiters and clean up
        const skillsList = skillsText.split(/[,•·\n]/).map((skill)=>skill.trim()).filter((skill)=>skill.length > 0 && skill.length < 50).slice(0, 20) // Limit to 20 skills
        ;
        profile.skills = skillsList;
    }
    console.log('Parsing completed:', {
        name: profile.personalInfo.name,
        title: profile.personalInfo.title,
        experienceCount: profile.experience.length,
        skillsCount: profile.skills.length
    });
    return profile;
}
}}),
"[project]/src/lib/demo-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "demoLinkedInProfile": (()=>demoLinkedInProfile),
    "simulateAIProcessing": (()=>simulateAIProcessing)
});
const demoLinkedInProfile = {
    personalInfo: {
        name: "Alex Johnson",
        title: "Senior Software Engineer",
        location: "San Francisco, CA",
        email: "<EMAIL>",
        linkedin: "linkedin.com/in/alexjohnson"
    },
    summary: "Experienced software engineer with 5+ years of expertise in full-stack development, cloud architecture, and team leadership. Passionate about building scalable applications and mentoring junior developers. Proven track record of delivering high-quality software solutions in fast-paced startup environments.",
    experience: [
        {
            company: "TechCorp Inc.",
            position: "Senior Software Engineer",
            duration: "Jan 2022 - Present",
            description: "Lead development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 60%. Mentored 3 junior developers and conducted technical interviews.",
            location: "San Francisco, CA"
        },
        {
            company: "StartupXYZ",
            position: "Full Stack Developer",
            duration: "Mar 2020 - Dec 2021",
            description: "Built responsive web applications using React and Node.js. Designed and implemented RESTful APIs. Collaborated with product team to define technical requirements and user stories.",
            location: "Remote"
        },
        {
            company: "Digital Solutions Ltd",
            position: "Junior Developer",
            duration: "Jun 2019 - Feb 2020",
            description: "Developed and maintained client websites using HTML, CSS, JavaScript, and PHP. Participated in code reviews and agile development processes. Fixed bugs and implemented new features.",
            location: "New York, NY"
        }
    ],
    education: [
        {
            institution: "University of California, Berkeley",
            degree: "Bachelor of Science",
            field: "Computer Science",
            duration: "2015 - 2019",
            location: "Berkeley, CA"
        }
    ],
    skills: [
        "JavaScript",
        "TypeScript",
        "React",
        "Node.js",
        "Python",
        "AWS",
        "Docker",
        "Kubernetes",
        "PostgreSQL",
        "MongoDB",
        "Git",
        "Agile/Scrum",
        "System Design",
        "API Development",
        "Microservices",
        "CI/CD"
    ],
    certifications: [
        {
            name: "AWS Certified Solutions Architect",
            issuer: "Amazon Web Services",
            date: "2023"
        },
        {
            name: "Certified Kubernetes Administrator",
            issuer: "Cloud Native Computing Foundation",
            date: "2022"
        }
    ],
    languages: [
        {
            language: "English",
            proficiency: "Native"
        },
        {
            language: "Spanish",
            proficiency: "Conversational"
        }
    ]
};
function simulateAIProcessing() {
    return new Promise((resolve)=>{
        setTimeout(()=>{
            resolve(demoLinkedInProfile);
        }, 2000) // 2 second delay to simulate processing
        ;
    });
}
}}),
"[project]/src/app/api/process-linkedin/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OPTIONS": (()=>OPTIONS),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$google$2d$vision$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/google-vision.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/demo-data.ts [app-route] (ecmascript)");
;
;
;
;
async function POST(request) {
    try {
        // Check if APIs are available
        const hasGoogleVision = true // Using credentials file
        ;
        const hasAIMLKey = !!process.env.AIML_API_KEY;
        console.log('API Keys available:', {
            googleVision: hasGoogleVision,
            aiml: hasAIMLKey
        });
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        // Validate file type
        if (!file.type.startsWith('image/')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File must be an image'
            }, {
                status: 400
            });
        }
        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File size must be less than 10MB'
            }, {
                status: 400
            });
        }
        let profileData;
        // Convert file to base64
        const arrayBuffer = await file.arrayBuffer();
        const base64 = Buffer.from(arrayBuffer).toString('base64');
        console.log('File type:', file.type);
        console.log('File size:', file.size);
        console.log('Base64 length:', base64.length);
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('Using Google Vision API with service account for text extraction...');
            try {
                // Extract text using Google Vision API
                const extractedText = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$google$2d$vision$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractTextWithGoogleVision"])(base64);
                console.log('Google Vision text extraction successful!');
                // Parse the extracted text into LinkedIn profile structure
                profileData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$google$2d$vision$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseLinkedInText"])(extractedText);
                console.log('LinkedIn text parsing completed!');
            } catch (googleError) {
                console.error('Google Vision API failed:', googleError);
                // Fallback to AIML API if available
                if (hasAIMLKey) {
                    console.log('Falling back to AIML API...');
                    try {
                        profileData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["processLinkedInScreenshot"])(base64);
                        console.log('AIML API fallback successful!');
                    } catch (aimlError) {
                        console.error('AIML API also failed, using demo data:', aimlError);
                        profileData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["simulateAIProcessing"])();
                    }
                } else {
                    console.log('No AIML fallback available, using demo data');
                    profileData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["simulateAIProcessing"])();
                }
            }
        } else {
            "TURBOPACK unreachable";
        }
        // Validate the extracted data
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateProfileData"])(profileData)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Could not extract sufficient profile information from the image'
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: profileData
        });
    } catch (error) {
        console.error('Error in process-linkedin API:', error);
        // Fallback to demo data if AI processing fails
        console.log('Falling back to demo data due to AI processing error');
        try {
            const demoData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["simulateAIProcessing"])();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: demoData,
                warning: 'AI processing failed, showing demo data. Please check your API configuration.'
            });
        } catch (demoError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error instanceof Error ? error.message : 'Failed to process LinkedIn screenshot',
                details: ("TURBOPACK compile-time truthy", 1) ? error : ("TURBOPACK unreachable", undefined)
            }, {
                status: 500
            });
        }
    }
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__c1a8ada7._.js.map