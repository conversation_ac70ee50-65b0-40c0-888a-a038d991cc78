@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;700;800&family=Space+Grotesk:wght@400;500;700;800&display=swap');
@import "tailwindcss";

:root {
  /* Neobrutalism Color Palette */
  --neo-black: #000000;
  --neo-white: #ffffff;
  --neo-yellow: #ffff00;
  --neo-pink: #ff00ff;
  --neo-cyan: #00ffff;
  --neo-green: #00ff00;
  --neo-red: #ff0000;
  --neo-blue: #0000ff;
  --neo-orange: #ff8000;
  --neo-purple: #8000ff;

  /* Shadows */
  --neo-shadow: 4px 4px 0px var(--neo-black);
  --neo-shadow-lg: 8px 8px 0px var(--neo-black);
  --neo-shadow-xl: 12px 12px 0px var(--neo-black);
}

body {
  font-family: 'Space Grotesk', sans-serif;
  background: var(--neo-white);
  color: var(--neo-black);
  line-height: 1.4;
  margin: 0;
  padding: 0;
}

/* Neobrutalism Base Styles */
.neo-border {
  border: 3px solid var(--neo-black);
}

.neo-border-thick {
  border: 5px solid var(--neo-black);
}

.neo-shadow {
  box-shadow: var(--neo-shadow);
}

.neo-shadow-lg {
  box-shadow: var(--neo-shadow-lg);
}

.neo-shadow-xl {
  box-shadow: var(--neo-shadow-xl);
}

.neo-button {
  border: 3px solid var(--neo-black);
  box-shadow: var(--neo-shadow);
  font-weight: 700;
  padding: 12px 24px;
  background: var(--neo-white);
  color: var(--neo-black);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 150ms ease;
  cursor: pointer;
  font-family: 'Space Grotesk', sans-serif;
}

.neo-button:hover {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0px var(--neo-black);
}

.neo-button:active {
  transform: translate(4px, 4px);
  box-shadow: 0px 0px 0px var(--neo-black);
}

.neo-card {
  border: 3px solid var(--neo-black);
  box-shadow: var(--neo-shadow-lg);
  background: var(--neo-white);
  padding: 24px;
}

.neo-input {
  border: 3px solid var(--neo-black);
  padding: 12px 16px;
  background: var(--neo-white);
  color: var(--neo-black);
  font-weight: 500;
  font-family: 'Space Grotesk', sans-serif;
  box-shadow: 3px 3px 0px var(--neo-black);
  transition: all 150ms ease;
}

.neo-input:focus {
  outline: none;
  transform: translate(2px, 2px);
  box-shadow: 1px 1px 0px var(--neo-black);
}
