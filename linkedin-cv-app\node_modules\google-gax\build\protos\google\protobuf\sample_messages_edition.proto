// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd
//
// Sample messages to generate example code.

edition = "2023";

package protobuf_test_messages.edition;

import "google/protobuf/cpp_features.proto";

option optimize_for = SPEED;
option features.(pb.cpp).string_type = VIEW;

// This proto includes every type of field in both singular and repeated
// forms.
//
// Also, crucially, all messages and enums in this file are eventually
// submessages of this message.  So for example, a fuzz test of TestAllTypes
// could trigger bugs that occur in any message type in this file.  We verify
// this stays true in a unit test.
message TestAllTypesEdition {
  message NestedMessage {
    int32 a = 1;
    TestAllTypesEdition corecursive = 2;
  }

  enum NestedEnum {
    FOO = 0;
    BAR = 1;
    BAZ = 2;
    NEG = -1;  // Intentionally negative.
  }

  // Singular
  int32 optional_int32 = 1;
  int64 optional_int64 = 2;
  uint32 optional_uint32 = 3;
  uint64 optional_uint64 = 4;
  sint32 optional_sint32 = 5;
  sint64 optional_sint64 = 6;
  fixed32 optional_fixed32 = 7;
  fixed64 optional_fixed64 = 8;
  sfixed32 optional_sfixed32 = 9;
  sfixed64 optional_sfixed64 = 10;
  float optional_float = 11;
  double optional_double = 12;
  bool optional_bool = 13;
  string optional_string = 14;
  bytes optional_bytes = 15;

  NestedMessage optional_nested_message = 18;
  ForeignMessageEdition optional_foreign_message = 19;

  NestedEnum optional_nested_enum = 21;
  ForeignEnumEdition optional_foreign_enum = 22;

  string optional_string_piece = 24 [ctype = STRING_PIECE];
  string optional_cord = 25 [ctype = CORD];

  TestAllTypesEdition recursive_message = 27;

  // Repeated
  repeated int32 repeated_int32 = 31;
  repeated int64 repeated_int64 = 32;
  repeated uint32 repeated_uint32 = 33;
  repeated uint64 repeated_uint64 = 34;
  repeated sint32 repeated_sint32 = 35;
  repeated sint64 repeated_sint64 = 36;
  repeated fixed32 repeated_fixed32 = 37;
  repeated fixed64 repeated_fixed64 = 38;
  repeated sfixed32 repeated_sfixed32 = 39;
  repeated sfixed64 repeated_sfixed64 = 40;
  repeated float repeated_float = 41;
  repeated double repeated_double = 42;
  repeated bool repeated_bool = 43;
  repeated string repeated_string = 44;
  repeated bytes repeated_bytes = 45;

  repeated NestedMessage repeated_nested_message = 48;
  repeated ForeignMessageEdition repeated_foreign_message = 49;

  repeated NestedEnum repeated_nested_enum = 51;
  repeated ForeignEnumEdition repeated_foreign_enum = 52;

  repeated string repeated_string_piece = 54 [ctype = STRING_PIECE];
  repeated string repeated_cord = 55 [ctype = CORD];

  // Packed
  repeated int32 packed_int32 = 75 [features.repeated_field_encoding = PACKED];
  repeated int64 packed_int64 = 76 [features.repeated_field_encoding = PACKED];
  repeated uint32 packed_uint32 = 77
      [features.repeated_field_encoding = PACKED];
  repeated uint64 packed_uint64 = 78
      [features.repeated_field_encoding = PACKED];
  repeated sint32 packed_sint32 = 79
      [features.repeated_field_encoding = PACKED];
  repeated sint64 packed_sint64 = 80
      [features.repeated_field_encoding = PACKED];
  repeated fixed32 packed_fixed32 = 81
      [features.repeated_field_encoding = PACKED];
  repeated fixed64 packed_fixed64 = 82
      [features.repeated_field_encoding = PACKED];
  repeated sfixed32 packed_sfixed32 = 83
      [features.repeated_field_encoding = PACKED];
  repeated sfixed64 packed_sfixed64 = 84
      [features.repeated_field_encoding = PACKED];
  repeated float packed_float = 85 [features.repeated_field_encoding = PACKED];
  repeated double packed_double = 86
      [features.repeated_field_encoding = PACKED];
  repeated bool packed_bool = 87 [features.repeated_field_encoding = PACKED];
  repeated NestedEnum packed_nested_enum = 88
      [features.repeated_field_encoding = PACKED];

  // Unpacked
  repeated int32 unpacked_int32 = 89
      [features.repeated_field_encoding = EXPANDED];
  repeated int64 unpacked_int64 = 90
      [features.repeated_field_encoding = EXPANDED];
  repeated uint32 unpacked_uint32 = 91
      [features.repeated_field_encoding = EXPANDED];
  repeated uint64 unpacked_uint64 = 92
      [features.repeated_field_encoding = EXPANDED];
  repeated sint32 unpacked_sint32 = 93
      [features.repeated_field_encoding = EXPANDED];
  repeated sint64 unpacked_sint64 = 94
      [features.repeated_field_encoding = EXPANDED];
  repeated fixed32 unpacked_fixed32 = 95
      [features.repeated_field_encoding = EXPANDED];
  repeated fixed64 unpacked_fixed64 = 96
      [features.repeated_field_encoding = EXPANDED];
  repeated sfixed32 unpacked_sfixed32 = 97
      [features.repeated_field_encoding = EXPANDED];
  repeated sfixed64 unpacked_sfixed64 = 98
      [features.repeated_field_encoding = EXPANDED];
  repeated float unpacked_float = 99
      [features.repeated_field_encoding = EXPANDED];
  repeated double unpacked_double = 100
      [features.repeated_field_encoding = EXPANDED];
  repeated bool unpacked_bool = 101
      [features.repeated_field_encoding = EXPANDED];
  repeated NestedEnum unpacked_nested_enum = 102
      [features.repeated_field_encoding = EXPANDED];

  // Map
  map<int32, int32> map_int32_int32 = 56;
  map<int64, int64> map_int64_int64 = 57;
  map<uint32, uint32> map_uint32_uint32 = 58;
  map<uint64, uint64> map_uint64_uint64 = 59;
  map<sint32, sint32> map_sint32_sint32 = 60;
  map<sint64, sint64> map_sint64_sint64 = 61;
  map<fixed32, fixed32> map_fixed32_fixed32 = 62;
  map<fixed64, fixed64> map_fixed64_fixed64 = 63;
  map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 64;
  map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 65;
  map<int32, float> map_int32_float = 66;
  map<int32, double> map_int32_double = 67;
  map<bool, bool> map_bool_bool = 68;
  map<string, string> map_string_string = 69;
  map<string, bytes> map_string_bytes = 70;
  map<string, NestedMessage> map_string_nested_message = 71;
  map<string, ForeignMessageEdition> map_string_foreign_message = 72;
  map<string, NestedEnum> map_string_nested_enum = 73;
  map<string, ForeignEnumEdition> map_string_foreign_enum = 74;

  oneof oneof_field {
    uint32 oneof_uint32 = 111;
    NestedMessage oneof_nested_message = 112;
    string oneof_string = 113;
    bytes oneof_bytes = 114;
    bool oneof_bool = 115;
    uint64 oneof_uint64 = 116;
    float oneof_float = 117;
    double oneof_double = 118;
    NestedEnum oneof_enum = 119;
  }

  // extensions
  extensions 120 to 200;

  // groups
  message Data {
    int32 group_int32 = 202;
    uint32 group_uint32 = 203;
  }

  Data data = 201 [features.message_encoding = DELIMITED];

  // default values
  int32 default_int32 = 241 [default = -123456789];
  int64 default_int64 = 242 [default = -9123456789123456789];
  uint32 default_uint32 = 243 [default = 2123456789];
  uint64 default_uint64 = 244 [default = 10123456789123456789];
  sint32 default_sint32 = 245 [default = -123456789];
  sint64 default_sint64 = 246 [default = -9123456789123456789];
  fixed32 default_fixed32 = 247 [default = 2123456789];
  fixed64 default_fixed64 = 248 [default = 10123456789123456789];
  sfixed32 default_sfixed32 = 249 [default = -123456789];
  sfixed64 default_sfixed64 = 250 [default = -9123456789123456789];
  float default_float = 251 [default = 9e9];
  double default_double = 252 [default = 7e22];
  bool default_bool = 253 [default = true];
  string default_string = 254 [default = "Rosebud"];
  bytes default_bytes = 255 [default = "joshua"];

  // Test field-name-to-JSON-name convention.
  // (protobuf says names can be any valid C/C++ identifier.)
  int32 fieldname1 = 401;
  int32 field_name2 = 402;
  int32 _field_name3 = 403;
  int32 field__name4_ = 404;
  int32 field0name5 = 405;
  int32 field_0_name6 = 406;
  int32 fieldName7 = 407;
  int32 FieldName8 = 408;
  int32 field_Name9 = 409;
  int32 Field_Name10 = 410;
  int32 FIELD_NAME11 = 411;
  int32 FIELD_name12 = 412;
  int32 __field_name13 = 413;
  int32 __Field_name14 = 414;
  int32 field__name15 = 415;
  int32 field__Name16 = 416;
  int32 field_name17__ = 417;
  int32 Field_name18__ = 418;

  // Reserved for unknown fields test.
  reserved 1000 to 9999;

  // message_set test case.
  message MessageSetCorrect {
    option message_set_wire_format = true;

    extensions 4 to max;
  }

  message MessageSetCorrectExtension1 {
    extend MessageSetCorrect {
      MessageSetCorrectExtension1 message_set_extension = 1547769;
    }
    string str = 25;
  }

  message MessageSetCorrectExtension2 {
    extend MessageSetCorrect {
      MessageSetCorrectExtension2 message_set_extension = 4135312;
    }
    int32 i = 9;
  }
}

message ForeignMessageEdition {
  int32 c = 1;
}

enum ForeignEnumEdition {
  FOREIGN_FOO = 0;
  FOREIGN_BAR = 1;
  FOREIGN_BAZ = 2;
}

extend TestAllTypesEdition {
  int32 extension_int32 = 120;
}

message UnknownToTestAllTypes {
  int32 optional_int32 = 1001;
  string optional_string = 1002;
  ForeignMessageEdition nested_message = 1003;
  message OptionalGroup {
    int32 a = 1;
  }
  OptionalGroup optionalgroup = 1004 [features.message_encoding = DELIMITED];
  bool optional_bool = 1006;
  repeated int32 repeated_int32 = 1011;
}

message NullHypothesisEdition {}

message EnumOnlyEdition {
  enum Bool {
    kFalse = 0;
    kTrue = 1;
  }
}

message OneStringEdition {
  string data = 1;
}

message ProtoWithKeywords {
  int32 inline = 1;
  string concept = 2;
  repeated string requires = 3;
}

message TestAllRequiredTypesEdition {
  message NestedMessage {
    int32 a = 1 [features.field_presence = LEGACY_REQUIRED];
    TestAllRequiredTypesEdition corecursive = 2
        [features.field_presence = LEGACY_REQUIRED];
    TestAllRequiredTypesEdition optional_corecursive = 3;
  }

  enum NestedEnum {
    FOO = 0;
    BAR = 1;
    BAZ = 2;
    NEG = -1;  // Intentionally negative.
  }

  // Singular
  int32 required_int32 = 1 [features.field_presence = LEGACY_REQUIRED];
  int64 required_int64 = 2 [features.field_presence = LEGACY_REQUIRED];
  uint32 required_uint32 = 3 [features.field_presence = LEGACY_REQUIRED];
  uint64 required_uint64 = 4 [features.field_presence = LEGACY_REQUIRED];
  sint32 required_sint32 = 5 [features.field_presence = LEGACY_REQUIRED];
  sint64 required_sint64 = 6 [features.field_presence = LEGACY_REQUIRED];
  fixed32 required_fixed32 = 7 [features.field_presence = LEGACY_REQUIRED];
  fixed64 required_fixed64 = 8 [features.field_presence = LEGACY_REQUIRED];
  sfixed32 required_sfixed32 = 9 [features.field_presence = LEGACY_REQUIRED];
  sfixed64 required_sfixed64 = 10 [features.field_presence = LEGACY_REQUIRED];
  float required_float = 11 [features.field_presence = LEGACY_REQUIRED];
  double required_double = 12 [features.field_presence = LEGACY_REQUIRED];
  bool required_bool = 13 [features.field_presence = LEGACY_REQUIRED];
  string required_string = 14 [features.field_presence = LEGACY_REQUIRED];
  bytes required_bytes = 15 [features.field_presence = LEGACY_REQUIRED];

  NestedMessage required_nested_message = 18
      [features.field_presence = LEGACY_REQUIRED];
  ForeignMessageEdition required_foreign_message = 19
      [features.field_presence = LEGACY_REQUIRED];

  NestedEnum required_nested_enum = 21
      [features.field_presence = LEGACY_REQUIRED];
  ForeignEnumEdition required_foreign_enum = 22
      [features.field_presence = LEGACY_REQUIRED];

  string required_string_piece = 24
      [ctype = STRING_PIECE, features.field_presence = LEGACY_REQUIRED];
  string required_cord = 25
      [ctype = CORD, features.field_presence = LEGACY_REQUIRED];

  TestAllRequiredTypesEdition recursive_message = 27;
  TestAllRequiredTypesEdition optional_recursive_message = 28;

  // extensions
  extensions 120 to 200;

  // groups
  message Data {
    int32 group_int32 = 202 [features.field_presence = LEGACY_REQUIRED];
    uint32 group_uint32 = 203 [features.field_presence = LEGACY_REQUIRED];
  }

  Data data = 201 [features.message_encoding = DELIMITED];

  // default values
  int32 default_int32 = 241
      [default = -123456789, features.field_presence = LEGACY_REQUIRED];
  int64 default_int64 = 242 [
    default = -9123456789123456789,
    features.field_presence = LEGACY_REQUIRED
  ];
  uint32 default_uint32 = 243
      [default = 2123456789, features.field_presence = LEGACY_REQUIRED];
  uint64 default_uint64 = 244 [
    default = 10123456789123456789,
    features.field_presence = LEGACY_REQUIRED
  ];
  sint32 default_sint32 = 245
      [default = -123456789, features.field_presence = LEGACY_REQUIRED];
  sint64 default_sint64 = 246 [
    default = -9123456789123456789,
    features.field_presence = LEGACY_REQUIRED
  ];
  fixed32 default_fixed32 = 247
      [default = 2123456789, features.field_presence = LEGACY_REQUIRED];
  fixed64 default_fixed64 = 248 [
    default = 10123456789123456789,
    features.field_presence = LEGACY_REQUIRED
  ];
  sfixed32 default_sfixed32 = 249
      [default = -123456789, features.field_presence = LEGACY_REQUIRED];
  sfixed64 default_sfixed64 = 250 [
    default = -9123456789123456789,
    features.field_presence = LEGACY_REQUIRED
  ];
  float default_float = 251
      [default = 9e9, features.field_presence = LEGACY_REQUIRED];
  double default_double = 252
      [default = 7e22, features.field_presence = LEGACY_REQUIRED];
  bool default_bool = 253
      [default = true, features.field_presence = LEGACY_REQUIRED];
  string default_string = 254
      [default = "Rosebud", features.field_presence = LEGACY_REQUIRED];
  bytes default_bytes = 255
      [default = "joshua", features.field_presence = LEGACY_REQUIRED];

  // Reserved for unknown fields test.
  reserved 1000 to 9999;

  // message_set test case.
  message MessageSetCorrect {
    option message_set_wire_format = true;

    extensions 4 to max;
  }

  message MessageSetCorrectExtension1 {
    extend MessageSetCorrect {
      MessageSetCorrectExtension1 message_set_extension = 1547769;
    }
    string str = 25 [features.field_presence = LEGACY_REQUIRED];
  }

  message MessageSetCorrectExtension2 {
    extend MessageSetCorrect {
      MessageSetCorrectExtension2 message_set_extension = 4135312;
    }
    int32 i = 9 [features.field_presence = LEGACY_REQUIRED];
  }
}
