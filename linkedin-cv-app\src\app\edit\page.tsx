'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { LinkedInProfile } from '@/lib/openai'

export default function EditCV() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [profile, setProfile] = useState<LinkedInProfile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get profile data from localStorage or URL params
    const savedProfile = localStorage.getItem('extractedProfile')
    if (savedProfile) {
      setProfile(JSON.parse(savedProfile))
    }
    setLoading(false)
  }, [])

  const updatePersonalInfo = (field: string, value: string) => {
    if (!profile) return
    setProfile({
      ...profile,
      personalInfo: {
        ...profile.personalInfo,
        [field]: value
      }
    })
  }

  const updateExperience = (index: number, field: string, value: string) => {
    if (!profile) return
    const newExperience = [...profile.experience]
    newExperience[index] = {
      ...newExperience[index],
      [field]: value
    }
    setProfile({
      ...profile,
      experience: newExperience
    })
  }

  const addExperience = () => {
    if (!profile) return
    setProfile({
      ...profile,
      experience: [
        ...profile.experience,
        {
          company: '',
          position: '',
          duration: '',
          description: '',
          location: ''
        }
      ]
    })
  }

  const removeExperience = (index: number) => {
    if (!profile) return
    const newExperience = profile.experience.filter((_, i) => i !== index)
    setProfile({
      ...profile,
      experience: newExperience
    })
  }

  const updateEducation = (index: number, field: string, value: string) => {
    if (!profile) return
    const newEducation = [...profile.education]
    newEducation[index] = {
      ...newEducation[index],
      [field]: value
    }
    setProfile({
      ...profile,
      education: newEducation
    })
  }

  const addEducation = () => {
    if (!profile) return
    setProfile({
      ...profile,
      education: [
        ...profile.education,
        {
          institution: '',
          degree: '',
          field: '',
          duration: '',
          location: ''
        }
      ]
    })
  }

  const removeEducation = (index: number) => {
    if (!profile) return
    const newEducation = profile.education.filter((_, i) => i !== index)
    setProfile({
      ...profile,
      education: newEducation
    })
  }

  const updateSkills = (skills: string[]) => {
    if (!profile) return
    setProfile({
      ...profile,
      skills
    })
  }

  const addSkill = (skill: string) => {
    if (!profile || !skill.trim()) return
    setProfile({
      ...profile,
      skills: [...profile.skills, skill.trim()]
    })
  }

  const removeSkill = (index: number) => {
    if (!profile) return
    const newSkills = profile.skills.filter((_, i) => i !== index)
    setProfile({
      ...profile,
      skills: newSkills
    })
  }

  const saveAndContinue = () => {
    if (profile) {
      localStorage.setItem('editedProfile', JSON.stringify(profile))
      router.push('/templates')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-yellow-300 flex items-center justify-center">
        <div className="text-2xl font-black">Loading...</div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-yellow-300 flex items-center justify-center">
        <div className="bg-white border-4 border-black p-8 shadow-brutal">
          <h1 className="text-2xl font-black mb-4">No Profile Data Found</h1>
          <p className="mb-4">Please upload a LinkedIn screenshot first.</p>
          <button
            onClick={() => router.push('/upload')}
            className="bg-red-500 text-white px-6 py-3 border-4 border-black font-black hover:bg-red-600 transition-colors shadow-brutal"
          >
            GO TO UPLOAD
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-yellow-300 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white border-4 border-black p-6 mb-6 shadow-brutal">
          <h1 className="text-4xl font-black mb-2">EDIT YOUR CV</h1>
          <p className="text-lg font-bold">Review and edit the extracted information</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Edit Form */}
          <div className="space-y-6">
            {/* Personal Information */}
            <div className="bg-white border-4 border-black p-6 shadow-brutal">
              <h2 className="text-2xl font-black mb-4 text-blue-600">PERSONAL INFO</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-black mb-2">FULL NAME</label>
                  <input
                    type="text"
                    value={profile.personalInfo.name}
                    onChange={(e) => updatePersonalInfo('name', e.target.value)}
                    className="w-full p-3 border-4 border-black font-bold text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
                    placeholder="Your full name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-black mb-2">PROFESSIONAL TITLE</label>
                  <input
                    type="text"
                    value={profile.personalInfo.title}
                    onChange={(e) => updatePersonalInfo('title', e.target.value)}
                    className="w-full p-3 border-4 border-black font-bold text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
                    placeholder="e.g., Senior Software Engineer"
                  />
                </div>

                <div>
                  <label className="block text-sm font-black mb-2">LOCATION</label>
                  <input
                    type="text"
                    value={profile.personalInfo.location}
                    onChange={(e) => updatePersonalInfo('location', e.target.value)}
                    className="w-full p-3 border-4 border-black font-bold text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
                    placeholder="City, State/Country"
                  />
                </div>

                <div>
                  <label className="block text-sm font-black mb-2">EMAIL</label>
                  <input
                    type="email"
                    value={profile.personalInfo.email}
                    onChange={(e) => updatePersonalInfo('email', e.target.value)}
                    className="w-full p-3 border-4 border-black font-bold text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-black mb-2">PHONE</label>
                  <input
                    type="tel"
                    value={profile.personalInfo.phone}
                    onChange={(e) => updatePersonalInfo('phone', e.target.value)}
                    className="w-full p-3 border-4 border-black font-bold text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
                    placeholder="+****************"
                  />
                </div>

                <div>
                  <label className="block text-sm font-black mb-2">LINKEDIN URL</label>
                  <input
                    type="url"
                    value={profile.personalInfo.linkedin}
                    onChange={(e) => updatePersonalInfo('linkedin', e.target.value)}
                    className="w-full p-3 border-4 border-black font-bold text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
                    placeholder="https://linkedin.com/in/yourprofile"
                  />
                </div>
              </div>
            </div>

            {/* Summary */}
            <div className="bg-white border-4 border-black p-6 shadow-brutal">
              <h2 className="text-2xl font-black mb-4 text-green-600">PROFESSIONAL SUMMARY</h2>
              <textarea
                value={profile.summary}
                onChange={(e) => setProfile({ ...profile, summary: e.target.value })}
                rows={4}
                className="w-full p-3 border-4 border-black font-bold text-lg focus:outline-none focus:ring-4 focus:ring-green-300 resize-none"
                placeholder="Write a compelling professional summary..."
              />
            </div>

            {/* Experience */}
            <div className="bg-white border-4 border-black p-6 shadow-brutal">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-black text-red-600">WORK EXPERIENCE</h2>
                <button
                  onClick={addExperience}
                  className="bg-red-500 text-white px-4 py-2 border-2 border-black font-black text-sm hover:bg-red-600 transition-colors"
                >
                  + ADD JOB
                </button>
              </div>

              <div className="space-y-6">
                {profile.experience.map((exp, index) => (
                  <div key={index} className="border-2 border-gray-300 p-4 relative">
                    <button
                      onClick={() => removeExperience(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white w-6 h-6 border border-black font-black text-xs hover:bg-red-600"
                    >
                      ×
                    </button>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs font-black mb-1">JOB TITLE</label>
                        <input
                          type="text"
                          value={exp.position}
                          onChange={(e) => updateExperience(index, 'position', e.target.value)}
                          className="w-full p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-red-300"
                          placeholder="e.g., Senior Software Engineer"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-black mb-1">COMPANY</label>
                        <input
                          type="text"
                          value={exp.company}
                          onChange={(e) => updateExperience(index, 'company', e.target.value)}
                          className="w-full p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-red-300"
                          placeholder="e.g., Tech Solutions Inc."
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-black mb-1">DURATION</label>
                        <input
                          type="text"
                          value={exp.duration}
                          onChange={(e) => updateExperience(index, 'duration', e.target.value)}
                          className="w-full p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-red-300"
                          placeholder="e.g., Jan 2020 - Present"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-black mb-1">LOCATION</label>
                        <input
                          type="text"
                          value={exp.location}
                          onChange={(e) => updateExperience(index, 'location', e.target.value)}
                          className="w-full p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-red-300"
                          placeholder="e.g., San Francisco, CA"
                        />
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="block text-xs font-black mb-1">JOB DESCRIPTION</label>
                      <textarea
                        value={exp.description}
                        onChange={(e) => updateExperience(index, 'description', e.target.value)}
                        rows={3}
                        className="w-full p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-red-300 resize-none"
                        placeholder="Describe your key responsibilities and achievements..."
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Education */}
            <div className="bg-white border-4 border-black p-6 shadow-brutal">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-black text-purple-600">EDUCATION</h2>
                <button
                  onClick={addEducation}
                  className="bg-purple-500 text-white px-4 py-2 border-2 border-black font-black text-sm hover:bg-purple-600 transition-colors"
                >
                  + ADD EDUCATION
                </button>
              </div>

              <div className="space-y-6">
                {profile.education.map((edu, index) => (
                  <div key={index} className="border-2 border-gray-300 p-4 relative">
                    <button
                      onClick={() => removeEducation(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white w-6 h-6 border border-black font-black text-xs hover:bg-red-600"
                    >
                      ×
                    </button>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs font-black mb-1">INSTITUTION</label>
                        <input
                          type="text"
                          value={edu.institution}
                          onChange={(e) => updateEducation(index, 'institution', e.target.value)}
                          className="w-full p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-purple-300"
                          placeholder="e.g., University of California"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-black mb-1">DEGREE</label>
                        <input
                          type="text"
                          value={edu.degree}
                          onChange={(e) => updateEducation(index, 'degree', e.target.value)}
                          className="w-full p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-purple-300"
                          placeholder="e.g., Bachelor of Science"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-black mb-1">FIELD OF STUDY</label>
                        <input
                          type="text"
                          value={edu.field}
                          onChange={(e) => updateEducation(index, 'field', e.target.value)}
                          className="w-full p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-purple-300"
                          placeholder="e.g., Computer Science"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-black mb-1">DURATION</label>
                        <input
                          type="text"
                          value={edu.duration}
                          onChange={(e) => updateEducation(index, 'duration', e.target.value)}
                          className="w-full p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-purple-300"
                          placeholder="e.g., 2016 - 2020"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Skills */}
            <div className="bg-white border-4 border-black p-6 shadow-brutal">
              <h2 className="text-2xl font-black mb-4 text-orange-600">SKILLS</h2>

              <div className="mb-4">
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Add a skill..."
                    className="flex-1 p-2 border-2 border-black font-bold focus:outline-none focus:ring-2 focus:ring-orange-300"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addSkill((e.target as HTMLInputElement).value);
                        (e.target as HTMLInputElement).value = ''
                      }
                    }}
                  />
                  <button
                    onClick={(e) => {
                      const input = (e.target as HTMLElement).previousElementSibling as HTMLInputElement
                      addSkill(input.value)
                      input.value = ''
                    }}
                    className="bg-orange-500 text-white px-4 py-2 border-2 border-black font-black hover:bg-orange-600 transition-colors"
                  >
                    ADD
                  </button>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {profile.skills.map((skill, index) => (
                  <div key={index} className="bg-orange-100 border-2 border-black px-3 py-1 flex items-center gap-2">
                    <span className="font-bold text-sm">{skill}</span>
                    <button
                      onClick={() => removeSkill(index)}
                      className="bg-red-500 text-white w-4 h-4 border border-black font-black text-xs hover:bg-red-600 flex items-center justify-center"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Preview */}
          <div className="bg-white border-4 border-black p-6 shadow-brutal">
            <h2 className="text-2xl font-black mb-4 text-purple-600">LIVE PREVIEW</h2>
            
            <div className="space-y-4 text-sm">
              <div>
                <h3 className="font-black text-lg">{profile.personalInfo.name || 'Your Name'}</h3>
                <p className="font-bold text-gray-700">{profile.personalInfo.title || 'Your Title'}</p>
                <p className="text-gray-600">{profile.personalInfo.location || 'Your Location'}</p>
                {profile.personalInfo.email && (
                  <p className="text-blue-600">{profile.personalInfo.email}</p>
                )}
                {profile.personalInfo.phone && (
                  <p className="text-gray-600">{profile.personalInfo.phone}</p>
                )}
              </div>

              {profile.summary && (
                <div>
                  <h4 className="font-black text-sm mb-2">SUMMARY</h4>
                  <p className="text-gray-700 text-xs leading-relaxed">{profile.summary}</p>
                </div>
              )}

              {profile.experience.length > 0 && (
                <div>
                  <h4 className="font-black text-sm mb-2">EXPERIENCE</h4>
                  {profile.experience.slice(0, 2).map((exp, index) => (
                    <div key={index} className="mb-3">
                      <p className="font-bold text-xs">{exp.position}</p>
                      <p className="text-xs text-gray-600">{exp.company}</p>
                      <p className="text-xs text-gray-500">{exp.duration}</p>
                    </div>
                  ))}
                </div>
              )}

              {profile.skills.length > 0 && (
                <div>
                  <h4 className="font-black text-sm mb-2">SKILLS</h4>
                  <div className="flex flex-wrap gap-1">
                    {profile.skills.slice(0, 8).map((skill, index) => (
                      <span key={index} className="bg-gray-200 px-2 py-1 text-xs font-bold border border-gray-400">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Continue Button */}
        <div className="mt-8 text-center">
          <button
            onClick={saveAndContinue}
            className="bg-green-500 text-white px-8 py-4 border-4 border-black font-black text-xl hover:bg-green-600 transition-colors shadow-brutal"
          >
            SAVE & CHOOSE TEMPLATE →
          </button>
        </div>
      </div>
    </div>
  )
}
