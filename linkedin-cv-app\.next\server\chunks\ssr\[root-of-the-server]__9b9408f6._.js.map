{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Neobrutalism color utilities\nexport const neoColors = {\n  black: '#000000',\n  white: '#ffffff',\n  yellow: '#ffff00',\n  pink: '#ff00ff',\n  cyan: '#00ffff',\n  green: '#00ff00',\n  red: '#ff0000',\n  blue: '#0000ff',\n  orange: '#ff8000',\n  purple: '#8000ff',\n} as const\n\nexport type NeoColor = keyof typeof neoColors\n\n// Generate random neobrutalism color\nexport function getRandomNeoColor(): NeoColor {\n  const colors = Object.keys(neoColors) as NeoColor[]\n  return colors[Math.floor(Math.random() * colors.length)]\n}\n\n// File validation utilities\nexport function validateImageFile(file: File): boolean {\n  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']\n  const maxSize = 10 * 1024 * 1024 // 10MB\n  \n  return validTypes.includes(file.type) && file.size <= maxSize\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAKO,SAAS;IACd,MAAM,SAAS,OAAO,IAAI,CAAC;IAC3B,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAGO,SAAS,kBAAkB,IAAU;IAC1C,MAAM,aAAa;QAAC;QAAc;QAAa;QAAa;KAAa;IACzE,MAAM,UAAU,KAAK,OAAO,KAAK,OAAO;;IAExC,OAAO,WAAW,QAAQ,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI;AACxD;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn, type NeoColor, neoColors } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  color?: NeoColor\n  children: React.ReactNode\n}\n\nexport function Button({ \n  variant = 'primary', \n  size = 'md', \n  color,\n  className, \n  children, \n  ...props \n}: ButtonProps) {\n  const getVariantStyles = () => {\n    switch (variant) {\n      case 'primary':\n        return 'bg-white text-black hover:bg-yellow-400'\n      case 'secondary':\n        return 'bg-black text-white hover:bg-gray-800'\n      case 'danger':\n        return 'bg-red-500 text-white hover:bg-red-600'\n      default:\n        return 'bg-white text-black hover:bg-yellow-400'\n    }\n  }\n\n  const getSizeStyles = () => {\n    switch (size) {\n      case 'sm':\n        return 'px-4 py-2 text-sm'\n      case 'md':\n        return 'px-6 py-3 text-base'\n      case 'lg':\n        return 'px-8 py-4 text-lg'\n      default:\n        return 'px-6 py-3 text-base'\n    }\n  }\n\n  const colorStyle = color ? { backgroundColor: neoColors[color] } : {}\n\n  return (\n    <button\n      className={cn(\n        'neo-button',\n        getVariantStyles(),\n        getSizeStyles(),\n        className\n      )}\n      style={colorStyle}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AASO,SAAS,OAAO,EACrB,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,KAAK,EACL,SAAS,EACT,QAAQ,EACR,GAAG,OACS;IACZ,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,QAAQ;QAAE,iBAAiB,mHAAA,CAAA,YAAS,CAAC,MAAM;IAAC,IAAI,CAAC;IAEpE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cACA,oBACA,iBACA;QAEF,OAAO;QACN,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn, type NeoColor, neoColors } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  color?: NeoColor\n  variant?: 'default' | 'elevated' | 'flat'\n}\n\nexport function Card({ children, className, color, variant = 'default' }: CardProps) {\n  const getVariantStyles = () => {\n    switch (variant) {\n      case 'elevated':\n        return 'neo-shadow-xl'\n      case 'flat':\n        return 'shadow-none'\n      default:\n        return 'neo-shadow-lg'\n    }\n  }\n\n  const colorStyle = color ? { backgroundColor: neoColors[color] } : {}\n\n  return (\n    <div\n      className={cn(\n        'neo-card',\n        getVariantStyles(),\n        className\n      )}\n      style={colorStyle}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface CardHeaderProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function CardHeader({ children, className }: CardHeaderProps) {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface CardTitleProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function CardTitle({ children, className }: CardTitleProps) {\n  return (\n    <h3 className={cn('text-2xl font-bold uppercase tracking-wide', className)}>\n      {children}\n    </h3>\n  )\n}\n\ninterface CardContentProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function CardContent({ children, className }: CardContentProps) {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AASO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,SAAS,EAAa;IACjF,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,QAAQ;QAAE,iBAAiB,mHAAA,CAAA,YAAS,CAAC,MAAM;IAAC,IAAI,CAAC;IAEpE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,YACA,oBACA;QAEF,OAAO;kBAEN;;;;;;AAGP;AAOO,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAmB;IACjE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;AAOO,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAkB;IAC/D,qBACE,8OAAC;QAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;kBAC7D;;;;;;AAGP;AAOO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAoB;IACnE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card'\nimport { Upload, Zap, FileText, Download } from 'lucide-react'\n\nexport default function Home() {\n  const [currentStep, setCurrentStep] = useState(0)\n\n  const steps = [\n    { icon: Upload, title: 'Upload LinkedIn Screenshot', description: 'Drop your LinkedIn profile screenshot' },\n    { icon: Zap, title: 'AI Processing', description: 'Our AI extracts your professional info' },\n    { icon: FileText, title: 'CV Generation', description: 'We create a professional CV layout' },\n    { icon: Download, title: 'Download PDF', description: 'Get your polished CV instantly' }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Header */}\n      <header className=\"neo-border-thick border-t-0 border-l-0 border-r-0 bg-yellow-400 p-6\">\n        <div className=\"max-w-6xl mx-auto flex items-center justify-between\">\n          <h1 className=\"text-3xl font-bold uppercase tracking-wide\">\n            LinkedIn AI CV Builder\n          </h1>\n          <div className=\"flex gap-4\">\n            <Button variant=\"secondary\" size=\"sm\">\n              About\n            </Button>\n            <Button variant=\"primary\" size=\"sm\">\n              Get Started\n            </Button>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20 px-6\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-6xl font-bold uppercase tracking-wide mb-6 leading-tight\">\n            Transform Your\n            <span className=\"block text-pink-500\">LinkedIn Profile</span>\n            Into a Professional CV\n          </h2>\n          <p className=\"text-xl font-medium mb-12 max-w-2xl mx-auto\">\n            Upload a screenshot of your LinkedIn profile and let our AI create a stunning,\n            professional CV in seconds. No manual data entry required!\n          </p>\n\n          <div className=\"flex gap-6 justify-center mb-16\">\n            <Button size=\"lg\" color=\"cyan\">\n              Start Building CV\n            </Button>\n            <Button variant=\"secondary\" size=\"lg\">\n              See Examples\n            </Button>\n          </div>\n\n          {/* Process Steps */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mt-16\">\n            {steps.map((step, index) => {\n              const Icon = step.icon\n              return (\n                <Card\n                  key={index}\n                  className={`text-center transition-all duration-300 ${\n                    index === currentStep ? 'transform -translate-y-2' : ''\n                  }`}\n                  color={index === 0 ? 'yellow' : index === 1 ? 'cyan' : index === 2 ? 'pink' : 'green'}\n                >\n                  <CardContent>\n                    <div className=\"flex justify-center mb-4\">\n                      <div className=\"neo-border neo-shadow p-4 bg-white\">\n                        <Icon size={32} className=\"text-black\" />\n                      </div>\n                    </div>\n                    <h3 className=\"font-bold text-lg uppercase mb-2\">\n                      {step.title}\n                    </h3>\n                    <p className=\"text-sm font-medium\">\n                      {step.description}\n                    </p>\n                  </CardContent>\n                </Card>\n              )\n            })}\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 px-6 bg-black text-white\">\n        <div className=\"max-w-6xl mx-auto\">\n          <h2 className=\"text-4xl font-bold uppercase text-center mb-16\">\n            Why Choose Our AI CV Builder?\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <Card className=\"bg-yellow-400 text-black\">\n              <CardHeader>\n                <CardTitle>⚡ Lightning Fast</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"font-medium\">\n                  Generate professional CVs in under 30 seconds. No more hours of formatting!\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card className=\"bg-pink-500 text-white\">\n              <CardHeader>\n                <CardTitle>🎯 AI Powered</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"font-medium\">\n                  Advanced AI extracts and organizes your professional information intelligently.\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card className=\"bg-cyan-400 text-black\">\n              <CardHeader>\n                <CardTitle>📄 Professional</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"font-medium\">\n                  Clean, ATS-friendly formats that get you noticed by recruiters.\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"neo-border-thick border-b-0 border-l-0 border-r-0 bg-white p-8\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <p className=\"font-bold uppercase tracking-wide\">\n            Built with ❤️ using AI & Neobrutalism Design\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,QAAQ;QACZ;YAAE,MAAM,sMAAA,CAAA,SAAM;YAAE,OAAO;YAA8B,aAAa;QAAwC;QAC1G;YAAE,MAAM,gMAAA,CAAA,MAAG;YAAE,OAAO;YAAiB,aAAa;QAAyC;QAC3F;YAAE,MAAM,8MAAA,CAAA,WAAQ;YAAE,OAAO;YAAiB,aAAa;QAAqC;QAC5F;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO;YAAgB,aAAa;QAAiC;KACxF;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6C;;;;;;sCAG3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,MAAK;8CAAK;;;;;;8CAGtC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAQ1C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;gCAAuB;;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA8C;;;;;;sCAK3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,OAAM;8CAAO;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,MAAK;8CAAK;;;;;;;;;;;;sCAMxC,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gCAChB,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;oCAEH,WAAW,CAAC,wCAAwC,EAClD,UAAU,cAAc,6BAA6B,IACrD;oCACF,OAAO,UAAU,IAAI,WAAW,UAAU,IAAI,SAAS,UAAU,IAAI,SAAS;8CAE9E,cAAA,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,MAAM;wDAAI,WAAU;;;;;;;;;;;;;;;;0DAG9B,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;;;;;;;mCAhBhB;;;;;4BAqBX;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAI/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAE,WAAU;0DAAc;;;;;;;;;;;;;;;;;8CAM/B,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAE,WAAU;0DAAc;;;;;;;;;;;;;;;;;8CAM/B,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAE,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAoC;;;;;;;;;;;;;;;;;;;;;;AAO3D", "debugId": null}}]}