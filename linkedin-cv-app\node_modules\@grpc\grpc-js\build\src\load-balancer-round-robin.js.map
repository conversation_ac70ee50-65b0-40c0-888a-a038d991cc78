{"version": 3, "file": "load-balancer-round-robin.js", "sourceRoot": "", "sources": ["../../src/load-balancer-round-robin.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAyOH,sBAMC;AA7OD,mDAMyB;AACzB,6DAAyD;AACzD,qCAMkB;AAClB,qCAAqC;AACrC,2CAA2C;AAC3C,6DAI8B;AAC9B,yEAA8D;AAG9D,MAAM,WAAW,GAAG,aAAa,CAAC;AAElC,SAAS,KAAK,CAAC,IAAY;IACzB,OAAO,CAAC,KAAK,CAAC,wBAAY,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC;AAED,MAAM,SAAS,GAAG,aAAa,CAAC;AAEhC,MAAM,6BAA6B;IACjC,mBAAmB;QACjB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,gBAAe,CAAC;IAEhB,YAAY;QACV,OAAO;YACL,CAAC,SAAS,CAAC,EAAE,EAAE;SAChB,CAAC;IACJ,CAAC;IAED,8DAA8D;IAC9D,MAAM,CAAC,cAAc,CAAC,GAAQ;QAC5B,OAAO,IAAI,6BAA6B,EAAE,CAAC;IAC7C,CAAC;CACF;AAED,MAAM,gBAAgB;IACpB,YACmB,QAAkD,EAC3D,YAAY,CAAC;QADJ,aAAQ,GAAR,QAAQ,CAA0C;QAC3D,cAAS,GAAT,SAAS,CAAI;IACpB,CAAC;IAEJ,IAAI,CAAC,QAAkB;QACrB,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC7D,OAAO,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC;IAChD,CAAC;CACF;AAED,MAAa,sBAAsB;IAajC,YACmB,oBAA0C;QAA1C,yBAAoB,GAApB,oBAAoB,CAAsB;QAbrD,aAAQ,GAAuB,EAAE,CAAC;QAElC,iBAAY,GAAsB,sCAAiB,CAAC,IAAI,CAAC;QAEzD,uBAAkB,GAA4B,IAAI,CAAC;QAEnD,kBAAa,GAAG,KAAK,CAAC;QAItB,cAAS,GAAkB,IAAI,CAAC;QAKtC,IAAI,CAAC,yBAAyB,GAAG,IAAA,+CAA+B,EAC9D,oBAAoB,EACpB;YACE,WAAW,EAAE,CAAC,iBAAiB,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE;gBACvD;;;6CAG6B;gBAC7B,IAAI,IAAI,CAAC,YAAY,KAAK,sCAAiB,CAAC,KAAK,IAAI,iBAAiB,KAAK,sCAAiB,CAAC,KAAK,EAAE,CAAC;oBACnG,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,CAAC;gBAClD,CAAC;gBACD,IAAI,YAAY,EAAE,CAAC;oBACjB,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC;gBAChC,CAAC;gBACD,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,CAAC;SACF,CACF,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,KAAwB;QACrD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,KAAK,CAAC;aACzE,MAAM,CAAC;IACZ,CAAC;IAEO,uBAAuB;QAC7B,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,sBAAsB,CAAC,sCAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CACxC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,sCAAiB,CAAC,KAAK,CAClE,CAAC;YACF,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;gBACrC,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;gBACtE,KAAK,GAAG,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CACtC,IAAA,kCAAa,EAAC,KAAK,CAAC,WAAW,EAAE,EAAE,kBAAkB,CAAC,CACvD,CAAC;gBACF,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBACd,KAAK,GAAG,CAAC,CAAC;gBACZ,CAAC;YACH,CAAC;YACD,IAAI,CAAC,WAAW,CACd,sCAAiB,CAAC,KAAK,EACvB,IAAI,gBAAgB,CAClB,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC1B,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE;gBAC7B,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE;aAC1B,CAAC,CAAC,EACH,KAAK,CACN,EACD,IAAI,CACL,CAAC;QACJ,CAAC;aAAM,IAAI,IAAI,CAAC,sBAAsB,CAAC,sCAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACzE,IAAI,CAAC,WAAW,CAAC,sCAAiB,CAAC,UAAU,EAAE,IAAI,oBAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QAC9E,CAAC;aAAM,IACL,IAAI,CAAC,sBAAsB,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,GAAG,CAAC,EACpE,CAAC;YACD,MAAM,YAAY,GAAG,uDAAuD,IAAI,CAAC,SAAS,EAAE,CAAC;YAC7F,IAAI,CAAC,WAAW,CACd,sCAAiB,CAAC,iBAAiB,EACnC,IAAI,0BAAiB,CAAC;gBACpB,OAAO,EAAE,YAAY;aACtB,CAAC,EACF,YAAY,CACb,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,sCAAiB,CAAC,IAAI,EAAE,IAAI,oBAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QACxE,CAAC;QACD;;;wCAGgC;QAChC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,KAAK,CAAC,oBAAoB,EAAE,KAAK,sCAAiB,CAAC,IAAI,EAAE,CAAC;gBAC5D,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,QAA2B,EAAE,MAAc,EAAE,YAA2B;QAC1F,KAAK,CACH,sCAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;YAClC,MAAM;YACN,sCAAiB,CAAC,QAAQ,CAAC,CAC9B,CAAC;QACF,IAAI,QAAQ,KAAK,sCAAiB,CAAC,KAAK,EAAE,CAAC;YACzC,IAAI,CAAC,kBAAkB,GAAG,MAA0B,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;QAC7B,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACxE,CAAC;IAEO,mBAAmB;QACzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,KAAK,CAAC,OAAO,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAED,iBAAiB,CACf,YAAwB,EACxB,QAAkC,EAClC,OAAuB;QAEvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,KAAK,CAAC,2BAA2B,GAAG,YAAY,CAAC,GAAG,CAAC,qCAAgB,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,GAAG,CAC9B,QAAQ,CAAC,EAAE,CACT,IAAI,2CAAgB,CAClB,QAAQ,EACR,IAAI,CAAC,yBAAyB,EAC9B,OAAO,CACR,CACJ,CAAC;QACF,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,KAAK,CAAC,eAAe,EAAE,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,QAAQ;QACN;;iFAEyE;IAC3E,CAAC;IACD,YAAY;QACV,yCAAyC;IAC3C,CAAC;IACD,OAAO;QACL,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IACD,WAAW;QACT,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AA3JD,wDA2JC;AAED,SAAgB,KAAK;IACnB,IAAA,wCAAwB,EACtB,SAAS,EACT,sBAAsB,EACtB,6BAA6B,CAC9B,CAAC;AACJ,CAAC"}