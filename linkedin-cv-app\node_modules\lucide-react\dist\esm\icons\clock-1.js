/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v6l2-4", key: "miptyd" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
];
const Clock1 = createLucideIcon("clock-1", __iconNode);

export { __iconNode, Clock1 as default };
//# sourceMappingURL=clock-1.js.map
