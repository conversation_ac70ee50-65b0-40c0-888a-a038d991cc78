module.exports = {

"[project]/.next-internal/server/app/api/process-linkedin/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cleanProfileData": (()=>cleanProfileData),
    "fileToBase64": (()=>fileToBase64),
    "processLinkedInScreenshot": (()=>processLinkedInScreenshot),
    "validateProfileData": (()=>validateProfileData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
// Initialize AIML API client (using OpenAI SDK with custom base URL)
const aimlClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.AIML_API_KEY,
    baseURL: 'https://api.aimlapi.com'
});
async function processLinkedInScreenshot(imageBase64) {
    const maxRetries = 2;
    let lastError = null;
    for(let attempt = 1; attempt <= maxRetries; attempt++){
        try {
            console.log(`Starting AIML API request (attempt ${attempt}/${maxRetries})...`);
            // Process the image directly
            const response = await aimlClient.chat.completions.create({
                model: "gpt-4o",
                messages: [
                    {
                        role: "user",
                        content: [
                            {
                                type: "text",
                                text: `What text do you see in this image? Describe what you can read.`
                            },
                            {
                                type: "image_url",
                                image_url: {
                                    url: `data:image/jpeg;base64,${imageBase64}`,
                                    detail: "low"
                                }
                            }
                        ]
                    }
                ],
                max_tokens: 500,
                temperature: 0.1
            });
            console.log('AIML API response received');
            console.log('Response object:', JSON.stringify(response, null, 2));
            const content = response.choices[0]?.message?.content;
            console.log('Response content length:', content?.length || 0);
            console.log('Response content preview:', content?.substring(0, 200));
            if (!content) {
                throw new Error('No response from AIML API');
            }
            console.log('Raw AI response:', content.substring(0, 500) + '...');
            // Try to parse the JSON response
            try {
                const rawProfileData = JSON.parse(content);
                console.log('Successfully parsed JSON response');
                // Clean and enhance the extracted data
                const cleanedProfileData = cleanProfileData(rawProfileData);
                console.log('Data cleaning completed');
                return cleanedProfileData;
            } catch (parseError) {
                console.log('Initial JSON parse failed, trying to extract JSON...');
                // If JSON parsing fails, try to extract JSON from the response
                // Look for JSON between ```json and ``` or just between { and }
                let jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
                if (!jsonMatch) {
                    jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/);
                }
                if (!jsonMatch) {
                    jsonMatch = content.match(/(\{[\s\S]*\})/);
                }
                if (jsonMatch) {
                    try {
                        const cleanedJson = jsonMatch[1] || jsonMatch[0];
                        console.log('Extracted JSON:', cleanedJson.substring(0, 200) + '...');
                        const rawProfileData = JSON.parse(cleanedJson);
                        console.log('Successfully parsed extracted JSON');
                        // Clean and enhance the extracted data
                        const cleanedProfileData = cleanProfileData(rawProfileData);
                        console.log('Data cleaning completed');
                        return cleanedProfileData;
                    } catch (secondParseError) {
                        console.error('Failed to parse extracted JSON:', secondParseError);
                    }
                }
                console.error('Raw response that failed to parse:', content);
                throw new Error('Failed to parse AI response as JSON');
            }
        } catch (error) {
            console.error(`Attempt ${attempt} failed:`, error);
            lastError = error instanceof Error ? error : new Error('Unknown error');
            if (attempt < maxRetries) {
                console.log(`Retrying in 2 seconds...`);
                await new Promise((resolve)=>setTimeout(resolve, 2000));
                continue;
            }
        }
    }
    // If all attempts failed, throw the last error
    if (lastError) {
        console.error('All attempts failed. Error processing LinkedIn screenshot with AIML API:', lastError);
        // Provide more detailed error information
        if (lastError instanceof Error) {
            console.error('Error message:', lastError.message);
            console.error('Error stack:', lastError.stack);
        }
        // Check if it's an API-related error
        if (lastError && typeof lastError === 'object' && 'response' in lastError) {
            console.error('API response error:', lastError);
        }
        throw new Error(`Failed to process LinkedIn screenshot with AI after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : 'Unknown error'}`);
    }
    // This should never be reached, but TypeScript requires it
    throw new Error('Unexpected error in processLinkedInScreenshot');
}
function fileToBase64(file) {
    return new Promise((resolve, reject)=>{
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = ()=>{
            const result = reader.result;
            // Remove the data URL prefix to get just the base64 string
            const base64 = result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = (error)=>reject(error);
    });
}
function validateProfileData(profile) {
    return !!(profile.personalInfo?.name && profile.personalInfo?.title && (profile.experience?.length > 0 || profile.education?.length > 0));
}
function cleanProfileData(profile) {
    return {
        personalInfo: {
            name: cleanText(profile.personalInfo?.name || ''),
            title: cleanText(profile.personalInfo?.title || ''),
            location: cleanText(profile.personalInfo?.location || ''),
            email: cleanEmail(profile.personalInfo?.email || ''),
            phone: cleanPhone(profile.personalInfo?.phone || ''),
            linkedin: cleanUrl(profile.personalInfo?.linkedin || '')
        },
        summary: cleanText(profile.summary || ''),
        experience: (profile.experience || []).map((exp)=>({
                company: cleanText(exp.company || ''),
                position: cleanText(exp.position || ''),
                duration: cleanDuration(exp.duration || ''),
                description: cleanText(exp.description || ''),
                location: cleanText(exp.location || '')
            })),
        education: (profile.education || []).map((edu)=>({
                institution: cleanText(edu.institution || ''),
                degree: cleanText(edu.degree || ''),
                field: cleanText(edu.field || ''),
                duration: cleanDuration(edu.duration || ''),
                location: cleanText(edu.location || '')
            })),
        skills: (profile.skills || []).map((skill)=>cleanText(skill)).filter((skill)=>skill.length > 0),
        certifications: (profile.certifications || []).map((cert)=>({
                name: cleanText(cert.name || ''),
                issuer: cleanText(cert.issuer || ''),
                date: cleanText(cert.date || '')
            })),
        languages: (profile.languages || []).map((lang)=>({
                language: cleanText(lang.language || ''),
                proficiency: cleanText(lang.proficiency || '')
            }))
    };
}
// Helper functions for data cleaning
function cleanText(text) {
    return text.trim().replace(/\s+/g, ' ').replace(/[""]/g, '"').replace(/['']/g, "'");
}
function cleanEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim()) ? email.trim().toLowerCase() : '';
}
function cleanPhone(phone) {
    const cleaned = phone.replace(/[^\d+\-\(\)\s]/g, '').trim();
    return cleaned.length >= 10 ? cleaned : '';
}
function cleanUrl(url) {
    if (!url) return '';
    if (url.startsWith('http')) return url;
    if (url.includes('linkedin.com')) return `https://${url}`;
    return url;
}
function cleanDuration(duration) {
    return duration.trim().replace(/\s+/g, ' ');
}
}}),
"[project]/src/lib/demo-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "demoLinkedInProfile": (()=>demoLinkedInProfile),
    "simulateAIProcessing": (()=>simulateAIProcessing)
});
const demoLinkedInProfile = {
    personalInfo: {
        name: "Alex Johnson",
        title: "Senior Software Engineer",
        location: "San Francisco, CA",
        email: "<EMAIL>",
        linkedin: "linkedin.com/in/alexjohnson"
    },
    summary: "Experienced software engineer with 5+ years of expertise in full-stack development, cloud architecture, and team leadership. Passionate about building scalable applications and mentoring junior developers. Proven track record of delivering high-quality software solutions in fast-paced startup environments.",
    experience: [
        {
            company: "TechCorp Inc.",
            position: "Senior Software Engineer",
            duration: "Jan 2022 - Present",
            description: "Lead development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 60%. Mentored 3 junior developers and conducted technical interviews.",
            location: "San Francisco, CA"
        },
        {
            company: "StartupXYZ",
            position: "Full Stack Developer",
            duration: "Mar 2020 - Dec 2021",
            description: "Built responsive web applications using React and Node.js. Designed and implemented RESTful APIs. Collaborated with product team to define technical requirements and user stories.",
            location: "Remote"
        },
        {
            company: "Digital Solutions Ltd",
            position: "Junior Developer",
            duration: "Jun 2019 - Feb 2020",
            description: "Developed and maintained client websites using HTML, CSS, JavaScript, and PHP. Participated in code reviews and agile development processes. Fixed bugs and implemented new features.",
            location: "New York, NY"
        }
    ],
    education: [
        {
            institution: "University of California, Berkeley",
            degree: "Bachelor of Science",
            field: "Computer Science",
            duration: "2015 - 2019",
            location: "Berkeley, CA"
        }
    ],
    skills: [
        "JavaScript",
        "TypeScript",
        "React",
        "Node.js",
        "Python",
        "AWS",
        "Docker",
        "Kubernetes",
        "PostgreSQL",
        "MongoDB",
        "Git",
        "Agile/Scrum",
        "System Design",
        "API Development",
        "Microservices",
        "CI/CD"
    ],
    certifications: [
        {
            name: "AWS Certified Solutions Architect",
            issuer: "Amazon Web Services",
            date: "2023"
        },
        {
            name: "Certified Kubernetes Administrator",
            issuer: "Cloud Native Computing Foundation",
            date: "2022"
        }
    ],
    languages: [
        {
            language: "English",
            proficiency: "Native"
        },
        {
            language: "Spanish",
            proficiency: "Conversational"
        }
    ]
};
function simulateAIProcessing() {
    return new Promise((resolve)=>{
        setTimeout(()=>{
            resolve(demoLinkedInProfile);
        }, 2000) // 2 second delay to simulate processing
        ;
    });
}
}}),
"[project]/src/app/api/process-linkedin/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OPTIONS": (()=>OPTIONS),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/demo-data.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        // Check if AIML API key is configured
        const hasApiKey = !!process.env.AIML_API_KEY;
        if (!hasApiKey) {
            console.log('AIML API key not configured, using demo data');
        }
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        // Validate file type
        if (!file.type.startsWith('image/')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File must be an image'
            }, {
                status: 400
            });
        }
        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File size must be less than 10MB'
            }, {
                status: 400
            });
        }
        let profileData;
        if (hasApiKey) {
            console.log('AIML API key found, testing vision API...');
            console.log('File type:', file.type);
            console.log('File size:', file.size);
            try {
                // Convert file to base64 with optimization
                const arrayBuffer = await file.arrayBuffer();
                let base64 = Buffer.from(arrayBuffer).toString('base64');
                console.log('Original base64 length:', base64.length);
                // If image is very large, we might want to resize it
                // For now, we'll use the original but log the size
                const imageSizeKB = Math.round(base64.length * 0.75 / 1024) // Approximate KB
                ;
                console.log(`Image size: ~${imageSizeKB}KB`);
                if (imageSizeKB > 5000) {
                    console.log('Large image detected, processing with high detail mode');
                }
                // Process with AIML API
                console.log('Processing LinkedIn screenshot with enhanced AI...');
                profileData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["processLinkedInScreenshot"])(base64);
                console.log('AIML API processing successful!');
            } catch (apiError) {
                console.error('AIML API failed, falling back to demo data:', apiError);
                profileData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["simulateAIProcessing"])();
            }
        } else {
            console.log('No API key found, using demo data...');
            // Use demo data when API key is not configured
            profileData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["simulateAIProcessing"])();
        }
        // Validate the extracted data
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateProfileData"])(profileData)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Could not extract sufficient profile information from the image'
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: profileData
        });
    } catch (error) {
        console.error('Error in process-linkedin API:', error);
        // Fallback to demo data if AI processing fails
        console.log('Falling back to demo data due to AI processing error');
        try {
            const demoData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["simulateAIProcessing"])();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: demoData,
                warning: 'AI processing failed, showing demo data. Please check your API configuration.'
            });
        } catch (demoError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error instanceof Error ? error.message : 'Failed to process LinkedIn screenshot',
                details: ("TURBOPACK compile-time truthy", 1) ? error : ("TURBOPACK unreachable", undefined)
            }, {
                status: 500
            });
        }
    }
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f38b47da._.js.map