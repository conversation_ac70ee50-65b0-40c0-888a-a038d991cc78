module.exports = {

"[project]/.next-internal/server/app/api/process-linkedin/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fileToBase64": (()=>fileToBase64),
    "processLinkedInScreenshot": (()=>processLinkedInScreenshot),
    "validateProfileData": (()=>validateProfileData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
// Initialize AIML API client (using OpenAI SDK with custom base URL)
const aimlClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.AIML_API_KEY,
    baseURL: 'https://api.aimlapi.com'
});
async function processLinkedInScreenshot(imageBase64) {
    try {
        const response = await aimlClient.chat.completions.create({
            model: "gpt-4o",
            messages: [
                {
                    role: "user",
                    content: [
                        {
                            type: "text",
                            text: `You are a professional CV extraction specialist. Analyze this LinkedIn profile screenshot and extract ALL visible professional information with high accuracy.

EXTRACT THE FOLLOWING DATA:

1. **Personal Information:**
   - Full name (exactly as shown)
   - Current job title/headline
   - Location (city, state/country)
   - Contact information (email, phone if visible)
   - LinkedIn URL (if visible)

2. **Professional Summary:**
   - Complete "About" section text
   - Professional headline/tagline

3. **Work Experience:** (For each position)
   - Company name
   - Job title/position
   - Employment duration (start - end dates)
   - Job description/responsibilities
   - Location (if shown)

4. **Education:** (For each degree)
   - Institution name
   - Degree type and field of study
   - Duration/graduation year
   - Location (if shown)

5. **Skills:**
   - All listed skills (technical and soft skills)
   - Endorsement counts (if visible)

6. **Certifications:** (if any)
   - Certification name
   - Issuing organization
   - Date obtained

7. **Languages:** (if any)
   - Language name
   - Proficiency level

**IMPORTANT REQUIREMENTS:**
- Return ONLY valid JSON format
- Use the exact structure provided below
- Extract text exactly as written (preserve professional language)
- If information is not visible, use empty string "" or empty array []
- Be thorough - this data will generate someone's professional CV

**JSON STRUCTURE:**
{
  "personalInfo": {
    "name": "",
    "title": "",
    "location": "",
    "email": "",
    "phone": "",
    "linkedin": ""
  },
  "summary": "",
  "experience": [
    {
      "company": "",
      "position": "",
      "duration": "",
      "description": "",
      "location": ""
    }
  ],
  "education": [
    {
      "institution": "",
      "degree": "",
      "field": "",
      "duration": "",
      "location": ""
    }
  ],
  "skills": [],
  "certifications": [
    {
      "name": "",
      "issuer": "",
      "date": ""
    }
  ],
  "languages": [
    {
      "language": "",
      "proficiency": ""
    }
  ]
}`
                        },
                        {
                            type: "image_url",
                            image_url: {
                                url: `data:image/jpeg;base64,${imageBase64}`,
                                detail: "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens: 4000,
            temperature: 0.1,
            top_p: 0.9
        });
        const content = response.choices[0]?.message?.content;
        if (!content) {
            throw new Error('No response from OpenAI');
        }
        // Try to parse the JSON response
        try {
            const profileData = JSON.parse(content);
            return profileData;
        } catch (parseError) {
            // If JSON parsing fails, try to extract JSON from the response
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const profileData = JSON.parse(jsonMatch[0]);
                return profileData;
            }
            throw new Error('Failed to parse AI response as JSON');
        }
    } catch (error) {
        console.error('Error processing LinkedIn screenshot with AIML API:', error);
        throw new Error('Failed to process LinkedIn screenshot with AI');
    }
}
function fileToBase64(file) {
    return new Promise((resolve, reject)=>{
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = ()=>{
            const result = reader.result;
            // Remove the data URL prefix to get just the base64 string
            const base64 = result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = (error)=>reject(error);
    });
}
function validateProfileData(profile) {
    return !!(profile.personalInfo?.name && profile.personalInfo?.title && (profile.experience?.length > 0 || profile.education?.length > 0));
}
}}),
"[project]/src/lib/demo-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "demoLinkedInProfile": (()=>demoLinkedInProfile),
    "simulateAIProcessing": (()=>simulateAIProcessing)
});
const demoLinkedInProfile = {
    personalInfo: {
        name: "Alex Johnson",
        title: "Senior Software Engineer",
        location: "San Francisco, CA",
        email: "<EMAIL>",
        linkedin: "linkedin.com/in/alexjohnson"
    },
    summary: "Experienced software engineer with 5+ years of expertise in full-stack development, cloud architecture, and team leadership. Passionate about building scalable applications and mentoring junior developers. Proven track record of delivering high-quality software solutions in fast-paced startup environments.",
    experience: [
        {
            company: "TechCorp Inc.",
            position: "Senior Software Engineer",
            duration: "Jan 2022 - Present",
            description: "Lead development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 60%. Mentored 3 junior developers and conducted technical interviews.",
            location: "San Francisco, CA"
        },
        {
            company: "StartupXYZ",
            position: "Full Stack Developer",
            duration: "Mar 2020 - Dec 2021",
            description: "Built responsive web applications using React and Node.js. Designed and implemented RESTful APIs. Collaborated with product team to define technical requirements and user stories.",
            location: "Remote"
        },
        {
            company: "Digital Solutions Ltd",
            position: "Junior Developer",
            duration: "Jun 2019 - Feb 2020",
            description: "Developed and maintained client websites using HTML, CSS, JavaScript, and PHP. Participated in code reviews and agile development processes. Fixed bugs and implemented new features.",
            location: "New York, NY"
        }
    ],
    education: [
        {
            institution: "University of California, Berkeley",
            degree: "Bachelor of Science",
            field: "Computer Science",
            duration: "2015 - 2019",
            location: "Berkeley, CA"
        }
    ],
    skills: [
        "JavaScript",
        "TypeScript",
        "React",
        "Node.js",
        "Python",
        "AWS",
        "Docker",
        "Kubernetes",
        "PostgreSQL",
        "MongoDB",
        "Git",
        "Agile/Scrum",
        "System Design",
        "API Development",
        "Microservices",
        "CI/CD"
    ],
    certifications: [
        {
            name: "AWS Certified Solutions Architect",
            issuer: "Amazon Web Services",
            date: "2023"
        },
        {
            name: "Certified Kubernetes Administrator",
            issuer: "Cloud Native Computing Foundation",
            date: "2022"
        }
    ],
    languages: [
        {
            language: "English",
            proficiency: "Native"
        },
        {
            language: "Spanish",
            proficiency: "Conversational"
        }
    ]
};
function simulateAIProcessing() {
    return new Promise((resolve)=>{
        setTimeout(()=>{
            resolve(demoLinkedInProfile);
        }, 2000) // 2 second delay to simulate processing
        ;
    });
}
}}),
"[project]/src/app/api/process-linkedin/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OPTIONS": (()=>OPTIONS),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/demo-data.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        // Check if AIML API key is configured
        const hasApiKey = !!process.env.AIML_API_KEY;
        if (!hasApiKey) {
            console.log('AIML API key not configured, using demo data');
        }
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        // Validate file type
        if (!file.type.startsWith('image/')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File must be an image'
            }, {
                status: 400
            });
        }
        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File size must be less than 10MB'
            }, {
                status: 400
            });
        }
        let profileData;
        if (hasApiKey) {
            // Convert file to base64
            const arrayBuffer = await file.arrayBuffer();
            const base64 = Buffer.from(arrayBuffer).toString('base64');
            // Process with AIML API
            profileData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["processLinkedInScreenshot"])(base64);
        } else {
            // Use demo data when API key is not configured
            profileData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$demo$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["simulateAIProcessing"])();
        }
        // Validate the extracted data
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateProfileData"])(profileData)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Could not extract sufficient profile information from the image'
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: profileData
        });
    } catch (error) {
        console.error('Error in process-linkedin API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error instanceof Error ? error.message : 'Failed to process LinkedIn screenshot',
            details: ("TURBOPACK compile-time truthy", 1) ? error : ("TURBOPACK unreachable", undefined)
        }, {
            status: 500
        });
    }
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f38b47da._.js.map