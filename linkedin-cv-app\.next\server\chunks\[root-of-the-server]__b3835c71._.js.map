{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/api/test-aiml/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport OpenAI from 'openai'\n\nexport async function GET() {\n  try {\n    // Check if AIML API key is configured\n    if (!process.env.AIML_API_KEY) {\n      return NextResponse.json({\n        status: 'error',\n        message: 'AIML API key not configured',\n        instructions: 'Add AIML_API_KEY to your .env.local file'\n      })\n    }\n\n    // Initialize AIML API client\n    const aimlClient = new OpenAI({\n      apiKey: process.env.AIML_API_KEY,\n      baseURL: 'https://api.aimlapi.com',\n    })\n\n    // Test the connection with a simple request\n    const response = await aimlClient.chat.completions.create({\n      model: \"gpt-4o\",\n      messages: [\n        {\n          role: \"user\",\n          content: \"Hello! Please respond with 'AIML API connection successful' to confirm the integration is working.\"\n        }\n      ],\n      max_tokens: 50,\n      temperature: 0.1,\n    })\n\n    const content = response.choices[0]?.message?.content\n\n    return NextResponse.json({\n      status: 'success',\n      message: 'AIML API connection successful',\n      response: content,\n      model: 'gpt-4o',\n      baseURL: 'https://api.aimlapi.com'\n    })\n\n  } catch (error) {\n    console.error('AIML API test error:', error)\n    \n    return NextResponse.json({\n      status: 'error',\n      message: 'Failed to connect to AIML API',\n      error: error instanceof Error ? error.message : 'Unknown error',\n      instructions: 'Check your AIML_API_KEY and internet connection'\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,eAAe;IACpB,IAAI;QACF,sCAAsC;QACtC,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,EAAE;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;gBACR,SAAS;gBACT,cAAc;YAChB;QACF;QAEA,6BAA6B;QAC7B,MAAM,aAAa,IAAI,wKAAA,CAAA,UAAM,CAAC;YAC5B,QAAQ,QAAQ,GAAG,CAAC,YAAY;YAChC,SAAS;QACX;QAEA,4CAA4C;QAC5C,MAAM,WAAW,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACxD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,YAAY;YACZ,aAAa;QACf;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAE9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,SAAS;YACT,UAAU;YACV,OAAO;YACP,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,cAAc;QAChB,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}