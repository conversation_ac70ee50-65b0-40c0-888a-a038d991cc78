'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { LinkedInProfile } from '@/lib/openai'

interface Template {
  id: string
  name: string
  description: string
  color: string
  preview: string
  features: string[]
}

const templates: Template[] = [
  {
    id: 'modern',
    name: 'MODERN',
    description: 'Clean, contemporary design with bold typography',
    color: 'blue',
    preview: 'Modern layout with sidebar and clean sections',
    features: ['Bold headers', 'Color accents', 'Modern typography', 'Professional layout']
  },
  {
    id: 'classic',
    name: 'CLASSIC',
    description: 'Traditional professional format, ATS-friendly',
    color: 'gray',
    preview: 'Traditional single-column layout',
    features: ['ATS-friendly', 'Traditional format', 'Clean typography', 'Professional standard']
  },
  {
    id: 'creative',
    name: 'CREATIVE',
    description: 'Eye-catching design for creative professionals',
    color: 'purple',
    preview: 'Creative layout with visual elements',
    features: ['Visual elements', 'Creative layout', 'Color highlights', 'Unique design']
  },
  {
    id: 'minimal',
    name: 'MINIMAL',
    description: 'Ultra-clean design focusing on content',
    color: 'green',
    preview: 'Minimal design with maximum readability',
    features: ['Ultra-clean', 'Content-focused', 'Minimal design', 'High readability']
  }
]

export default function TemplatesPage() {
  const router = useRouter()
  const [profile, setProfile] = useState<LinkedInProfile | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get profile data from localStorage
    const editedProfile = localStorage.getItem('editedProfile')
    const extractedProfile = localStorage.getItem('extractedProfile')
    
    if (editedProfile) {
      setProfile(JSON.parse(editedProfile))
    } else if (extractedProfile) {
      setProfile(JSON.parse(extractedProfile))
    }
    setLoading(false)
  }, [])

  const selectTemplate = (templateId: string) => {
    setSelectedTemplate(templateId)
  }

  const generateCV = () => {
    if (profile && selectedTemplate) {
      // Store selected template and profile data
      localStorage.setItem('selectedTemplate', selectedTemplate)
      localStorage.setItem('finalProfile', JSON.stringify(profile))
      router.push('/generate')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-yellow-300 flex items-center justify-center">
        <div className="text-2xl font-black">Loading templates...</div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-yellow-300 flex items-center justify-center">
        <div className="bg-white border-4 border-black p-8 shadow-brutal">
          <h1 className="text-2xl font-black mb-4">No Profile Data Found</h1>
          <p className="mb-4">Please complete the CV editing process first.</p>
          <button
            onClick={() => router.push('/upload')}
            className="bg-red-500 text-white px-6 py-3 border-4 border-black font-black hover:bg-red-600 transition-colors shadow-brutal"
          >
            START OVER
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-yellow-300 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white border-4 border-black p-6 mb-6 shadow-brutal">
          <h1 className="text-4xl font-black mb-2">CHOOSE YOUR CV TEMPLATE</h1>
          <p className="text-lg font-bold">Select a professional template that matches your style</p>
        </div>

        {/* Template Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {templates.map((template) => (
            <div
              key={template.id}
              className={`bg-white border-4 border-black p-6 shadow-brutal cursor-pointer transition-all hover:scale-105 ${
                selectedTemplate === template.id 
                  ? 'ring-4 ring-yellow-400 bg-yellow-50' 
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => selectTemplate(template.id)}
            >
              {/* Template Preview */}
              <div className={`h-48 border-2 border-black mb-4 bg-${template.color}-100 flex items-center justify-center`}>
                <div className="text-center">
                  <div className={`w-16 h-16 bg-${template.color}-500 border-2 border-black mx-auto mb-2`}></div>
                  <div className="text-xs font-black">{template.preview}</div>
                </div>
              </div>

              {/* Template Info */}
              <h3 className="text-xl font-black mb-2">{template.name}</h3>
              <p className="text-sm font-bold mb-3 text-gray-700">{template.description}</p>

              {/* Features */}
              <div className="space-y-1">
                {template.features.map((feature, index) => (
                  <div key={index} className="flex items-center text-xs">
                    <div className={`w-2 h-2 bg-${template.color}-500 border border-black mr-2`}></div>
                    <span className="font-bold">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Selection Indicator */}
              {selectedTemplate === template.id && (
                <div className="mt-4 bg-yellow-400 border-2 border-black p-2 text-center">
                  <span className="font-black text-sm">SELECTED ✓</span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Profile Preview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Profile Summary */}
          <div className="bg-white border-4 border-black p-6 shadow-brutal">
            <h2 className="text-2xl font-black mb-4 text-blue-600">YOUR CV DATA</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-black text-lg">{profile.personalInfo.name}</h3>
                <p className="font-bold text-gray-700">{profile.personalInfo.title}</p>
                <p className="text-gray-600">{profile.personalInfo.location}</p>
              </div>

              {profile.summary && (
                <div>
                  <h4 className="font-black text-sm mb-2">SUMMARY</h4>
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {profile.summary.substring(0, 150)}...
                  </p>
                </div>
              )}

              <div>
                <h4 className="font-black text-sm mb-2">EXPERIENCE</h4>
                <p className="text-sm text-gray-600">{profile.experience.length} positions</p>
              </div>

              <div>
                <h4 className="font-black text-sm mb-2">EDUCATION</h4>
                <p className="text-sm text-gray-600">{profile.education.length} entries</p>
              </div>

              <div>
                <h4 className="font-black text-sm mb-2">SKILLS</h4>
                <p className="text-sm text-gray-600">{profile.skills.length} skills</p>
              </div>
            </div>
          </div>

          {/* Template Preview with Data */}
          <div className="bg-white border-4 border-black p-6 shadow-brutal">
            <h2 className="text-2xl font-black mb-4 text-green-600">TEMPLATE PREVIEW</h2>
            
            {selectedTemplate ? (
              <div className="border-2 border-gray-300 p-4 h-96 overflow-hidden">
                <div className="text-center mb-4">
                  <h3 className="font-black text-lg">{profile.personalInfo.name}</h3>
                  <p className="font-bold text-sm text-gray-700">{profile.personalInfo.title}</p>
                  <p className="text-xs text-gray-600">{profile.personalInfo.location}</p>
                </div>

                <div className="space-y-3 text-xs">
                  {profile.summary && (
                    <div>
                      <h4 className="font-black text-xs mb-1">SUMMARY</h4>
                      <p className="text-gray-700 leading-tight">
                        {profile.summary.substring(0, 100)}...
                      </p>
                    </div>
                  )}

                  {profile.experience.length > 0 && (
                    <div>
                      <h4 className="font-black text-xs mb-1">EXPERIENCE</h4>
                      {profile.experience.slice(0, 2).map((exp, index) => (
                        <div key={index} className="mb-2">
                          <p className="font-bold text-xs">{exp.position}</p>
                          <p className="text-xs text-gray-600">{exp.company}</p>
                        </div>
                      ))}
                    </div>
                  )}

                  {profile.skills.length > 0 && (
                    <div>
                      <h4 className="font-black text-xs mb-1">SKILLS</h4>
                      <div className="flex flex-wrap gap-1">
                        {profile.skills.slice(0, 6).map((skill, index) => (
                          <span key={index} className="bg-gray-200 px-1 py-0.5 text-xs font-bold border border-gray-400">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="border-2 border-gray-300 p-4 h-96 flex items-center justify-center">
                <p className="text-gray-500 font-bold">Select a template to see preview</p>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => router.push('/edit')}
            className="bg-gray-500 text-white px-8 py-4 border-4 border-black font-black text-xl hover:bg-gray-600 transition-colors shadow-brutal"
          >
            ← EDIT CV DATA
          </button>
          
          <button
            onClick={generateCV}
            disabled={!selectedTemplate}
            className={`px-8 py-4 border-4 border-black font-black text-xl transition-colors shadow-brutal ${
              selectedTemplate
                ? 'bg-green-500 text-white hover:bg-green-600'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            GENERATE CV →
          </button>
        </div>
      </div>
    </div>
  )
}
