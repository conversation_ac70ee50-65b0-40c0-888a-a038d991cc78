{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/google-vision.ts"], "sourcesContent": ["import { LinkedInProfile } from './openai'\nimport { GoogleAuth } from 'google-auth-library'\nimport path from 'path'\n\n// Google Vision API configuration\nconst GOOGLE_SERVICE_ACCOUNT = process.env.GOOGLE_SERVICE_ACCOUNT\nconst GOOGLE_VISION_ENDPOINT = 'https://vision.googleapis.com/v1/images:annotate'\nconst CREDENTIALS_PATH = path.join(process.cwd(), 'google-credentials.json')\n\ninterface GoogleVisionResponse {\n  responses: Array<{\n    textAnnotations?: Array<{\n      description: string\n      boundingPoly?: {\n        vertices: Array<{ x: number; y: number }>\n      }\n    }>\n    fullTextAnnotation?: {\n      text: string\n    }\n    error?: {\n      code: number\n      message: string\n    }\n  }>\n}\n\nexport async function extractTextWithGoogleVision(imageBase64: string): Promise<string> {\n  try {\n    console.log('Starting Google Vision API text extraction with service account...')\n\n    // Create Google Auth client using credentials file\n    const auth = new GoogleAuth({\n      keyFile: CREDENTIALS_PATH,\n      scopes: ['https://www.googleapis.com/auth/cloud-platform']\n    })\n\n    // Get access token\n    const authClient = await auth.getClient()\n    const accessToken = await authClient.getAccessToken()\n\n    if (!accessToken.token) {\n      throw new Error('Failed to get access token')\n    }\n\n    console.log('Successfully obtained access token')\n\n    const requestBody = {\n      requests: [\n        {\n          image: {\n            content: imageBase64\n          },\n          features: [\n            {\n              type: 'TEXT_DETECTION',\n              maxResults: 1\n            },\n            {\n              type: 'DOCUMENT_TEXT_DETECTION',\n              maxResults: 1\n            }\n          ]\n        }\n      ]\n    }\n\n    const response = await fetch(GOOGLE_VISION_ENDPOINT, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${accessToken.token}`\n      },\n      body: JSON.stringify(requestBody)\n    })\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error('Google Vision API error response:', errorText)\n      throw new Error(`Google Vision API error: ${response.status} ${response.statusText} - ${errorText}`)\n    }\n\n    const data: GoogleVisionResponse = await response.json()\n    console.log('Google Vision API response received')\n\n    if (data.responses[0]?.error) {\n      throw new Error(`Google Vision API error: ${data.responses[0].error.message}`)\n    }\n\n    // Get the full text from the response\n    const fullText = data.responses[0]?.fullTextAnnotation?.text || \n                    data.responses[0]?.textAnnotations?.[0]?.description || ''\n\n    console.log('Extracted text length:', fullText.length)\n    console.log('Text preview:', fullText.substring(0, 200) + '...')\n\n    return fullText\n\n  } catch (error) {\n    console.error('Google Vision API error:', error)\n    throw new Error(`Failed to extract text with Google Vision: ${error instanceof Error ? error.message : 'Unknown error'}`)\n  }\n}\n\n// Enhanced text cleaning and preprocessing\nfunction cleanText(text: string): string {\n  return text\n    .replace(/\\s+/g, ' ') // Replace multiple spaces with single space\n    .replace(/[\"\"]/g, '\"') // Normalize quotes\n    .replace(/['']/g, \"'\") // Normalize apostrophes\n    .replace(/\\u00A0/g, ' ') // Replace non-breaking spaces\n    .trim()\n}\n\n// Enhanced date parsing and formatting\nfunction parseDate(dateStr: string): string {\n  if (!dateStr) return ''\n\n  // Common LinkedIn date patterns\n  const patterns = [\n    /(\\w{3,9})\\s+(\\d{4})\\s*[-–—]\\s*(\\w{3,9})\\s+(\\d{4})/i, // \"Jan 2020 - Dec 2023\"\n    /(\\w{3,9})\\s+(\\d{4})\\s*[-–—]\\s*(Present|Current)/i,    // \"Jan 2020 - Present\"\n    /(\\d{4})\\s*[-–—]\\s*(\\d{4})/,                           // \"2020 - 2023\"\n    /(\\d{4})\\s*[-–—]\\s*(Present|Current)/i,                // \"2020 - Present\"\n    /(\\w{3,9})\\s+(\\d{4})/i                                 // \"Jan 2020\"\n  ]\n\n  for (const pattern of patterns) {\n    const match = dateStr.match(pattern)\n    if (match) {\n      return match[0].trim()\n    }\n  }\n\n  return dateStr.trim()\n}\n\n// Enhanced section detection\nfunction detectSection(text: string): string | null {\n  const sectionKeywords = {\n    'experience': ['experience', 'work history', 'employment', 'professional experience'],\n    'education': ['education', 'academic background', 'qualifications'],\n    'skills': ['skills', 'technical skills', 'competencies', 'expertise'],\n    'about': ['about', 'summary', 'profile', 'overview'],\n    'certifications': ['certifications', 'certificates', 'licenses'],\n    'languages': ['languages', 'language skills'],\n    'projects': ['projects', 'key projects'],\n    'achievements': ['achievements', 'accomplishments', 'awards']\n  }\n\n  const lowerText = text.toLowerCase()\n\n  for (const [section, keywords] of Object.entries(sectionKeywords)) {\n    if (keywords.some(keyword => lowerText.includes(keyword))) {\n      return section\n    }\n  }\n\n  return null\n}\n\nexport function parseLinkedInText(extractedText: string): LinkedInProfile {\n  console.log('Enhanced parsing of LinkedIn text...')\n  console.log('Raw text length:', extractedText.length)\n\n  // Clean and preprocess text\n  const cleanedText = cleanText(extractedText)\n  const lines = cleanedText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n  console.log('Cleaned text lines:', lines.length)\n\n  // Initialize profile structure\n  const profile: LinkedInProfile = {\n    personalInfo: {\n      name: '',\n      title: '',\n      location: '',\n      email: '',\n      phone: '',\n      linkedin: ''\n    },\n    summary: '',\n    experience: [],\n    education: [],\n    skills: [],\n    certifications: [],\n    languages: []\n  }\n\n  // Enhanced personal information parsing\n  if (lines.length > 0) {\n    // Find name (usually the largest text at the top, often in first 3 lines)\n    for (let i = 0; i < Math.min(3, lines.length); i++) {\n      const line = lines[i]\n      // Name is typically 2-4 words, contains letters, and is not a common LinkedIn element\n      if (line.length > 3 && line.length < 50 &&\n          /^[A-Za-z\\s\\-'\\.]+$/.test(line) &&\n          !line.toLowerCase().includes('connect') &&\n          !line.toLowerCase().includes('message') &&\n          !line.toLowerCase().includes('follow')) {\n        profile.personalInfo.name = line\n        break\n      }\n    }\n\n    // Find professional title/headline (usually after name, before location)\n    for (let i = 1; i < Math.min(5, lines.length); i++) {\n      const line = lines[i]\n      // Title often contains job-related keywords\n      if (line.length > 5 && line.length < 100 &&\n          (line.toLowerCase().includes('engineer') ||\n           line.toLowerCase().includes('developer') ||\n           line.toLowerCase().includes('manager') ||\n           line.toLowerCase().includes('director') ||\n           line.toLowerCase().includes('analyst') ||\n           line.toLowerCase().includes('consultant') ||\n           line.toLowerCase().includes('specialist') ||\n           line.toLowerCase().includes('lead') ||\n           line.toLowerCase().includes('senior') ||\n           line.toLowerCase().includes('junior') ||\n           line.toLowerCase().includes('at ') ||\n           /\\b(CEO|CTO|CFO|VP|President|Founder)\\b/i.test(line))) {\n        profile.personalInfo.title = line\n        break\n      }\n    }\n\n    // Enhanced location detection\n    for (let i = 0; i < Math.min(8, lines.length); i++) {\n      const line = lines[i].toLowerCase()\n      // Look for location patterns\n      if (line.includes(',') && (\n          // US locations\n          line.includes('ca') || line.includes('california') ||\n          line.includes('ny') || line.includes('new york') ||\n          line.includes('tx') || line.includes('texas') ||\n          line.includes('fl') || line.includes('florida') ||\n          line.includes('wa') || line.includes('washington') ||\n          line.includes('usa') || line.includes('united states') ||\n          // International locations\n          line.includes('uk') || line.includes('united kingdom') ||\n          line.includes('canada') || line.includes('australia') ||\n          line.includes('germany') || line.includes('france') ||\n          line.includes('india') || line.includes('singapore') ||\n          // Common city patterns\n          /\\b(san francisco|los angeles|chicago|boston|seattle|austin|denver|miami|atlanta)\\b/.test(line) ||\n          /\\b(london|paris|berlin|tokyo|sydney|toronto|vancouver)\\b/.test(line)\n      )) {\n        profile.personalInfo.location = lines[i]\n        break\n      }\n    }\n  }\n\n  // Look for email addresses\n  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/g\n  const emailMatch = extractedText.match(emailRegex)\n  if (emailMatch) {\n    profile.personalInfo.email = emailMatch[0]\n  }\n\n  // Look for phone numbers\n  const phoneRegex = /(\\+?1?[-.\\s]?)?\\(?([0-9]{3})\\)?[-.\\s]?([0-9]{3})[-.\\s]?([0-9]{4})/g\n  const phoneMatch = extractedText.match(phoneRegex)\n  if (phoneMatch) {\n    profile.personalInfo.phone = phoneMatch[0]\n  }\n\n  // Look for LinkedIn URL\n  const linkedinRegex = /linkedin\\.com\\/in\\/[a-zA-Z0-9-]+/g\n  const linkedinMatch = extractedText.match(linkedinRegex)\n  if (linkedinMatch) {\n    profile.personalInfo.linkedin = 'https://' + linkedinMatch[0]\n  }\n\n  // Look for About/Summary section\n  const aboutIndex = extractedText.toLowerCase().indexOf('about')\n  if (aboutIndex !== -1) {\n    const aboutSection = extractedText.substring(aboutIndex)\n    const nextSectionIndex = aboutSection.toLowerCase().search(/(experience|education|skills)/i)\n    if (nextSectionIndex !== -1) {\n      profile.summary = aboutSection.substring(5, nextSectionIndex).trim()\n    } else {\n      profile.summary = aboutSection.substring(5, Math.min(500, aboutSection.length)).trim()\n    }\n  }\n\n  // Enhanced Experience section parsing\n  const experienceIndex = cleanedText.toLowerCase().indexOf('experience')\n  if (experienceIndex !== -1) {\n    console.log('Found experience section')\n    const experienceSection = cleanedText.substring(experienceIndex)\n    const nextSectionIndex = experienceSection.toLowerCase().search(/(education|skills|certifications|languages|projects)/i)\n    const expText = nextSectionIndex !== -1 ?\n      experienceSection.substring(10, nextSectionIndex) :\n      experienceSection.substring(10, Math.min(2000, experienceSection.length))\n\n    const expLines = expText.split('\\n').filter(line => line.trim().length > 0)\n    let currentExp: any = null\n    let lineIndex = 0\n\n    while (lineIndex < expLines.length) {\n      const line = expLines[lineIndex].trim()\n\n      // Detect job title (often contains keywords or is followed by company)\n      if (line.length > 3 && (\n          line.toLowerCase().includes('engineer') ||\n          line.toLowerCase().includes('developer') ||\n          line.toLowerCase().includes('manager') ||\n          line.toLowerCase().includes('director') ||\n          line.toLowerCase().includes('analyst') ||\n          line.toLowerCase().includes('consultant') ||\n          line.toLowerCase().includes('specialist') ||\n          line.toLowerCase().includes('lead') ||\n          line.toLowerCase().includes('senior') ||\n          line.toLowerCase().includes('junior') ||\n          /\\b(CEO|CTO|CFO|VP|President|Founder|Intern)\\b/i.test(line) ||\n          // Or if next line looks like a company\n          (lineIndex + 1 < expLines.length &&\n           (expLines[lineIndex + 1].toLowerCase().includes('inc') ||\n            expLines[lineIndex + 1].toLowerCase().includes('corp') ||\n            expLines[lineIndex + 1].toLowerCase().includes('ltd') ||\n            expLines[lineIndex + 1].toLowerCase().includes('llc') ||\n            expLines[lineIndex + 1].toLowerCase().includes('company') ||\n            expLines[lineIndex + 1].toLowerCase().includes('technologies') ||\n            expLines[lineIndex + 1].toLowerCase().includes('solutions')))\n      )) {\n        // Save previous experience\n        if (currentExp && currentExp.position) {\n          profile.experience.push(currentExp)\n        }\n\n        // Start new experience\n        currentExp = {\n          company: '',\n          position: line,\n          duration: '',\n          description: '',\n          location: ''\n        }\n\n        // Look for company in next few lines\n        for (let i = lineIndex + 1; i < Math.min(lineIndex + 4, expLines.length); i++) {\n          const nextLine = expLines[i].trim()\n          if (nextLine.length > 2 && !parseDate(nextLine) &&\n              !nextLine.toLowerCase().includes('full-time') &&\n              !nextLine.toLowerCase().includes('part-time') &&\n              !nextLine.toLowerCase().includes('contract') &&\n              !nextLine.toLowerCase().includes('freelance')) {\n            currentExp.company = nextLine\n            lineIndex = i\n            break\n          }\n        }\n      }\n      // Detect duration/dates\n      else if (currentExp && parseDate(line) && line.length > 4) {\n        currentExp.duration = parseDate(line)\n      }\n      // Detect location\n      else if (currentExp && line.includes(',') && line.length < 50) {\n        currentExp.location = line\n      }\n      // Add to description\n      else if (currentExp && line.length > 10 &&\n               !line.toLowerCase().includes('connect') &&\n               !line.toLowerCase().includes('message')) {\n        currentExp.description += (currentExp.description ? ' ' : '') + line\n      }\n\n      lineIndex++\n    }\n\n    // Add final experience\n    if (currentExp && currentExp.position) {\n      profile.experience.push(currentExp)\n    }\n\n    console.log('Parsed experiences:', profile.experience.length)\n  }\n\n  // Parse Education section\n  const educationIndex = extractedText.toLowerCase().indexOf('education')\n  if (educationIndex !== -1) {\n    const educationSection = extractedText.substring(educationIndex)\n    const nextSectionIndex = educationSection.toLowerCase().search(/(skills|certifications|languages)/i)\n    const eduText = nextSectionIndex !== -1 ? \n      educationSection.substring(9, nextSectionIndex) : \n      educationSection.substring(9, Math.min(500, educationSection.length))\n    \n    const eduLines = eduText.split('\\n').filter(line => line.trim().length > 0)\n    if (eduLines.length > 0) {\n      profile.education.push({\n        institution: eduLines[0] || '',\n        degree: eduLines[1] || '',\n        field: eduLines[2] || '',\n        duration: eduLines.find(line => /\\d{4}/.test(line)) || '',\n        location: ''\n      })\n    }\n  }\n\n  // Enhanced Skills section parsing\n  const skillsIndex = cleanedText.toLowerCase().indexOf('skills')\n  if (skillsIndex !== -1) {\n    console.log('Found skills section')\n    const skillsSection = cleanedText.substring(skillsIndex)\n    const nextSectionIndex = skillsSection.toLowerCase().search(/(certifications|languages|recommendations|projects|achievements)/i)\n    const skillsText = nextSectionIndex !== -1 ?\n      skillsSection.substring(6, nextSectionIndex) :\n      skillsSection.substring(6, Math.min(500, skillsSection.length))\n\n    // Enhanced skill extraction with better filtering\n    const skillsList = skillsText\n      .split(/[,•·\\n\\|]/) // Split by various delimiters\n      .map(skill => skill.trim())\n      .filter(skill => {\n        // Filter out non-skills\n        if (skill.length < 2 || skill.length > 30) return false\n        if (/^\\d+$/.test(skill)) return false // Pure numbers\n        if (skill.toLowerCase().includes('show more')) return false\n        if (skill.toLowerCase().includes('see all')) return false\n        if (skill.toLowerCase().includes('skills')) return false\n        if (skill.toLowerCase().includes('endorsed')) return false\n        if (skill.toLowerCase().includes('connections')) return false\n\n        // Keep technical skills, tools, languages, frameworks\n        return /^[A-Za-z0-9\\s\\+\\#\\.\\-\\/]+$/.test(skill)\n      })\n      .slice(0, 25) // Limit to 25 skills\n\n    // Deduplicate skills\n    profile.skills = [...new Set(skillsList)]\n    console.log('Parsed skills:', profile.skills.length)\n  }\n\n  console.log('Parsing completed:', {\n    name: profile.personalInfo.name,\n    title: profile.personalInfo.title,\n    experienceCount: profile.experience.length,\n    skillsCount: profile.skills.length\n  })\n\n  return profile\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAEA,kCAAkC;AAClC,MAAM,yBAAyB,QAAQ,GAAG,CAAC,sBAAsB;AACjE,MAAM,yBAAyB;AAC/B,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAoB3C,eAAe,4BAA4B,WAAmB;IACnE,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,mDAAmD;QACnD,MAAM,OAAO,IAAI,oKAAA,CAAA,aAAU,CAAC;YAC1B,SAAS;YACT,QAAQ;gBAAC;aAAiD;QAC5D;QAEA,mBAAmB;QACnB,MAAM,aAAa,MAAM,KAAK,SAAS;QACvC,MAAM,cAAc,MAAM,WAAW,cAAc;QAEnD,IAAI,CAAC,YAAY,KAAK,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC;QAEZ,MAAM,cAAc;YAClB,UAAU;gBACR;oBACE,OAAO;wBACL,SAAS;oBACX;oBACA,UAAU;wBACR;4BACE,MAAM;4BACN,YAAY;wBACd;wBACA;4BACE,MAAM;4BACN,YAAY;wBACd;qBACD;gBACH;aACD;QACH;QAEA,MAAM,WAAW,MAAM,MAAM,wBAAwB;YACnD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,YAAY,KAAK,EAAE;YAChD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;QACrG;QAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;QACtD,QAAQ,GAAG,CAAC;QAEZ,IAAI,KAAK,SAAS,CAAC,EAAE,EAAE,OAAO;YAC5B,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE;QAC/E;QAEA,sCAAsC;QACtC,MAAM,WAAW,KAAK,SAAS,CAAC,EAAE,EAAE,oBAAoB,QACxC,KAAK,SAAS,CAAC,EAAE,EAAE,iBAAiB,CAAC,EAAE,EAAE,eAAe;QAExE,QAAQ,GAAG,CAAC,0BAA0B,SAAS,MAAM;QACrD,QAAQ,GAAG,CAAC,iBAAiB,SAAS,SAAS,CAAC,GAAG,OAAO;QAE1D,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM,IAAI,MAAM,CAAC,2CAA2C,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC1H;AACF;AAEA,2CAA2C;AAC3C,SAAS,UAAU,IAAY;IAC7B,OAAO,KACJ,OAAO,CAAC,QAAQ,KAAK,4CAA4C;KACjE,OAAO,CAAC,SAAS,KAAK,mBAAmB;KACzC,OAAO,CAAC,SAAS,KAAK,wBAAwB;KAC9C,OAAO,CAAC,WAAW,KAAK,8BAA8B;KACtD,IAAI;AACT;AAEA,uCAAuC;AACvC,SAAS,UAAU,OAAe;IAChC,IAAI,CAAC,SAAS,OAAO;IAErB,gCAAgC;IAChC,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA,uBAAuD,aAAa;KACrE;IAED,KAAK,MAAM,WAAW,SAAU;QAC9B,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,IAAI,OAAO;YACT,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;QACtB;IACF;IAEA,OAAO,QAAQ,IAAI;AACrB;AAEA,6BAA6B;AAC7B,SAAS,cAAc,IAAY;IACjC,MAAM,kBAAkB;QACtB,cAAc;YAAC;YAAc;YAAgB;YAAc;SAA0B;QACrF,aAAa;YAAC;YAAa;YAAuB;SAAiB;QACnE,UAAU;YAAC;YAAU;YAAoB;YAAgB;SAAY;QACrE,SAAS;YAAC;YAAS;YAAW;YAAW;SAAW;QACpD,kBAAkB;YAAC;YAAkB;YAAgB;SAAW;QAChE,aAAa;YAAC;YAAa;SAAkB;QAC7C,YAAY;YAAC;YAAY;SAAe;QACxC,gBAAgB;YAAC;YAAgB;YAAmB;SAAS;IAC/D;IAEA,MAAM,YAAY,KAAK,WAAW;IAElC,KAAK,MAAM,CAAC,SAAS,SAAS,IAAI,OAAO,OAAO,CAAC,iBAAkB;QACjE,IAAI,SAAS,IAAI,CAAC,CAAA,UAAW,UAAU,QAAQ,CAAC,WAAW;YACzD,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEO,SAAS,kBAAkB,aAAqB;IACrD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,oBAAoB,cAAc,MAAM;IAEpD,4BAA4B;IAC5B,MAAM,cAAc,UAAU;IAC9B,MAAM,QAAQ,YAAY,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAE5F,QAAQ,GAAG,CAAC,uBAAuB,MAAM,MAAM;IAE/C,+BAA+B;IAC/B,MAAM,UAA2B;QAC/B,cAAc;YACZ,MAAM;YACN,OAAO;YACP,UAAU;YACV,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QACA,SAAS;QACT,YAAY,EAAE;QACd,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,WAAW,EAAE;IACf;IAEA,wCAAwC;IACxC,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,0EAA0E;QAC1E,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,IAAK;YAClD,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,sFAAsF;YACtF,IAAI,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,GAAG,MACjC,qBAAqB,IAAI,CAAC,SAC1B,CAAC,KAAK,WAAW,GAAG,QAAQ,CAAC,cAC7B,CAAC,KAAK,WAAW,GAAG,QAAQ,CAAC,cAC7B,CAAC,KAAK,WAAW,GAAG,QAAQ,CAAC,WAAW;gBAC1C,QAAQ,YAAY,CAAC,IAAI,GAAG;gBAC5B;YACF;QACF;QAEA,yEAAyE;QACzE,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,IAAK;YAClD,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,4CAA4C;YAC5C,IAAI,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,GAAG,OACjC,CAAC,KAAK,WAAW,GAAG,QAAQ,CAAC,eAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,gBAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,cAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,eAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,cAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,iBAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,iBAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,WAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,aAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,aAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,UAC5B,0CAA0C,IAAI,CAAC,KAAK,GAAG;gBAC1D,QAAQ,YAAY,CAAC,KAAK,GAAG;gBAC7B;YACF;QACF;QAEA,8BAA8B;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,IAAK;YAClD,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,WAAW;YACjC,6BAA6B;YAC7B,IAAI,KAAK,QAAQ,CAAC,QAAQ,CACtB,eAAe;YACf,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,iBACrC,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,eACrC,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,YACrC,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,cACrC,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,iBACrC,KAAK,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC,oBACtC,0BAA0B;YAC1B,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,qBACrC,KAAK,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,gBACzC,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,aAC1C,KAAK,QAAQ,CAAC,YAAY,KAAK,QAAQ,CAAC,gBACxC,uBAAuB;YACvB,qFAAqF,IAAI,CAAC,SAC1F,2DAA2D,IAAI,CAAC,KACpE,GAAG;gBACD,QAAQ,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACxC;YACF;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,aAAa;IACnB,MAAM,aAAa,cAAc,KAAK,CAAC;IACvC,IAAI,YAAY;QACd,QAAQ,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE;IAC5C;IAEA,yBAAyB;IACzB,MAAM,aAAa;IACnB,MAAM,aAAa,cAAc,KAAK,CAAC;IACvC,IAAI,YAAY;QACd,QAAQ,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE;IAC5C;IAEA,wBAAwB;IACxB,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,cAAc,KAAK,CAAC;IAC1C,IAAI,eAAe;QACjB,QAAQ,YAAY,CAAC,QAAQ,GAAG,aAAa,aAAa,CAAC,EAAE;IAC/D;IAEA,iCAAiC;IACjC,MAAM,aAAa,cAAc,WAAW,GAAG,OAAO,CAAC;IACvD,IAAI,eAAe,CAAC,GAAG;QACrB,MAAM,eAAe,cAAc,SAAS,CAAC;QAC7C,MAAM,mBAAmB,aAAa,WAAW,GAAG,MAAM,CAAC;QAC3D,IAAI,qBAAqB,CAAC,GAAG;YAC3B,QAAQ,OAAO,GAAG,aAAa,SAAS,CAAC,GAAG,kBAAkB,IAAI;QACpE,OAAO;YACL,QAAQ,OAAO,GAAG,aAAa,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,aAAa,MAAM,GAAG,IAAI;QACtF;IACF;IAEA,sCAAsC;IACtC,MAAM,kBAAkB,YAAY,WAAW,GAAG,OAAO,CAAC;IAC1D,IAAI,oBAAoB,CAAC,GAAG;QAC1B,QAAQ,GAAG,CAAC;QACZ,MAAM,oBAAoB,YAAY,SAAS,CAAC;QAChD,MAAM,mBAAmB,kBAAkB,WAAW,GAAG,MAAM,CAAC;QAChE,MAAM,UAAU,qBAAqB,CAAC,IACpC,kBAAkB,SAAS,CAAC,IAAI,oBAChC,kBAAkB,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,kBAAkB,MAAM;QAEzE,MAAM,WAAW,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,MAAM,GAAG;QACzE,IAAI,aAAkB;QACtB,IAAI,YAAY;QAEhB,MAAO,YAAY,SAAS,MAAM,CAAE;YAClC,MAAM,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI;YAErC,uEAAuE;YACvE,IAAI,KAAK,MAAM,GAAG,KAAK,CACnB,KAAK,WAAW,GAAG,QAAQ,CAAC,eAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,gBAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,cAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,eAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,cAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,iBAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,iBAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,WAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,aAC5B,KAAK,WAAW,GAAG,QAAQ,CAAC,aAC5B,iDAAiD,IAAI,CAAC,SAErD,YAAY,IAAI,SAAS,MAAM,IAC/B,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC/C,QAAQ,CAAC,YAAY,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAC/C,QAAQ,CAAC,YAAY,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC/C,QAAQ,CAAC,YAAY,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC/C,QAAQ,CAAC,YAAY,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,cAC/C,QAAQ,CAAC,YAAY,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAC/C,QAAQ,CAAC,YAAY,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,CACjE,GAAG;gBACD,2BAA2B;gBAC3B,IAAI,cAAc,WAAW,QAAQ,EAAE;oBACrC,QAAQ,UAAU,CAAC,IAAI,CAAC;gBAC1B;gBAEA,uBAAuB;gBACvB,aAAa;oBACX,SAAS;oBACT,UAAU;oBACV,UAAU;oBACV,aAAa;oBACb,UAAU;gBACZ;gBAEA,qCAAqC;gBACrC,IAAK,IAAI,IAAI,YAAY,GAAG,IAAI,KAAK,GAAG,CAAC,YAAY,GAAG,SAAS,MAAM,GAAG,IAAK;oBAC7E,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,IAAI;oBACjC,IAAI,SAAS,MAAM,GAAG,KAAK,CAAC,UAAU,aAClC,CAAC,SAAS,WAAW,GAAG,QAAQ,CAAC,gBACjC,CAAC,SAAS,WAAW,GAAG,QAAQ,CAAC,gBACjC,CAAC,SAAS,WAAW,GAAG,QAAQ,CAAC,eACjC,CAAC,SAAS,WAAW,GAAG,QAAQ,CAAC,cAAc;wBACjD,WAAW,OAAO,GAAG;wBACrB,YAAY;wBACZ;oBACF;gBACF;YACF,OAEK,IAAI,cAAc,UAAU,SAAS,KAAK,MAAM,GAAG,GAAG;gBACzD,WAAW,QAAQ,GAAG,UAAU;YAClC,OAEK,IAAI,cAAc,KAAK,QAAQ,CAAC,QAAQ,KAAK,MAAM,GAAG,IAAI;gBAC7D,WAAW,QAAQ,GAAG;YACxB,OAEK,IAAI,cAAc,KAAK,MAAM,GAAG,MAC5B,CAAC,KAAK,WAAW,GAAG,QAAQ,CAAC,cAC7B,CAAC,KAAK,WAAW,GAAG,QAAQ,CAAC,YAAY;gBAChD,WAAW,WAAW,IAAI,CAAC,WAAW,WAAW,GAAG,MAAM,EAAE,IAAI;YAClE;YAEA;QACF;QAEA,uBAAuB;QACvB,IAAI,cAAc,WAAW,QAAQ,EAAE;YACrC,QAAQ,UAAU,CAAC,IAAI,CAAC;QAC1B;QAEA,QAAQ,GAAG,CAAC,uBAAuB,QAAQ,UAAU,CAAC,MAAM;IAC9D;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,cAAc,WAAW,GAAG,OAAO,CAAC;IAC3D,IAAI,mBAAmB,CAAC,GAAG;QACzB,MAAM,mBAAmB,cAAc,SAAS,CAAC;QACjD,MAAM,mBAAmB,iBAAiB,WAAW,GAAG,MAAM,CAAC;QAC/D,MAAM,UAAU,qBAAqB,CAAC,IACpC,iBAAiB,SAAS,CAAC,GAAG,oBAC9B,iBAAiB,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,iBAAiB,MAAM;QAErE,MAAM,WAAW,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,MAAM,GAAG;QACzE,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,QAAQ,SAAS,CAAC,IAAI,CAAC;gBACrB,aAAa,QAAQ,CAAC,EAAE,IAAI;gBAC5B,QAAQ,QAAQ,CAAC,EAAE,IAAI;gBACvB,OAAO,QAAQ,CAAC,EAAE,IAAI;gBACtB,UAAU,SAAS,IAAI,CAAC,CAAA,OAAQ,QAAQ,IAAI,CAAC,UAAU;gBACvD,UAAU;YACZ;QACF;IACF;IAEA,kCAAkC;IAClC,MAAM,cAAc,YAAY,WAAW,GAAG,OAAO,CAAC;IACtD,IAAI,gBAAgB,CAAC,GAAG;QACtB,QAAQ,GAAG,CAAC;QACZ,MAAM,gBAAgB,YAAY,SAAS,CAAC;QAC5C,MAAM,mBAAmB,cAAc,WAAW,GAAG,MAAM,CAAC;QAC5D,MAAM,aAAa,qBAAqB,CAAC,IACvC,cAAc,SAAS,CAAC,GAAG,oBAC3B,cAAc,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,cAAc,MAAM;QAE/D,kDAAkD;QAClD,MAAM,aAAa,WAChB,KAAK,CAAC,aAAa,8BAA8B;SACjD,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA;YACN,wBAAwB;YACxB,IAAI,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,IAAI,OAAO;YAClD,IAAI,QAAQ,IAAI,CAAC,QAAQ,OAAO,MAAM,eAAe;;YACrD,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,OAAO;YACtD,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,OAAO;YACpD,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,OAAO;YACnD,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,OAAO;YACrD,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,gBAAgB,OAAO;YAExD,sDAAsD;YACtD,OAAO,6BAA6B,IAAI,CAAC;QAC3C,GACC,KAAK,CAAC,GAAG,IAAI,qBAAqB;;QAErC,qBAAqB;QACrB,QAAQ,MAAM,GAAG;eAAI,IAAI,IAAI;SAAY;QACzC,QAAQ,GAAG,CAAC,kBAAkB,QAAQ,MAAM,CAAC,MAAM;IACrD;IAEA,QAAQ,GAAG,CAAC,sBAAsB;QAChC,MAAM,QAAQ,YAAY,CAAC,IAAI;QAC/B,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,iBAAiB,QAAQ,UAAU,CAAC,MAAM;QAC1C,aAAa,QAAQ,MAAM,CAAC,MAAM;IACpC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/api/test-google-vision/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { extractTextWithGoogleVision } from '@/lib/google-vision'\n\nexport async function GET() {\n  try {\n    // Test will use the credentials file directly\n\n    // Test with a simple image (1x1 pixel PNG with some text)\n    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='\n\n    console.log('Testing Google Vision API...')\n\n    const extractedText = await extractTextWithGoogleVision(testImageBase64)\n\n    return NextResponse.json({\n      success: true,\n      message: 'Google Vision API is working with service account',\n      extractedText: extractedText || 'No text found in test image (this is normal)',\n      serviceAccountConfigured: true\n    })\n\n  } catch (error) {\n    console.error('Google Vision API test failed:', error)\n    \n    return NextResponse.json({\n      error: 'Google Vision API test failed',\n      details: error instanceof Error ? error.message : 'Unknown error',\n      instructions: [\n        '1. Get API key from Google Cloud Console',\n        '2. Enable Vision API in your Google Cloud project',\n        '3. Add GOOGLE_VISION_API_KEY to .env.local',\n        '4. Make sure billing is enabled for your Google Cloud project'\n      ]\n    }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Test will use the credentials file directly\n\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n\n    if (!file) {\n      return NextResponse.json({\n        error: 'No file provided'\n      }, { status: 400 })\n    }\n\n    // Convert file to base64\n    const arrayBuffer = await file.arrayBuffer()\n    const base64 = Buffer.from(arrayBuffer).toString('base64')\n\n    console.log('Testing Google Vision with uploaded file...')\n    console.log('File type:', file.type)\n    console.log('File size:', file.size)\n\n    const extractedText = await extractTextWithGoogleVision(base64)\n\n    return NextResponse.json({\n      success: true,\n      message: 'Google Vision text extraction successful',\n      extractedText: extractedText,\n      textLength: extractedText.length,\n      preview: extractedText.substring(0, 500) + (extractedText.length > 500 ? '...' : '')\n    })\n\n  } catch (error) {\n    console.error('Google Vision file test failed:', error)\n    \n    return NextResponse.json({\n      error: 'Google Vision file test failed',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,8CAA8C;QAE9C,0DAA0D;QAC1D,MAAM,kBAAkB;QAExB,QAAQ,GAAG,CAAC;QAEZ,MAAM,gBAAgB,MAAM,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD,EAAE;QAExD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,eAAe,iBAAiB;YAChC,0BAA0B;QAC5B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;QACH,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,8CAA8C;QAE9C,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;QAEjD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;QACnC,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;QAEnC,MAAM,gBAAgB,MAAM,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD,EAAE;QAExD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,eAAe;YACf,YAAY,cAAc,MAAM;YAChC,SAAS,cAAc,SAAS,CAAC,GAAG,OAAO,CAAC,cAAc,MAAM,GAAG,MAAM,QAAQ,EAAE;QACrF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}