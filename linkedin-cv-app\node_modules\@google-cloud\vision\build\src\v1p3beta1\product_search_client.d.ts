import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, LROperation, PaginationCallback } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  Manages Products and ProductSets of reference images for use in product
 *  search. It uses the following resource model:
 *
 *  - The API has a collection of {@link protos.google.cloud.vision.v1p3beta1.ProductSet|ProductSet} resources, named
 *  `projects/* /locations/* /productSets/*`, which acts as a way to put different
 *  products into groups to limit identification.
 *
 *  In parallel,
 *
 *  - The API has a collection of {@link protos.google.cloud.vision.v1p3beta1.Product|Product} resources, named
 *    `projects/* /locations/* /products/*`
 *
 *  - Each {@link protos.google.cloud.vision.v1p3beta1.Product|Product} has a collection of {@link protos.google.cloud.vision.v1p3beta1.ReferenceImage|ReferenceImage} resources, named
 *    `projects/* /locations/* /products/* /referenceImages/*`
 * @class
 * @memberof v1p3beta1
 */
export declare class ProductSearchClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    private _log;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    operationsClient: gax.OperationsClient;
    productSearchStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of ProductSearchClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new ProductSearchClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): string[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Creates and returns a new ProductSet resource.
     *
     * Possible errors:
     *
     * * Returns INVALID_ARGUMENT if display_name is missing, or is longer than
     *   4096 characters.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project in which the ProductSet should be created.
     *
     *   Format is `projects/PROJECT_ID/locations/LOC_ID`.
     * @param {google.cloud.vision.v1p3beta1.ProductSet} request.productSet
     *   Required. The ProductSet to create.
     * @param {string} request.productSetId
     *   A user-supplied resource id for this ProductSet. If set, the server will
     *   attempt to use this value as the resource id. If it is already in use, an
     *   error is returned with code ALREADY_EXISTS. Must be at most 128 characters
     *   long. It cannot contain the character `/`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.vision.v1p3beta1.ProductSet|ProductSet}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.create_product_set.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_CreateProductSet_async
     */
    createProductSet(request?: protos.google.cloud.vision.v1p3beta1.ICreateProductSetRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IProductSet,
        protos.google.cloud.vision.v1p3beta1.ICreateProductSetRequest | undefined,
        {} | undefined
    ]>;
    createProductSet(request: protos.google.cloud.vision.v1p3beta1.ICreateProductSetRequest, options: CallOptions, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProductSet, protos.google.cloud.vision.v1p3beta1.ICreateProductSetRequest | null | undefined, {} | null | undefined>): void;
    createProductSet(request: protos.google.cloud.vision.v1p3beta1.ICreateProductSetRequest, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProductSet, protos.google.cloud.vision.v1p3beta1.ICreateProductSetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets information associated with a ProductSet.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the ProductSet does not exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. Resource name of the ProductSet to get.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.vision.v1p3beta1.ProductSet|ProductSet}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.get_product_set.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_GetProductSet_async
     */
    getProductSet(request?: protos.google.cloud.vision.v1p3beta1.IGetProductSetRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IProductSet,
        protos.google.cloud.vision.v1p3beta1.IGetProductSetRequest | undefined,
        {} | undefined
    ]>;
    getProductSet(request: protos.google.cloud.vision.v1p3beta1.IGetProductSetRequest, options: CallOptions, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProductSet, protos.google.cloud.vision.v1p3beta1.IGetProductSetRequest | null | undefined, {} | null | undefined>): void;
    getProductSet(request: protos.google.cloud.vision.v1p3beta1.IGetProductSetRequest, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProductSet, protos.google.cloud.vision.v1p3beta1.IGetProductSetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Makes changes to a ProductSet resource.
     * Only display_name can be updated currently.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the ProductSet does not exist.
     * * Returns INVALID_ARGUMENT if display_name is present in update_mask but
     *   missing from the request or longer than 4096 characters.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.vision.v1p3beta1.ProductSet} request.productSet
     *   Required. The ProductSet resource which replaces the one on the server.
     * @param {google.protobuf.FieldMask} request.updateMask
     *   The {@link protos.google.protobuf.FieldMask|FieldMask} that specifies which fields to
     *   update.
     *   If update_mask isn't specified, all mutable fields are to be updated.
     *   Valid mask path is `display_name`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.vision.v1p3beta1.ProductSet|ProductSet}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.update_product_set.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_UpdateProductSet_async
     */
    updateProductSet(request?: protos.google.cloud.vision.v1p3beta1.IUpdateProductSetRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IProductSet,
        protos.google.cloud.vision.v1p3beta1.IUpdateProductSetRequest | undefined,
        {} | undefined
    ]>;
    updateProductSet(request: protos.google.cloud.vision.v1p3beta1.IUpdateProductSetRequest, options: CallOptions, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProductSet, protos.google.cloud.vision.v1p3beta1.IUpdateProductSetRequest | null | undefined, {} | null | undefined>): void;
    updateProductSet(request: protos.google.cloud.vision.v1p3beta1.IUpdateProductSetRequest, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProductSet, protos.google.cloud.vision.v1p3beta1.IUpdateProductSetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Permanently deletes a ProductSet. All Products and ReferenceImages in the
     * ProductSet will be deleted.
     *
     * The actual image files are not deleted from Google Cloud Storage.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the ProductSet does not exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. Resource name of the ProductSet to delete.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.delete_product_set.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_DeleteProductSet_async
     */
    deleteProductSet(request?: protos.google.cloud.vision.v1p3beta1.IDeleteProductSetRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        protos.google.cloud.vision.v1p3beta1.IDeleteProductSetRequest | undefined,
        {} | undefined
    ]>;
    deleteProductSet(request: protos.google.cloud.vision.v1p3beta1.IDeleteProductSetRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IDeleteProductSetRequest | null | undefined, {} | null | undefined>): void;
    deleteProductSet(request: protos.google.cloud.vision.v1p3beta1.IDeleteProductSetRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IDeleteProductSetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates and returns a new product resource.
     *
     * Possible errors:
     *
     * * Returns INVALID_ARGUMENT if display_name is missing or longer than 4096
     *   characters.
     * * Returns INVALID_ARGUMENT if description is longer than 4096 characters.
     * * Returns INVALID_ARGUMENT if product_category is missing or invalid.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project in which the Product should be created.
     *
     *   Format is
     *   `projects/PROJECT_ID/locations/LOC_ID`.
     * @param {google.cloud.vision.v1p3beta1.Product} request.product
     *   Required. The product to create.
     * @param {string} request.productId
     *   A user-supplied resource id for this Product. If set, the server will
     *   attempt to use this value as the resource id. If it is already in use, an
     *   error is returned with code ALREADY_EXISTS. Must be at most 128 characters
     *   long. It cannot contain the character `/`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.vision.v1p3beta1.Product|Product}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.create_product.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_CreateProduct_async
     */
    createProduct(request?: protos.google.cloud.vision.v1p3beta1.ICreateProductRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IProduct,
        protos.google.cloud.vision.v1p3beta1.ICreateProductRequest | undefined,
        {} | undefined
    ]>;
    createProduct(request: protos.google.cloud.vision.v1p3beta1.ICreateProductRequest, options: CallOptions, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProduct, protos.google.cloud.vision.v1p3beta1.ICreateProductRequest | null | undefined, {} | null | undefined>): void;
    createProduct(request: protos.google.cloud.vision.v1p3beta1.ICreateProductRequest, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProduct, protos.google.cloud.vision.v1p3beta1.ICreateProductRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets information associated with a Product.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the Product does not exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. Resource name of the Product to get.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.vision.v1p3beta1.Product|Product}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.get_product.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_GetProduct_async
     */
    getProduct(request?: protos.google.cloud.vision.v1p3beta1.IGetProductRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IProduct,
        protos.google.cloud.vision.v1p3beta1.IGetProductRequest | undefined,
        {} | undefined
    ]>;
    getProduct(request: protos.google.cloud.vision.v1p3beta1.IGetProductRequest, options: CallOptions, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProduct, protos.google.cloud.vision.v1p3beta1.IGetProductRequest | null | undefined, {} | null | undefined>): void;
    getProduct(request: protos.google.cloud.vision.v1p3beta1.IGetProductRequest, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProduct, protos.google.cloud.vision.v1p3beta1.IGetProductRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Makes changes to a Product resource.
     * Only display_name, description and labels can be updated right now.
     *
     * If labels are updated, the change will not be reflected in queries until
     * the next index time.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the Product does not exist.
     * * Returns INVALID_ARGUMENT if display_name is present in update_mask but is
     *   missing from the request or longer than 4096 characters.
     * * Returns INVALID_ARGUMENT if description is present in update_mask but is
     *   longer than 4096 characters.
     * * Returns INVALID_ARGUMENT if product_category is present in update_mask.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.vision.v1p3beta1.Product} request.product
     *   Required. The Product resource which replaces the one on the server.
     *   product.name is immutable.
     * @param {google.protobuf.FieldMask} request.updateMask
     *   The {@link protos.google.protobuf.FieldMask|FieldMask} that specifies which fields
     *   to update.
     *   If update_mask isn't specified, all mutable fields are to be updated.
     *   Valid mask paths include `product_labels`, `display_name`, and
     *   `description`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.vision.v1p3beta1.Product|Product}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.update_product.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_UpdateProduct_async
     */
    updateProduct(request?: protos.google.cloud.vision.v1p3beta1.IUpdateProductRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IProduct,
        protos.google.cloud.vision.v1p3beta1.IUpdateProductRequest | undefined,
        {} | undefined
    ]>;
    updateProduct(request: protos.google.cloud.vision.v1p3beta1.IUpdateProductRequest, options: CallOptions, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProduct, protos.google.cloud.vision.v1p3beta1.IUpdateProductRequest | null | undefined, {} | null | undefined>): void;
    updateProduct(request: protos.google.cloud.vision.v1p3beta1.IUpdateProductRequest, callback: Callback<protos.google.cloud.vision.v1p3beta1.IProduct, protos.google.cloud.vision.v1p3beta1.IUpdateProductRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Permanently deletes a product and its reference images.
     *
     * Metadata of the product and all its images will be deleted right away, but
     * search queries against ProductSets containing the product may still work
     * until all related caches are refreshed.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the product does not exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. Resource name of product to delete.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.delete_product.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_DeleteProduct_async
     */
    deleteProduct(request?: protos.google.cloud.vision.v1p3beta1.IDeleteProductRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        protos.google.cloud.vision.v1p3beta1.IDeleteProductRequest | undefined,
        {} | undefined
    ]>;
    deleteProduct(request: protos.google.cloud.vision.v1p3beta1.IDeleteProductRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IDeleteProductRequest | null | undefined, {} | null | undefined>): void;
    deleteProduct(request: protos.google.cloud.vision.v1p3beta1.IDeleteProductRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IDeleteProductRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates and returns a new ReferenceImage resource.
     *
     * The `bounding_poly` field is optional. If `bounding_poly` is not specified,
     * the system will try to detect regions of interest in the image that are
     * compatible with the product_category on the parent product. If it is
     * specified, detection is ALWAYS skipped. The system converts polygons into
     * non-rotated rectangles.
     *
     * Note that the pipeline will resize the image if the image resolution is too
     * large to process (above 50MP).
     *
     * Possible errors:
     *
     * * Returns INVALID_ARGUMENT if the image_uri is missing or longer than 4096
     *   characters.
     * * Returns INVALID_ARGUMENT if the product does not exist.
     * * Returns INVALID_ARGUMENT if bounding_poly is not provided, and nothing
     *   compatible with the parent product's product_category is detected.
     * * Returns INVALID_ARGUMENT if bounding_poly contains more than 10 polygons.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Resource name of the product in which to create the reference image.
     *
     *   Format is
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
     * @param {google.cloud.vision.v1p3beta1.ReferenceImage} request.referenceImage
     *   Required. The reference image to create.
     *   If an image ID is specified, it is ignored.
     * @param {string} request.referenceImageId
     *   A user-supplied resource id for the ReferenceImage to be added. If set,
     *   the server will attempt to use this value as the resource id. If it is
     *   already in use, an error is returned with code ALREADY_EXISTS. Must be at
     *   most 128 characters long. It cannot contain the character `/`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.vision.v1p3beta1.ReferenceImage|ReferenceImage}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.create_reference_image.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_CreateReferenceImage_async
     */
    createReferenceImage(request?: protos.google.cloud.vision.v1p3beta1.ICreateReferenceImageRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IReferenceImage,
        (protos.google.cloud.vision.v1p3beta1.ICreateReferenceImageRequest | undefined),
        {} | undefined
    ]>;
    createReferenceImage(request: protos.google.cloud.vision.v1p3beta1.ICreateReferenceImageRequest, options: CallOptions, callback: Callback<protos.google.cloud.vision.v1p3beta1.IReferenceImage, protos.google.cloud.vision.v1p3beta1.ICreateReferenceImageRequest | null | undefined, {} | null | undefined>): void;
    createReferenceImage(request: protos.google.cloud.vision.v1p3beta1.ICreateReferenceImageRequest, callback: Callback<protos.google.cloud.vision.v1p3beta1.IReferenceImage, protos.google.cloud.vision.v1p3beta1.ICreateReferenceImageRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Permanently deletes a reference image.
     *
     * The image metadata will be deleted right away, but search queries
     * against ProductSets containing the image may still work until all related
     * caches are refreshed.
     *
     * The actual image files are not deleted from Google Cloud Storage.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the reference image does not exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the reference image to delete.
     *
     *   Format is:
     *
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceImages/IMAGE_ID`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.delete_reference_image.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_DeleteReferenceImage_async
     */
    deleteReferenceImage(request?: protos.google.cloud.vision.v1p3beta1.IDeleteReferenceImageRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.cloud.vision.v1p3beta1.IDeleteReferenceImageRequest | undefined),
        {} | undefined
    ]>;
    deleteReferenceImage(request: protos.google.cloud.vision.v1p3beta1.IDeleteReferenceImageRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IDeleteReferenceImageRequest | null | undefined, {} | null | undefined>): void;
    deleteReferenceImage(request: protos.google.cloud.vision.v1p3beta1.IDeleteReferenceImageRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IDeleteReferenceImageRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets information associated with a ReferenceImage.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the specified image does not exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the ReferenceImage to get.
     *
     *   Format is:
     *
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceImages/IMAGE_ID`.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.vision.v1p3beta1.ReferenceImage|ReferenceImage}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.get_reference_image.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_GetReferenceImage_async
     */
    getReferenceImage(request?: protos.google.cloud.vision.v1p3beta1.IGetReferenceImageRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IReferenceImage,
        (protos.google.cloud.vision.v1p3beta1.IGetReferenceImageRequest | undefined),
        {} | undefined
    ]>;
    getReferenceImage(request: protos.google.cloud.vision.v1p3beta1.IGetReferenceImageRequest, options: CallOptions, callback: Callback<protos.google.cloud.vision.v1p3beta1.IReferenceImage, protos.google.cloud.vision.v1p3beta1.IGetReferenceImageRequest | null | undefined, {} | null | undefined>): void;
    getReferenceImage(request: protos.google.cloud.vision.v1p3beta1.IGetReferenceImageRequest, callback: Callback<protos.google.cloud.vision.v1p3beta1.IReferenceImage, protos.google.cloud.vision.v1p3beta1.IGetReferenceImageRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Adds a Product to the specified ProductSet. If the Product is already
     * present, no change is made.
     *
     * One Product can be added to at most 100 ProductSets.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the Product or the ProductSet doesn't exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name for the ProductSet to modify.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
     * @param {string} request.product
     *   Required. The resource name for the Product to be added to this ProductSet.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.add_product_to_product_set.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_AddProductToProductSet_async
     */
    addProductToProductSet(request?: protos.google.cloud.vision.v1p3beta1.IAddProductToProductSetRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.cloud.vision.v1p3beta1.IAddProductToProductSetRequest | undefined),
        {} | undefined
    ]>;
    addProductToProductSet(request: protos.google.cloud.vision.v1p3beta1.IAddProductToProductSetRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IAddProductToProductSetRequest | null | undefined, {} | null | undefined>): void;
    addProductToProductSet(request: protos.google.cloud.vision.v1p3beta1.IAddProductToProductSetRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IAddProductToProductSetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Removes a Product from the specified ProductSet.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND If the Product is not found under the ProductSet.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name for the ProductSet to modify.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
     * @param {string} request.product
     *   Required. The resource name for the Product to be removed from this ProductSet.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.remove_product_from_product_set.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_RemoveProductFromProductSet_async
     */
    removeProductFromProductSet(request?: protos.google.cloud.vision.v1p3beta1.IRemoveProductFromProductSetRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        (protos.google.cloud.vision.v1p3beta1.IRemoveProductFromProductSetRequest | undefined),
        {} | undefined
    ]>;
    removeProductFromProductSet(request: protos.google.cloud.vision.v1p3beta1.IRemoveProductFromProductSetRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IRemoveProductFromProductSetRequest | null | undefined, {} | null | undefined>): void;
    removeProductFromProductSet(request: protos.google.cloud.vision.v1p3beta1.IRemoveProductFromProductSetRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.vision.v1p3beta1.IRemoveProductFromProductSetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Asynchronous API that imports a list of reference images to specified
     * product sets based on a list of image information.
     *
     * The {@link protos.google.longrunning.Operation|google.longrunning.Operation} API can be
     * used to keep track of the progress and results of the request.
     * `Operation.metadata` contains `BatchOperationMetadata`. (progress)
     * `Operation.response` contains `ImportProductSetsResponse`. (results)
     *
     * The input source of this method is a csv file on Google Cloud Storage.
     * For the format of the csv file please see
     * {@link protos.google.cloud.vision.v1p3beta1.ImportProductSetsGcsSource.csv_file_uri|ImportProductSetsGcsSource.csv_file_uri}.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project in which the ProductSets should be imported.
     *
     *   Format is `projects/PROJECT_ID/locations/LOC_ID`.
     * @param {google.cloud.vision.v1p3beta1.ImportProductSetsInputConfig} request.inputConfig
     *   Required. The input content for the list of requests.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.import_product_sets.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_ImportProductSets_async
     */
    importProductSets(request?: protos.google.cloud.vision.v1p3beta1.IImportProductSetsRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.vision.v1p3beta1.IImportProductSetsResponse, protos.google.cloud.vision.v1p3beta1.IBatchOperationMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    importProductSets(request: protos.google.cloud.vision.v1p3beta1.IImportProductSetsRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.vision.v1p3beta1.IImportProductSetsResponse, protos.google.cloud.vision.v1p3beta1.IBatchOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    importProductSets(request: protos.google.cloud.vision.v1p3beta1.IImportProductSetsRequest, callback: Callback<LROperation<protos.google.cloud.vision.v1p3beta1.IImportProductSetsResponse, protos.google.cloud.vision.v1p3beta1.IBatchOperationMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `importProductSets()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.import_product_sets.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_ImportProductSets_async
     */
    checkImportProductSetsProgress(name: string): Promise<LROperation<protos.google.cloud.vision.v1p3beta1.ImportProductSetsResponse, protos.google.cloud.vision.v1p3beta1.BatchOperationMetadata>>;
    /**
     * Lists ProductSets in an unspecified order.
     *
     * Possible errors:
     *
     * * Returns INVALID_ARGUMENT if page_size is greater than 100, or less
     *   than 1.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project from which ProductSets should be listed.
     *
     *   Format is `projects/PROJECT_ID/locations/LOC_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.vision.v1p3beta1.ProductSet|ProductSet}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listProductSetsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listProductSets(request?: protos.google.cloud.vision.v1p3beta1.IListProductSetsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IProductSet[],
        protos.google.cloud.vision.v1p3beta1.IListProductSetsRequest | null,
        protos.google.cloud.vision.v1p3beta1.IListProductSetsResponse
    ]>;
    listProductSets(request: protos.google.cloud.vision.v1p3beta1.IListProductSetsRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.vision.v1p3beta1.IListProductSetsRequest, protos.google.cloud.vision.v1p3beta1.IListProductSetsResponse | null | undefined, protos.google.cloud.vision.v1p3beta1.IProductSet>): void;
    listProductSets(request: protos.google.cloud.vision.v1p3beta1.IListProductSetsRequest, callback: PaginationCallback<protos.google.cloud.vision.v1p3beta1.IListProductSetsRequest, protos.google.cloud.vision.v1p3beta1.IListProductSetsResponse | null | undefined, protos.google.cloud.vision.v1p3beta1.IProductSet>): void;
    /**
     * Equivalent to `listProductSets`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project from which ProductSets should be listed.
     *
     *   Format is `projects/PROJECT_ID/locations/LOC_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.vision.v1p3beta1.ProductSet|ProductSet} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listProductSetsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listProductSetsStream(request?: protos.google.cloud.vision.v1p3beta1.IListProductSetsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listProductSets`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project from which ProductSets should be listed.
     *
     *   Format is `projects/PROJECT_ID/locations/LOC_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.vision.v1p3beta1.ProductSet|ProductSet}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.list_product_sets.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_ListProductSets_async
     */
    listProductSetsAsync(request?: protos.google.cloud.vision.v1p3beta1.IListProductSetsRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.vision.v1p3beta1.IProductSet>;
    /**
     * Lists products in an unspecified order.
     *
     * Possible errors:
     *
     * * Returns INVALID_ARGUMENT if page_size is greater than 100 or less than 1.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project OR ProductSet from which Products should be listed.
     *
     *   Format:
     *   `projects/PROJECT_ID/locations/LOC_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.vision.v1p3beta1.Product|Product}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listProductsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listProducts(request?: protos.google.cloud.vision.v1p3beta1.IListProductsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IProduct[],
        protos.google.cloud.vision.v1p3beta1.IListProductsRequest | null,
        protos.google.cloud.vision.v1p3beta1.IListProductsResponse
    ]>;
    listProducts(request: protos.google.cloud.vision.v1p3beta1.IListProductsRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.vision.v1p3beta1.IListProductsRequest, protos.google.cloud.vision.v1p3beta1.IListProductsResponse | null | undefined, protos.google.cloud.vision.v1p3beta1.IProduct>): void;
    listProducts(request: protos.google.cloud.vision.v1p3beta1.IListProductsRequest, callback: PaginationCallback<protos.google.cloud.vision.v1p3beta1.IListProductsRequest, protos.google.cloud.vision.v1p3beta1.IListProductsResponse | null | undefined, protos.google.cloud.vision.v1p3beta1.IProduct>): void;
    /**
     * Equivalent to `listProducts`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project OR ProductSet from which Products should be listed.
     *
     *   Format:
     *   `projects/PROJECT_ID/locations/LOC_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.vision.v1p3beta1.Product|Product} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listProductsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listProductsStream(request?: protos.google.cloud.vision.v1p3beta1.IListProductsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listProducts`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project OR ProductSet from which Products should be listed.
     *
     *   Format:
     *   `projects/PROJECT_ID/locations/LOC_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.vision.v1p3beta1.Product|Product}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.list_products.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_ListProducts_async
     */
    listProductsAsync(request?: protos.google.cloud.vision.v1p3beta1.IListProductsRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.vision.v1p3beta1.IProduct>;
    /**
     * Lists reference images.
     *
     * Possible errors:
     *
     * * Returns NOT_FOUND if the parent product does not exist.
     * * Returns INVALID_ARGUMENT if the page_size is greater than 100, or less
     *   than 1.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Resource name of the product containing the reference images.
     *
     *   Format is
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   A token identifying a page of results to be returned. This is the value
     *   of `nextPageToken` returned in a previous reference image list request.
     *
     *   Defaults to the first page if not specified.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.vision.v1p3beta1.ReferenceImage|ReferenceImage}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listReferenceImagesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listReferenceImages(request?: protos.google.cloud.vision.v1p3beta1.IListReferenceImagesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IReferenceImage[],
        protos.google.cloud.vision.v1p3beta1.IListReferenceImagesRequest | null,
        protos.google.cloud.vision.v1p3beta1.IListReferenceImagesResponse
    ]>;
    listReferenceImages(request: protos.google.cloud.vision.v1p3beta1.IListReferenceImagesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.vision.v1p3beta1.IListReferenceImagesRequest, protos.google.cloud.vision.v1p3beta1.IListReferenceImagesResponse | null | undefined, protos.google.cloud.vision.v1p3beta1.IReferenceImage>): void;
    listReferenceImages(request: protos.google.cloud.vision.v1p3beta1.IListReferenceImagesRequest, callback: PaginationCallback<protos.google.cloud.vision.v1p3beta1.IListReferenceImagesRequest, protos.google.cloud.vision.v1p3beta1.IListReferenceImagesResponse | null | undefined, protos.google.cloud.vision.v1p3beta1.IReferenceImage>): void;
    /**
     * Equivalent to `listReferenceImages`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Resource name of the product containing the reference images.
     *
     *   Format is
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   A token identifying a page of results to be returned. This is the value
     *   of `nextPageToken` returned in a previous reference image list request.
     *
     *   Defaults to the first page if not specified.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.vision.v1p3beta1.ReferenceImage|ReferenceImage} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listReferenceImagesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listReferenceImagesStream(request?: protos.google.cloud.vision.v1p3beta1.IListReferenceImagesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listReferenceImages`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Resource name of the product containing the reference images.
     *
     *   Format is
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   A token identifying a page of results to be returned. This is the value
     *   of `nextPageToken` returned in a previous reference image list request.
     *
     *   Defaults to the first page if not specified.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.vision.v1p3beta1.ReferenceImage|ReferenceImage}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.list_reference_images.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_ListReferenceImages_async
     */
    listReferenceImagesAsync(request?: protos.google.cloud.vision.v1p3beta1.IListReferenceImagesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.vision.v1p3beta1.IReferenceImage>;
    /**
     * Lists the Products in a ProductSet, in an unspecified order. If the
     * ProductSet does not exist, the products field of the response will be
     * empty.
     *
     * Possible errors:
     *
     * * Returns INVALID_ARGUMENT if page_size is greater than 100 or less than 1.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The ProductSet resource for which to retrieve Products.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is Array of {@link protos.google.cloud.vision.v1p3beta1.Product|Product}.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed and will merge results from all the pages into this array.
     *   Note that it can affect your quota.
     *   We recommend using `listProductsInProductSetAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listProductsInProductSet(request?: protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetRequest, options?: CallOptions): Promise<[
        protos.google.cloud.vision.v1p3beta1.IProduct[],
        protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetRequest | null,
        protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetResponse
    ]>;
    listProductsInProductSet(request: protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetRequest, protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetResponse | null | undefined, protos.google.cloud.vision.v1p3beta1.IProduct>): void;
    listProductsInProductSet(request: protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetRequest, callback: PaginationCallback<protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetRequest, protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetResponse | null | undefined, protos.google.cloud.vision.v1p3beta1.IProduct>): void;
    /**
     * Equivalent to `listProductsInProductSet`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The ProductSet resource for which to retrieve Products.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.vision.v1p3beta1.Product|Product} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listProductsInProductSetAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listProductsInProductSetStream(request?: protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listProductsInProductSet`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The ProductSet resource for which to retrieve Products.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.vision.v1p3beta1.Product|Product}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p3beta1/product_search.list_products_in_product_set.js</caption>
     * region_tag:vision_v1p3beta1_generated_ProductSearch_ListProductsInProductSet_async
     */
    listProductsInProductSetAsync(request?: protos.google.cloud.vision.v1p3beta1.IListProductsInProductSetRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.vision.v1p3beta1.IProduct>;
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project: string, location: string): string;
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName: string): string | number;
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName: string): string | number;
    /**
     * Return a fully-qualified product resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} product
     * @returns {string} Resource name string.
     */
    productPath(project: string, location: string, product: string): string;
    /**
     * Parse the project from Product resource.
     *
     * @param {string} productName
     *   A fully-qualified path representing Product resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProductName(productName: string): string | number;
    /**
     * Parse the location from Product resource.
     *
     * @param {string} productName
     *   A fully-qualified path representing Product resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProductName(productName: string): string | number;
    /**
     * Parse the product from Product resource.
     *
     * @param {string} productName
     *   A fully-qualified path representing Product resource.
     * @returns {string} A string representing the product.
     */
    matchProductFromProductName(productName: string): string | number;
    /**
     * Return a fully-qualified productSet resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} product_set
     * @returns {string} Resource name string.
     */
    productSetPath(project: string, location: string, productSet: string): string;
    /**
     * Parse the project from ProductSet resource.
     *
     * @param {string} productSetName
     *   A fully-qualified path representing ProductSet resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProductSetName(productSetName: string): string | number;
    /**
     * Parse the location from ProductSet resource.
     *
     * @param {string} productSetName
     *   A fully-qualified path representing ProductSet resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProductSetName(productSetName: string): string | number;
    /**
     * Parse the product_set from ProductSet resource.
     *
     * @param {string} productSetName
     *   A fully-qualified path representing ProductSet resource.
     * @returns {string} A string representing the product_set.
     */
    matchProductSetFromProductSetName(productSetName: string): string | number;
    /**
     * Return a fully-qualified referenceImage resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} product
     * @param {string} reference_image
     * @returns {string} Resource name string.
     */
    referenceImagePath(project: string, location: string, product: string, referenceImage: string): string;
    /**
     * Parse the project from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromReferenceImageName(referenceImageName: string): string | number;
    /**
     * Parse the location from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromReferenceImageName(referenceImageName: string): string | number;
    /**
     * Parse the product from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the product.
     */
    matchProductFromReferenceImageName(referenceImageName: string): string | number;
    /**
     * Parse the reference_image from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the reference_image.
     */
    matchReferenceImageFromReferenceImageName(referenceImageName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
