import React from 'react'
import { cn, type NeoColor, neoColors } from '@/lib/utils'

interface CardProps {
  children: React.ReactNode
  className?: string
  color?: NeoColor
  variant?: 'default' | 'elevated' | 'flat'
}

export function Card({ children, className, color, variant = 'default' }: CardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'elevated':
        return 'neo-shadow-xl'
      case 'flat':
        return 'shadow-none'
      default:
        return 'neo-shadow-lg'
    }
  }

  const colorStyle = color ? { backgroundColor: neoColors[color] } : {}

  return (
    <div
      className={cn(
        'neo-card',
        getVariantStyles(),
        className
      )}
      style={colorStyle}
    >
      {children}
    </div>
  )
}

interface CardHeaderProps {
  children: React.ReactNode
  className?: string
}

export function CardHeader({ children, className }: CardHeaderProps) {
  return (
    <div className={cn('mb-4', className)}>
      {children}
    </div>
  )
}

interface CardTitleProps {
  children: React.ReactNode
  className?: string
}

export function CardTitle({ children, className }: CardTitleProps) {
  return (
    <h3 className={cn('text-2xl font-bold uppercase tracking-wide', className)}>
      {children}
    </h3>
  )
}

interface CardContentProps {
  children: React.ReactNode
  className?: string
}

export function CardContent({ children, className }: CardContentProps) {
  return (
    <div className={cn('', className)}>
      {children}
    </div>
  )
}
