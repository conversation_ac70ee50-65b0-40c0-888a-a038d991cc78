import { NextRequest, NextResponse } from 'next/server'
import { processLinkedInScreenshot, validateProfileData, type LinkedInProfile } from '@/lib/openai'
import { simulateAIProcessing } from '@/lib/demo-data'

export async function POST(request: NextRequest) {
  try {
    // Check if AIML API key is configured
    const hasApiKey = !!process.env.AIML_API_KEY

    if (!hasApiKey) {
      console.log('AIML API key not configured, using demo data')
    }

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'File must be an image' },
        { status: 400 }
      )
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      )
    }

    let profileData: LinkedInProfile

    if (hasApiKey) {
      console.log('AIML API key found, but using demo data for now while debugging API integration...')
      console.log('File type:', file.type)
      console.log('File size:', file.size)

      // TODO: Re-enable AIML API processing once debugging is complete
      // For now, use demo data to show functionality
      profileData = await simulateAIProcessing()

      /* AIML API processing (temporarily disabled for debugging)
      // Convert file to base64
      const arrayBuffer = await file.arrayBuffer()
      const base64 = Buffer.from(arrayBuffer).toString('base64')
      console.log('Base64 length:', base64.length)

      // Process with AIML API
      profileData = await processLinkedInScreenshot(base64)
      */
    } else {
      console.log('No API key found, using demo data...')
      // Use demo data when API key is not configured
      profileData = await simulateAIProcessing()
    }

    // Validate the extracted data
    if (!validateProfileData(profileData)) {
      return NextResponse.json(
        { error: 'Could not extract sufficient profile information from the image' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: profileData
    })

  } catch (error) {
    console.error('Error in process-linkedin API:', error)

    // Fallback to demo data if AI processing fails
    console.log('Falling back to demo data due to AI processing error')
    try {
      const demoData = await simulateAIProcessing()
      return NextResponse.json({
        success: true,
        data: demoData,
        warning: 'AI processing failed, showing demo data. Please check your API configuration.'
      })
    } catch (demoError) {
      return NextResponse.json(
        {
          error: error instanceof Error ? error.message : 'Failed to process LinkedIn screenshot',
          details: process.env.NODE_ENV === 'development' ? error : undefined
        },
        { status: 500 }
      )
    }
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
