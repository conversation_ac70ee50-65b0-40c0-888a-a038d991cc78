import { NextRequest, NextResponse } from 'next/server'
import { processLinkedInScreenshot, validateProfileData, type LinkedInProfile } from '@/lib/openai'
import { extractTextWithGoogleVision, parseLinkedInText } from '@/lib/google-vision'
import { simulateAIProcessing } from '@/lib/demo-data'

export async function POST(request: NextRequest) {
  try {
    // Check if Google Vision API key is configured
    const hasGoogleVisionKey = !!process.env.GOOGLE_VISION_API_KEY
    const hasAIMLKey = !!process.env.AIML_API_KEY

    console.log('API Keys available:', {
      googleVision: hasGoogleVisionKey,
      aiml: hasAIMLKey
    })

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'File must be an image' },
        { status: 400 }
      )
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      )
    }

    let profileData: LinkedInProfile

    // Convert file to base64
    const arrayBuffer = await file.arrayBuffer()
    const base64 = Buffer.from(arrayBuffer).toString('base64')
    console.log('File type:', file.type)
    console.log('File size:', file.size)
    console.log('Base64 length:', base64.length)

    if (hasGoogleVisionKey) {
      console.log('Using Google Vision API for text extraction...')

      try {
        // Extract text using Google Vision API
        const extractedText = await extractTextWithGoogleVision(base64)
        console.log('Google Vision text extraction successful!')

        // Parse the extracted text into LinkedIn profile structure
        profileData = parseLinkedInText(extractedText)
        console.log('LinkedIn text parsing completed!')

      } catch (googleError) {
        console.error('Google Vision API failed:', googleError)

        // Fallback to AIML API if available
        if (hasAIMLKey) {
          console.log('Falling back to AIML API...')
          try {
            profileData = await processLinkedInScreenshot(base64)
            console.log('AIML API fallback successful!')
          } catch (aimlError) {
            console.error('AIML API also failed, using demo data:', aimlError)
            profileData = await simulateAIProcessing()
          }
        } else {
          console.log('No AIML fallback available, using demo data')
          profileData = await simulateAIProcessing()
        }
      }
    } else if (hasAIMLKey) {
      console.log('Using AIML API (Google Vision not available)...')

      try {
        profileData = await processLinkedInScreenshot(base64)
        console.log('AIML API processing successful!')
      } catch (aimlError) {
        console.error('AIML API failed, using demo data:', aimlError)
        profileData = await simulateAIProcessing()
      }
    } else {
      console.log('No API keys configured, using demo data...')
      profileData = await simulateAIProcessing()
    }

    // Validate the extracted data
    if (!validateProfileData(profileData)) {
      return NextResponse.json(
        { error: 'Could not extract sufficient profile information from the image' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: profileData
    })

  } catch (error) {
    console.error('Error in process-linkedin API:', error)

    // Fallback to demo data if AI processing fails
    console.log('Falling back to demo data due to AI processing error')
    try {
      const demoData = await simulateAIProcessing()
      return NextResponse.json({
        success: true,
        data: demoData,
        warning: 'AI processing failed, showing demo data. Please check your API configuration.'
      })
    } catch (demoError) {
      return NextResponse.json(
        {
          error: error instanceof Error ? error.message : 'Failed to process LinkedIn screenshot',
          details: process.env.NODE_ENV === 'development' ? error : undefined
        },
        { status: 500 }
      )
    }
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
