import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

export async function GET() {
  try {
    if (!process.env.AIML_API_KEY) {
      return NextResponse.json({ error: 'No API key configured' })
    }

    const aimlClient = new OpenAI({
      apiKey: process.env.AIML_API_KEY,
      baseURL: 'https://api.aimlapi.com',
    })

    console.log('Testing AIML API key validity...')

    // Test with a simple text request
    const response = await aimlClient.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: "Hello, please respond with 'API key is working' if you can see this message."
        }
      ],
      max_tokens: 20,
      temperature: 0.1,
    })

    const content = response.choices[0]?.message?.content

    return NextResponse.json({
      success: true,
      message: 'API key is valid',
      response: content,
      usage: response.usage
    })

  } catch (error) {
    console.error('API key verification failed:', error)
    
    if (error && typeof error === 'object' && 'status' in error) {
      return NextResponse.json({
        error: 'API key verification failed',
        status: (error as any).status,
        message: (error as any).message || 'Unknown API error'
      }, { status: 500 })
    }
    
    return NextResponse.json({
      error: 'API key verification failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
