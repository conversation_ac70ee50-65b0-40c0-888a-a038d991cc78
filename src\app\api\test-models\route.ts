import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

export async function GET() {
  try {
    if (!process.env.AIML_API_KEY) {
      return NextResponse.json({ error: 'No API key configured' })
    }

    const aimlClient = new OpenAI({
      apiKey: process.env.AIML_API_KEY,
      baseURL: 'https://api.aimlapi.com',
    })

    console.log('Testing AIML API models...')

    // Test different models to see which ones work
    const modelsToTest = [
      'gpt-4o',
      'gpt-4o-mini',
      'gpt-4-turbo',
      'gpt-4-vision-preview',
      'gpt-4o-2024-08-06',
      'gpt-3.5-turbo'
    ]

    const results = []

    for (const model of modelsToTest) {
      try {
        console.log(`Testing model: ${model}`)
        
        const response = await aimlClient.chat.completions.create({
          model: model,
          messages: [
            {
              role: "user",
              content: "Hello! Please respond with just the word 'SUCCESS' to test this model."
            }
          ],
          max_tokens: 10,
          temperature: 0.1,
        })

        const content = response.choices[0]?.message?.content
        console.log(`${model} response:`, content)
        
        results.push({
          model: model,
          status: 'success',
          response: content,
          supportsVision: model.includes('vision') || model.includes('4o')
        })

      } catch (error) {
        console.log(`${model} failed:`, error instanceof Error ? error.message : 'Unknown error')
        results.push({
          model: model,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      success: true,
      results: results,
      workingModels: results.filter(r => r.status === 'success'),
      visionModels: results.filter(r => r.status === 'success' && r.supportsVision)
    })

  } catch (error) {
    console.error('Model test failed:', error)
    return NextResponse.json({
      error: 'Model test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
