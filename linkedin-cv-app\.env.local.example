# Google Vision API Configuration (Primary)
GOOGLE_VISION_API_KEY=your_google_vision_api_key_here

# AIML API Configuration (Fallback)
AIML_API_KEY=your_aiml_api_key_here

# Instructions:
# 1. Copy this file to .env.local
# 2. Get Google Vision API key from: https://console.cloud.google.com/apis/credentials
# 3. Enable Vision API in Google Cloud Console
# 4. Replace 'your_google_vision_api_key_here' with your actual Google Vision API key
# 5. (Optional) Keep AIML API key as fallback
# 6. Make sure .env.local is in your .gitignore file (it should be by default)

# Note: Google Vision API is excellent for OCR and text extraction from images
# It's specifically designed for reading text from screenshots and documents
