# AIML API Setup Guide

This LinkedIn CV Builder uses the AIML API (https://api.aimlapi.com) for AI-powered LinkedIn profile extraction.

## Why AIML API?

- **Cost-effective**: More affordable than OpenAI
- **Powerful models**: Access to GPT-4o and other advanced vision models
- **Easy integration**: Compatible with OpenAI SDK
- **Reliable**: High-quality text and image processing

## Setup Instructions

### 1. Get Your AIML API Key

1. Visit [https://aimlapi.com](https://aimlapi.com)
2. Sign up for an account
3. Navigate to your dashboard
4. Generate an API key

### 2. Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.local.example .env.local
   ```

2. Edit `.env.local` and add your API key:
   ```
   AIML_API_KEY=your_actual_aiml_api_key_here
   ```

### 3. Test the Integration

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Test the API connection:
   ```
   http://localhost:3000/api/test-aiml
   ```

3. If successful, you should see:
   ```json
   {
     "status": "success",
     "message": "AIML API connection successful",
     "model": "gpt-4o",
     "baseURL": "https://api.aimlapi.com"
   }
   ```

## Features

- **GPT-4o Vision**: Advanced image analysis for LinkedIn screenshots
- **Structured Extraction**: Comprehensive profile data extraction
- **High Accuracy**: Optimized prompts for professional CV generation
- **Error Handling**: Robust error handling and fallback mechanisms
- **Demo Mode**: Works without API key using sample data

## Usage

1. Upload a LinkedIn profile screenshot
2. AIML API processes the image using GPT-4o
3. Extracted data is formatted for CV generation
4. Download your professional CV as PDF

## Troubleshooting

### API Key Issues
- Ensure your API key is correctly set in `.env.local`
- Check that you have sufficient credits in your AIML account
- Verify the API key has the necessary permissions

### Image Processing Issues
- Use high-quality, clear screenshots
- Ensure text is readable and not cropped
- LinkedIn profile should be in light mode for better OCR

### Demo Mode
If no API key is configured, the app will use demo data to show functionality.

## Support

For AIML API support: [https://aimlapi.com/support](https://aimlapi.com/support)
For app issues: Check the console logs and error messages
