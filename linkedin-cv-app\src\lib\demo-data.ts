import { LinkedInProfile } from './openai'

// Demo profile data for when OpenAI API is not configured
export const demoLinkedInProfile: LinkedInProfile = {
  personalInfo: {
    name: "<PERSON>",
    title: "Senior Software Engineer",
    location: "San Francisco, CA",
    email: "<EMAIL>",
    linkedin: "linkedin.com/in/alex<PERSON><PERSON><PERSON>"
  },
  summary: "Experienced software engineer with 5+ years of expertise in full-stack development, cloud architecture, and team leadership. Passionate about building scalable applications and mentoring junior developers. Proven track record of delivering high-quality software solutions in fast-paced startup environments.",
  experience: [
    {
      company: "TechCorp Inc.",
      position: "Senior Software Engineer",
      duration: "Jan 2022 - Present",
      description: "Lead development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 60%. Mentored 3 junior developers and conducted technical interviews.",
      location: "San Francisco, CA"
    },
    {
      company: "StartupXYZ",
      position: "Full Stack Developer",
      duration: "Mar 2020 - Dec 2021",
      description: "Built responsive web applications using React and Node.js. Designed and implemented RESTful APIs. Collaborated with product team to define technical requirements and user stories.",
      location: "Remote"
    },
    {
      company: "Digital Solutions Ltd",
      position: "Junior Developer",
      duration: "Jun 2019 - Feb 2020",
      description: "Developed and maintained client websites using HTML, CSS, JavaScript, and PHP. Participated in code reviews and agile development processes. Fixed bugs and implemented new features.",
      location: "New York, NY"
    }
  ],
  education: [
    {
      institution: "University of California, Berkeley",
      degree: "Bachelor of Science",
      field: "Computer Science",
      duration: "2015 - 2019",
      location: "Berkeley, CA"
    }
  ],
  skills: [
    "JavaScript", "TypeScript", "React", "Node.js", "Python", "AWS", 
    "Docker", "Kubernetes", "PostgreSQL", "MongoDB", "Git", "Agile/Scrum",
    "System Design", "API Development", "Microservices", "CI/CD"
  ],
  certifications: [
    {
      name: "AWS Certified Solutions Architect",
      issuer: "Amazon Web Services",
      date: "2023"
    },
    {
      name: "Certified Kubernetes Administrator",
      issuer: "Cloud Native Computing Foundation",
      date: "2022"
    }
  ],
  languages: [
    {
      language: "English",
      proficiency: "Native"
    },
    {
      language: "Spanish",
      proficiency: "Conversational"
    }
  ]
}

// Function to simulate AI processing delay
export function simulateAIProcessing(): Promise<LinkedInProfile> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(demoLinkedInProfile)
    }, 2000) // 2 second delay to simulate processing
  })
}
