{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/node_modules/next/src/pages/_app.tsx"], "sourcesContent": ["import React from 'react'\n\nimport type {\n  AppContextType,\n  AppInitialProps,\n  AppPropsType,\n  NextWebVitalsMetric,\n  AppType,\n} from '../shared/lib/utils'\nimport type { Router } from '../client/router'\n\nimport { loadGetInitialProps } from '../shared/lib/utils'\n\nexport type { AppInitialProps, AppType }\n\nexport type { NextWebVitalsMetric }\n\nexport type AppContext = AppContextType<Router>\n\nexport type AppProps<P = any> = AppPropsType<Router, P>\n\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */\nasync function appGetInitialProps({\n  Component,\n  ctx,\n}: AppContext): Promise<AppInitialProps> {\n  const pageProps = await loadGetInitialProps(Component, ctx)\n  return { pageProps }\n}\n\nexport default class App<P = any, CP = {}, S = {}> extends React.Component<\n  P & AppProps<CP>,\n  S\n> {\n  static origGetInitialProps = appGetInitialProps\n  static getInitialProps = appGetInitialProps\n\n  render() {\n    const { Component, pageProps } = this.props as AppProps<CP>\n\n    return <Component {...pageProps} />\n  }\n}\n"], "names": ["App", "appGetInitialProps", "Component", "ctx", "pageProps", "loadGetInitialProps", "React", "render", "props", "origGetInitialProps", "getInitialProps"], "mappings": ";;;;;;;eAiCqBA;;;;;gEAjCH;uBAWkB;AAUpC;;;CAGC,GACD,eAAeC,mBAAmB,KAGrB;IAHqB,IAAA,EAChCC,SAAS,EACTC,GAAG,EACQ,GAHqB;IAIhC,MAAMC,YAAY,MAAMC,CAAAA,GAAAA,OAAAA,mBAAmB,EAACH,WAAWC;IACvD,OAAO;QAAEC;IAAU;AACrB;AAEe,MAAMJ,YAAsCM,OAAAA,OAAK,CAACJ,SAAS;IAOxEK,SAAS;QACP,MAAM,EAAEL,SAAS,EAAEE,SAAS,EAAE,GAAG,IAAI,CAACI,KAAK;QAE3C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACN,WAAAA;YAAW,GAAGE,SAAS;;IACjC;AACF;AAZqBJ,IAIZS,mBAAAA,GAAsBR;AAJVD,IAKZU,eAAAA,GAAkBT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/node_modules/next/app.js"], "sourcesContent": ["module.exports = require('./dist/pages/_app')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}