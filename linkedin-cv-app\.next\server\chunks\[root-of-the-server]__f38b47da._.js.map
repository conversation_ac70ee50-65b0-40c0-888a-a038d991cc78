{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai'\n\n// Initialize AIML API client (using OpenAI SDK with custom base URL)\nconst aimlClient = new OpenAI({\n  apiKey: process.env.AIML_API_KEY,\n  baseURL: 'https://api.aimlapi.com',\n})\n\n// AIML API provides access to GPT-4o and other advanced vision models\n\nexport interface LinkedInProfile {\n  personalInfo: {\n    name: string\n    title: string\n    location: string\n    email?: string\n    phone?: string\n    linkedin?: string\n  }\n  summary: string\n  experience: Array<{\n    company: string\n    position: string\n    duration: string\n    description: string\n    location?: string\n  }>\n  education: Array<{\n    institution: string\n    degree: string\n    field: string\n    duration: string\n    location?: string\n  }>\n  skills: string[]\n  certifications?: Array<{\n    name: string\n    issuer: string\n    date?: string\n  }>\n  languages?: Array<{\n    language: string\n    proficiency: string\n  }>\n}\n\nexport async function processLinkedInScreenshot(imageBase64: string): Promise<LinkedInProfile> {\n  const maxRetries = 2\n  let lastError: Error | null = null\n\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      console.log(`Starting AIML API request (attempt ${attempt}/${maxRetries})...`)\n\n      const response = await aimlClient.chat.completions.create({\n      model: \"gpt-4o\",\n      messages: [\n        {\n          role: \"user\",\n          content: [\n            {\n              type: \"text\",\n              text: `You are an expert LinkedIn profile analyzer. Carefully examine this LinkedIn profile screenshot and extract ALL visible professional information with maximum accuracy and detail.\n\nEXTRACTION REQUIREMENTS:\n1. Read ALL text visible in the image carefully\n2. Extract information exactly as written (preserve original wording)\n3. Include ALL work experiences, education entries, and skills shown\n4. Capture complete job descriptions and summaries\n5. Note any certifications, languages, or additional sections visible\n\nRETURN FORMAT: JSON object only, no explanations or formatting.\n\n{\n  \"personalInfo\": {\n    \"name\": \"Extract full name exactly as shown\",\n    \"title\": \"Current headline/job title from top of profile\",\n    \"location\": \"Geographic location shown\",\n    \"email\": \"Email if visible in contact info\",\n    \"phone\": \"Phone if visible in contact info\",\n    \"linkedin\": \"LinkedIn URL if visible\"\n  },\n  \"summary\": \"Complete About/Summary section text - extract the full paragraph(s) exactly as written\",\n  \"experience\": [\n    {\n      \"company\": \"Company name exactly as shown\",\n      \"position\": \"Job title exactly as shown\",\n      \"duration\": \"Employment dates (e.g., 'Jan 2020 - Present' or 'Jun 2018 - Dec 2019')\",\n      \"description\": \"Complete job description - extract all bullet points and details exactly as written\",\n      \"location\": \"Job location if shown\"\n    }\n  ],\n  \"education\": [\n    {\n      \"institution\": \"School/University name exactly as shown\",\n      \"degree\": \"Degree type (e.g., 'Bachelor of Science', 'Master of Arts')\",\n      \"field\": \"Field of study exactly as shown\",\n      \"duration\": \"Years attended or graduation year\",\n      \"location\": \"School location if shown\"\n    }\n  ],\n  \"skills\": [\"Extract ALL skills shown - include every skill listed in the skills section\"],\n  \"certifications\": [\n    {\n      \"name\": \"Certification name exactly as shown\",\n      \"issuer\": \"Issuing organization\",\n      \"date\": \"Date obtained if shown\"\n    }\n  ],\n  \"languages\": [\n    {\n      \"language\": \"Language name\",\n      \"proficiency\": \"Proficiency level if shown (e.g., 'Native', 'Professional', 'Conversational')\"\n    }\n  ]\n}\n\nCRITICAL: Extract information exactly as it appears. Be thorough and include ALL visible details.`\n            },\n            {\n              type: \"image_url\",\n              image_url: {\n                url: `data:image/jpeg;base64,${imageBase64}`,\n                detail: \"high\"\n              }\n            }\n          ]\n        }\n      ],\n      max_tokens: 6000,\n      temperature: 0.05, // Very low temperature for maximum accuracy\n      top_p: 0.95, // Focus on most likely tokens for accuracy\n    })\n\n    console.log('AIML API response received')\n    console.log('Response object:', JSON.stringify(response, null, 2))\n\n    const content = response.choices[0]?.message?.content\n    console.log('Response content length:', content?.length || 0)\n    console.log('Response content preview:', content?.substring(0, 200))\n    if (!content) {\n      throw new Error('No response from AIML API')\n    }\n\n    console.log('Raw AI response:', content.substring(0, 500) + '...')\n\n    // Try to parse the JSON response\n    try {\n      const rawProfileData = JSON.parse(content) as LinkedInProfile\n      console.log('Successfully parsed JSON response')\n\n      // Clean and enhance the extracted data\n      const cleanedProfileData = cleanProfileData(rawProfileData)\n      console.log('Data cleaning completed')\n\n      return cleanedProfileData\n    } catch (parseError) {\n      console.log('Initial JSON parse failed, trying to extract JSON...')\n\n      // If JSON parsing fails, try to extract JSON from the response\n      // Look for JSON between ```json and ``` or just between { and }\n      let jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/)\n      if (!jsonMatch) {\n        jsonMatch = content.match(/```\\s*([\\s\\S]*?)\\s*```/)\n      }\n      if (!jsonMatch) {\n        jsonMatch = content.match(/(\\{[\\s\\S]*\\})/)\n      }\n\n      if (jsonMatch) {\n        try {\n          const cleanedJson = jsonMatch[1] || jsonMatch[0]\n          console.log('Extracted JSON:', cleanedJson.substring(0, 200) + '...')\n          const rawProfileData = JSON.parse(cleanedJson) as LinkedInProfile\n          console.log('Successfully parsed extracted JSON')\n\n          // Clean and enhance the extracted data\n          const cleanedProfileData = cleanProfileData(rawProfileData)\n          console.log('Data cleaning completed')\n\n          return cleanedProfileData\n        } catch (secondParseError) {\n          console.error('Failed to parse extracted JSON:', secondParseError)\n        }\n      }\n\n      console.error('Raw response that failed to parse:', content)\n      throw new Error('Failed to parse AI response as JSON')\n    }\n\n    } catch (error) {\n      console.error(`Attempt ${attempt} failed:`, error)\n      lastError = error instanceof Error ? error : new Error('Unknown error')\n\n      if (attempt < maxRetries) {\n        console.log(`Retrying in 2 seconds...`)\n        await new Promise(resolve => setTimeout(resolve, 2000))\n        continue\n      }\n    }\n  }\n\n  // If all attempts failed, throw the last error\n  if (lastError) {\n    console.error('All attempts failed. Error processing LinkedIn screenshot with AIML API:', lastError)\n\n    // Provide more detailed error information\n    if (lastError instanceof Error) {\n      console.error('Error message:', lastError.message)\n      console.error('Error stack:', lastError.stack)\n    }\n\n    // Check if it's an API-related error\n    if (lastError && typeof lastError === 'object' && 'response' in lastError) {\n      console.error('API response error:', lastError)\n    }\n\n    throw new Error(`Failed to process LinkedIn screenshot with AI after ${maxRetries} attempts: ${lastError instanceof Error ? lastError.message : 'Unknown error'}`)\n  }\n\n  // This should never be reached, but TypeScript requires it\n  throw new Error('Unexpected error in processLinkedInScreenshot')\n}\n\n// Helper function to convert file to base64\nexport function fileToBase64(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader()\n    reader.readAsDataURL(file)\n    reader.onload = () => {\n      const result = reader.result as string\n      // Remove the data URL prefix to get just the base64 string\n      const base64 = result.split(',')[1]\n      resolve(base64)\n    }\n    reader.onerror = error => reject(error)\n  })\n}\n\n// Validate extracted profile data\nexport function validateProfileData(profile: LinkedInProfile): boolean {\n  return !!(\n    profile.personalInfo?.name &&\n    profile.personalInfo?.title &&\n    (profile.experience?.length > 0 || profile.education?.length > 0)\n  )\n}\n\n// Clean and enhance extracted profile data\nexport function cleanProfileData(profile: LinkedInProfile): LinkedInProfile {\n  return {\n    personalInfo: {\n      name: cleanText(profile.personalInfo?.name || ''),\n      title: cleanText(profile.personalInfo?.title || ''),\n      location: cleanText(profile.personalInfo?.location || ''),\n      email: cleanEmail(profile.personalInfo?.email || ''),\n      phone: cleanPhone(profile.personalInfo?.phone || ''),\n      linkedin: cleanUrl(profile.personalInfo?.linkedin || '')\n    },\n    summary: cleanText(profile.summary || ''),\n    experience: (profile.experience || []).map(exp => ({\n      company: cleanText(exp.company || ''),\n      position: cleanText(exp.position || ''),\n      duration: cleanDuration(exp.duration || ''),\n      description: cleanText(exp.description || ''),\n      location: cleanText(exp.location || '')\n    })),\n    education: (profile.education || []).map(edu => ({\n      institution: cleanText(edu.institution || ''),\n      degree: cleanText(edu.degree || ''),\n      field: cleanText(edu.field || ''),\n      duration: cleanDuration(edu.duration || ''),\n      location: cleanText(edu.location || '')\n    })),\n    skills: (profile.skills || []).map(skill => cleanText(skill)).filter(skill => skill.length > 0),\n    certifications: (profile.certifications || []).map(cert => ({\n      name: cleanText(cert.name || ''),\n      issuer: cleanText(cert.issuer || ''),\n      date: cleanText(cert.date || '')\n    })),\n    languages: (profile.languages || []).map(lang => ({\n      language: cleanText(lang.language || ''),\n      proficiency: cleanText(lang.proficiency || '')\n    }))\n  }\n}\n\n// Helper functions for data cleaning\nfunction cleanText(text: string): string {\n  return text.trim().replace(/\\s+/g, ' ').replace(/[\"\"]/g, '\"').replace(/['']/g, \"'\")\n}\n\nfunction cleanEmail(email: string): string {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email.trim()) ? email.trim().toLowerCase() : ''\n}\n\nfunction cleanPhone(phone: string): string {\n  const cleaned = phone.replace(/[^\\d+\\-\\(\\)\\s]/g, '').trim()\n  return cleaned.length >= 10 ? cleaned : ''\n}\n\nfunction cleanUrl(url: string): string {\n  if (!url) return ''\n  if (url.startsWith('http')) return url\n  if (url.includes('linkedin.com')) return `https://${url}`\n  return url\n}\n\nfunction cleanDuration(duration: string): string {\n  return duration.trim().replace(/\\s+/g, ' ')\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;;AAEA,qEAAqE;AACrE,MAAM,aAAa,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC5B,QAAQ,QAAQ,GAAG,CAAC,YAAY;IAChC,SAAS;AACX;AAwCO,eAAe,0BAA0B,WAAmB;IACjE,MAAM,aAAa;IACnB,IAAI,YAA0B;IAE9B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,CAAC,EAAE,WAAW,IAAI,CAAC;YAE7E,MAAM,WAAW,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC1D,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iGAuD4E,CAAC;4BACtF;4BACA;gCACE,MAAM;gCACN,WAAW;oCACT,KAAK,CAAC,uBAAuB,EAAE,aAAa;oCAC5C,QAAQ;gCACV;4BACF;yBACD;oBACH;iBACD;gBACD,YAAY;gBACZ,aAAa;gBACb,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,oBAAoB,KAAK,SAAS,CAAC,UAAU,MAAM;YAE/D,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,QAAQ,GAAG,CAAC,4BAA4B,SAAS,UAAU;YAC3D,QAAQ,GAAG,CAAC,6BAA6B,SAAS,UAAU,GAAG;YAC/D,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,SAAS,CAAC,GAAG,OAAO;YAE5D,iCAAiC;YACjC,IAAI;gBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAClC,QAAQ,GAAG,CAAC;gBAEZ,uCAAuC;gBACvC,MAAM,qBAAqB,iBAAiB;gBAC5C,QAAQ,GAAG,CAAC;gBAEZ,OAAO;YACT,EAAE,OAAO,YAAY;gBACnB,QAAQ,GAAG,CAAC;gBAEZ,+DAA+D;gBAC/D,gEAAgE;gBAChE,IAAI,YAAY,QAAQ,KAAK,CAAC;gBAC9B,IAAI,CAAC,WAAW;oBACd,YAAY,QAAQ,KAAK,CAAC;gBAC5B;gBACA,IAAI,CAAC,WAAW;oBACd,YAAY,QAAQ,KAAK,CAAC;gBAC5B;gBAEA,IAAI,WAAW;oBACb,IAAI;wBACF,MAAM,cAAc,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;wBAChD,QAAQ,GAAG,CAAC,mBAAmB,YAAY,SAAS,CAAC,GAAG,OAAO;wBAC/D,MAAM,iBAAiB,KAAK,KAAK,CAAC;wBAClC,QAAQ,GAAG,CAAC;wBAEZ,uCAAuC;wBACvC,MAAM,qBAAqB,iBAAiB;wBAC5C,QAAQ,GAAG,CAAC;wBAEZ,OAAO;oBACT,EAAE,OAAO,kBAAkB;wBACzB,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF;gBAEA,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,MAAM,IAAI,MAAM;YAClB;QAEA,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAAE;YAC5C,YAAY,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;YAEvD,IAAI,UAAU,YAAY;gBACxB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,CAAC;gBACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD;YACF;QACF;IACF;IAEA,+CAA+C;IAC/C,IAAI,WAAW;QACb,QAAQ,KAAK,CAAC,4EAA4E;QAE1F,0CAA0C;QAC1C,IAAI,qBAAqB,OAAO;YAC9B,QAAQ,KAAK,CAAC,kBAAkB,UAAU,OAAO;YACjD,QAAQ,KAAK,CAAC,gBAAgB,UAAU,KAAK;QAC/C;QAEA,qCAAqC;QACrC,IAAI,aAAa,OAAO,cAAc,YAAY,cAAc,WAAW;YACzE,QAAQ,KAAK,CAAC,uBAAuB;QACvC;QAEA,MAAM,IAAI,MAAM,CAAC,oDAAoD,EAAE,WAAW,WAAW,EAAE,qBAAqB,QAAQ,UAAU,OAAO,GAAG,iBAAiB;IACnK;IAEA,2DAA2D;IAC3D,MAAM,IAAI,MAAM;AAClB;AAGO,SAAS,aAAa,IAAU;IACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,aAAa,CAAC;QACrB,OAAO,MAAM,GAAG;YACd,MAAM,SAAS,OAAO,MAAM;YAC5B,2DAA2D;YAC3D,MAAM,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;YACnC,QAAQ;QACV;QACA,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;IACnC;AACF;AAGO,SAAS,oBAAoB,OAAwB;IAC1D,OAAO,CAAC,CAAC,CACP,QAAQ,YAAY,EAAE,QACtB,QAAQ,YAAY,EAAE,SACtB,CAAC,QAAQ,UAAU,EAAE,SAAS,KAAK,QAAQ,SAAS,EAAE,SAAS,CAAC,CAClE;AACF;AAGO,SAAS,iBAAiB,OAAwB;IACvD,OAAO;QACL,cAAc;YACZ,MAAM,UAAU,QAAQ,YAAY,EAAE,QAAQ;YAC9C,OAAO,UAAU,QAAQ,YAAY,EAAE,SAAS;YAChD,UAAU,UAAU,QAAQ,YAAY,EAAE,YAAY;YACtD,OAAO,WAAW,QAAQ,YAAY,EAAE,SAAS;YACjD,OAAO,WAAW,QAAQ,YAAY,EAAE,SAAS;YACjD,UAAU,SAAS,QAAQ,YAAY,EAAE,YAAY;QACvD;QACA,SAAS,UAAU,QAAQ,OAAO,IAAI;QACtC,YAAY,CAAC,QAAQ,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;gBACjD,SAAS,UAAU,IAAI,OAAO,IAAI;gBAClC,UAAU,UAAU,IAAI,QAAQ,IAAI;gBACpC,UAAU,cAAc,IAAI,QAAQ,IAAI;gBACxC,aAAa,UAAU,IAAI,WAAW,IAAI;gBAC1C,UAAU,UAAU,IAAI,QAAQ,IAAI;YACtC,CAAC;QACD,WAAW,CAAC,QAAQ,SAAS,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC/C,aAAa,UAAU,IAAI,WAAW,IAAI;gBAC1C,QAAQ,UAAU,IAAI,MAAM,IAAI;gBAChC,OAAO,UAAU,IAAI,KAAK,IAAI;gBAC9B,UAAU,cAAc,IAAI,QAAQ,IAAI;gBACxC,UAAU,UAAU,IAAI,QAAQ,IAAI;YACtC,CAAC;QACD,QAAQ,CAAC,QAAQ,MAAM,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,QAAS,UAAU,QAAQ,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,GAAG;QAC7F,gBAAgB,CAAC,QAAQ,cAAc,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC1D,MAAM,UAAU,KAAK,IAAI,IAAI;gBAC7B,QAAQ,UAAU,KAAK,MAAM,IAAI;gBACjC,MAAM,UAAU,KAAK,IAAI,IAAI;YAC/B,CAAC;QACD,WAAW,CAAC,QAAQ,SAAS,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAChD,UAAU,UAAU,KAAK,QAAQ,IAAI;gBACrC,aAAa,UAAU,KAAK,WAAW,IAAI;YAC7C,CAAC;IACH;AACF;AAEA,qCAAqC;AACrC,SAAS,UAAU,IAAY;IAC7B,OAAO,KAAK,IAAI,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS;AACjF;AAEA,SAAS,WAAW,KAAa;IAC/B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM,IAAI,GAAG,WAAW,KAAK;AACtE;AAEA,SAAS,WAAW,KAAa;IAC/B,MAAM,UAAU,MAAM,OAAO,CAAC,mBAAmB,IAAI,IAAI;IACzD,OAAO,QAAQ,MAAM,IAAI,KAAK,UAAU;AAC1C;AAEA,SAAS,SAAS,GAAW;IAC3B,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI,IAAI,UAAU,CAAC,SAAS,OAAO;IACnC,IAAI,IAAI,QAAQ,CAAC,iBAAiB,OAAO,CAAC,QAAQ,EAAE,KAAK;IACzD,OAAO;AACT;AAEA,SAAS,cAAc,QAAgB;IACrC,OAAO,SAAS,IAAI,GAAG,OAAO,CAAC,QAAQ;AACzC", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/demo-data.ts"], "sourcesContent": ["import { LinkedInProfile } from './openai'\n\n// Demo profile data for when OpenAI API is not configured\nexport const demoLinkedInProfile: LinkedInProfile = {\n  personalInfo: {\n    name: \"<PERSON>\",\n    title: \"Senior Software Engineer\",\n    location: \"San Francisco, CA\",\n    email: \"<EMAIL>\",\n    linkedin: \"linkedin.com/in/alex<PERSON><PERSON><PERSON>\"\n  },\n  summary: \"Experienced software engineer with 5+ years of expertise in full-stack development, cloud architecture, and team leadership. Passionate about building scalable applications and mentoring junior developers. Proven track record of delivering high-quality software solutions in fast-paced startup environments.\",\n  experience: [\n    {\n      company: \"TechCorp Inc.\",\n      position: \"Senior Software Engineer\",\n      duration: \"Jan 2022 - Present\",\n      description: \"Lead development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 60%. Mentored 3 junior developers and conducted technical interviews.\",\n      location: \"San Francisco, CA\"\n    },\n    {\n      company: \"StartupXYZ\",\n      position: \"Full Stack Developer\",\n      duration: \"Mar 2020 - Dec 2021\",\n      description: \"Built responsive web applications using React and Node.js. Designed and implemented RESTful APIs. Collaborated with product team to define technical requirements and user stories.\",\n      location: \"Remote\"\n    },\n    {\n      company: \"Digital Solutions Ltd\",\n      position: \"Junior Developer\",\n      duration: \"Jun 2019 - Feb 2020\",\n      description: \"Developed and maintained client websites using HTML, CSS, JavaScript, and PHP. Participated in code reviews and agile development processes. Fixed bugs and implemented new features.\",\n      location: \"New York, NY\"\n    }\n  ],\n  education: [\n    {\n      institution: \"University of California, Berkeley\",\n      degree: \"Bachelor of Science\",\n      field: \"Computer Science\",\n      duration: \"2015 - 2019\",\n      location: \"Berkeley, CA\"\n    }\n  ],\n  skills: [\n    \"JavaScript\", \"TypeScript\", \"React\", \"Node.js\", \"Python\", \"AWS\", \n    \"Docker\", \"Kubernetes\", \"PostgreSQL\", \"MongoDB\", \"Git\", \"Agile/Scrum\",\n    \"System Design\", \"API Development\", \"Microservices\", \"CI/CD\"\n  ],\n  certifications: [\n    {\n      name: \"AWS Certified Solutions Architect\",\n      issuer: \"Amazon Web Services\",\n      date: \"2023\"\n    },\n    {\n      name: \"Certified Kubernetes Administrator\",\n      issuer: \"Cloud Native Computing Foundation\",\n      date: \"2022\"\n    }\n  ],\n  languages: [\n    {\n      language: \"English\",\n      proficiency: \"Native\"\n    },\n    {\n      language: \"Spanish\",\n      proficiency: \"Conversational\"\n    }\n  ]\n}\n\n// Function to simulate AI processing delay\nexport function simulateAIProcessing(): Promise<LinkedInProfile> {\n  return new Promise((resolve) => {\n    setTimeout(() => {\n      resolve(demoLinkedInProfile)\n    }, 2000) // 2 second delay to simulate processing\n  })\n}\n"], "names": [], "mappings": ";;;;AAGO,MAAM,sBAAuC;IAClD,cAAc;QACZ,MAAM;QACN,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA,SAAS;IACT,YAAY;QACV;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;QACA;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;QACA;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;KACD;IACD,WAAW;QACT;YACE,aAAa;YACb,QAAQ;YACR,OAAO;YACP,UAAU;YACV,UAAU;QACZ;KACD;IACD,QAAQ;QACN;QAAc;QAAc;QAAS;QAAW;QAAU;QAC1D;QAAU;QAAc;QAAc;QAAW;QAAO;QACxD;QAAiB;QAAmB;QAAiB;KACtD;IACD,gBAAgB;QACd;YACE,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IACD,WAAW;QACT;YACE,UAAU;YACV,aAAa;QACf;QACA;YACE,UAAU;YACV,aAAa;QACf;KACD;AACH;AAGO,SAAS;IACd,OAAO,IAAI,QAAQ,CAAC;QAClB,WAAW;YACT,QAAQ;QACV,GAAG,MAAM,wCAAwC;;IACnD;AACF", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/api/process-linkedin/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { processLinkedInScreenshot, validateProfileData, type LinkedInProfile } from '@/lib/openai'\nimport { simulateAIProcessing } from '@/lib/demo-data'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Check if AIML API key is configured\n    const hasApiKey = !!process.env.AIML_API_KEY\n\n    if (!hasApiKey) {\n      console.log('AIML API key not configured, using demo data')\n    }\n\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No file provided' },\n        { status: 400 }\n      )\n    }\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      return NextResponse.json(\n        { error: 'File must be an image' },\n        { status: 400 }\n      )\n    }\n\n    // Validate file size (10MB limit)\n    if (file.size > 10 * 1024 * 1024) {\n      return NextResponse.json(\n        { error: 'File size must be less than 10MB' },\n        { status: 400 }\n      )\n    }\n\n    let profileData: LinkedInProfile\n\n    if (hasApiKey) {\n      console.log('AIML API key found, testing vision API...')\n      console.log('File type:', file.type)\n      console.log('File size:', file.size)\n\n      try {\n        // Convert file to base64 with optimization\n        const arrayBuffer = await file.arrayBuffer()\n        let base64 = Buffer.from(arrayBuffer).toString('base64')\n        console.log('Original base64 length:', base64.length)\n\n        // If image is very large, we might want to resize it\n        // For now, we'll use the original but log the size\n        const imageSizeKB = Math.round(base64.length * 0.75 / 1024) // Approximate KB\n        console.log(`Image size: ~${imageSizeKB}KB`)\n\n        if (imageSizeKB > 5000) { // If larger than 5MB\n          console.log('Large image detected, processing with high detail mode')\n        }\n\n        // Process with AIML API\n        console.log('Processing LinkedIn screenshot with enhanced AI...')\n        profileData = await processLinkedInScreenshot(base64)\n        console.log('AIML API processing successful!')\n\n      } catch (apiError) {\n        console.error('AIML API failed, falling back to demo data:', apiError)\n        profileData = await simulateAIProcessing()\n      }\n    } else {\n      console.log('No API key found, using demo data...')\n      // Use demo data when API key is not configured\n      profileData = await simulateAIProcessing()\n    }\n\n    // Validate the extracted data\n    if (!validateProfileData(profileData)) {\n      return NextResponse.json(\n        { error: 'Could not extract sufficient profile information from the image' },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: profileData\n    })\n\n  } catch (error) {\n    console.error('Error in process-linkedin API:', error)\n\n    // Fallback to demo data if AI processing fails\n    console.log('Falling back to demo data due to AI processing error')\n    try {\n      const demoData = await simulateAIProcessing()\n      return NextResponse.json({\n        success: true,\n        data: demoData,\n        warning: 'AI processing failed, showing demo data. Please check your API configuration.'\n      })\n    } catch (demoError) {\n      return NextResponse.json(\n        {\n          error: error instanceof Error ? error.message : 'Failed to process LinkedIn screenshot',\n          details: process.env.NODE_ENV === 'development' ? error : undefined\n        },\n        { status: 500 }\n      )\n    }\n  }\n}\n\n// Handle preflight requests for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,sCAAsC;QACtC,MAAM,YAAY,CAAC,CAAC,QAAQ,GAAG,CAAC,YAAY;QAE5C,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwB,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;QAEJ,IAAI,WAAW;YACb,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;YACnC,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;YAEnC,IAAI;gBACF,2CAA2C;gBAC3C,MAAM,cAAc,MAAM,KAAK,WAAW;gBAC1C,IAAI,SAAS,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;gBAC/C,QAAQ,GAAG,CAAC,2BAA2B,OAAO,MAAM;gBAEpD,qDAAqD;gBACrD,mDAAmD;gBACnD,MAAM,cAAc,KAAK,KAAK,CAAC,OAAO,MAAM,GAAG,OAAO,MAAM,iBAAiB;;gBAC7E,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC;gBAE3C,IAAI,cAAc,MAAM;oBACtB,QAAQ,GAAG,CAAC;gBACd;gBAEA,wBAAwB;gBACxB,QAAQ,GAAG,CAAC;gBACZ,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EAAE;gBAC9C,QAAQ,GAAG,CAAC;YAEd,EAAE,OAAO,UAAU;gBACjB,QAAQ,KAAK,CAAC,+CAA+C;gBAC7D,cAAc,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;YACzC;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,+CAA+C;YAC/C,cAAc,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;QACzC;QAEA,8BAA8B;QAC9B,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkE,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,+CAA+C;QAC/C,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF,EAAE,OAAO,WAAW;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,SAAS,uCAAyC;YACpD,GACA;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAGO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}