{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/demo-data.ts"], "sourcesContent": ["import { LinkedInProfile } from './openai'\n\n// Demo profile data for when OpenAI API is not configured\nexport const demoLinkedInProfile: LinkedInProfile = {\n  personalInfo: {\n    name: \"<PERSON>\",\n    title: \"Senior Software Engineer\",\n    location: \"San Francisco, CA\",\n    email: \"<EMAIL>\",\n    linkedin: \"linkedin.com/in/alex<PERSON><PERSON><PERSON>\"\n  },\n  summary: \"Experienced software engineer with 5+ years of expertise in full-stack development, cloud architecture, and team leadership. Passionate about building scalable applications and mentoring junior developers. Proven track record of delivering high-quality software solutions in fast-paced startup environments.\",\n  experience: [\n    {\n      company: \"TechCorp Inc.\",\n      position: \"Senior Software Engineer\",\n      duration: \"Jan 2022 - Present\",\n      description: \"Lead development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 60%. Mentored 3 junior developers and conducted technical interviews.\",\n      location: \"San Francisco, CA\"\n    },\n    {\n      company: \"StartupXYZ\",\n      position: \"Full Stack Developer\",\n      duration: \"Mar 2020 - Dec 2021\",\n      description: \"Built responsive web applications using React and Node.js. Designed and implemented RESTful APIs. Collaborated with product team to define technical requirements and user stories.\",\n      location: \"Remote\"\n    },\n    {\n      company: \"Digital Solutions Ltd\",\n      position: \"Junior Developer\",\n      duration: \"Jun 2019 - Feb 2020\",\n      description: \"Developed and maintained client websites using HTML, CSS, JavaScript, and PHP. Participated in code reviews and agile development processes. Fixed bugs and implemented new features.\",\n      location: \"New York, NY\"\n    }\n  ],\n  education: [\n    {\n      institution: \"University of California, Berkeley\",\n      degree: \"Bachelor of Science\",\n      field: \"Computer Science\",\n      duration: \"2015 - 2019\",\n      location: \"Berkeley, CA\"\n    }\n  ],\n  skills: [\n    \"JavaScript\", \"TypeScript\", \"React\", \"Node.js\", \"Python\", \"AWS\", \n    \"Docker\", \"Kubernetes\", \"PostgreSQL\", \"MongoDB\", \"Git\", \"Agile/Scrum\",\n    \"System Design\", \"API Development\", \"Microservices\", \"CI/CD\"\n  ],\n  certifications: [\n    {\n      name: \"AWS Certified Solutions Architect\",\n      issuer: \"Amazon Web Services\",\n      date: \"2023\"\n    },\n    {\n      name: \"Certified Kubernetes Administrator\",\n      issuer: \"Cloud Native Computing Foundation\",\n      date: \"2022\"\n    }\n  ],\n  languages: [\n    {\n      language: \"English\",\n      proficiency: \"Native\"\n    },\n    {\n      language: \"Spanish\",\n      proficiency: \"Conversational\"\n    }\n  ]\n}\n\n// Function to simulate AI processing delay\nexport function simulateAIProcessing(): Promise<LinkedInProfile> {\n  return new Promise((resolve) => {\n    setTimeout(() => {\n      resolve(demoLinkedInProfile)\n    }, 2000) // 2 second delay to simulate processing\n  })\n}\n"], "names": [], "mappings": ";;;;AAGO,MAAM,sBAAuC;IAClD,cAAc;QACZ,MAAM;QACN,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA,SAAS;IACT,YAAY;QACV;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;QACA;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;QACA;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;KACD;IACD,WAAW;QACT;YACE,aAAa;YACb,QAAQ;YACR,OAAO;YACP,UAAU;YACV,UAAU;QACZ;KACD;IACD,QAAQ;QACN;QAAc;QAAc;QAAS;QAAW;QAAU;QAC1D;QAAU;QAAc;QAAc;QAAW;QAAO;QACxD;QAAiB;QAAmB;QAAiB;KACtD;IACD,gBAAgB;QACd;YACE,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IACD,WAAW;QACT;YACE,UAAU;YACV,aAAa;QACf;QACA;YACE,UAAU;YACV,aAAa;QACf;KACD;AACH;AAGO,SAAS;IACd,OAAO,IAAI,QAAQ,CAAC;QAClB,WAAW;YACT,QAAQ;QACV,GAAG,MAAM,wCAAwC;;IACnD;AACF", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/api/process-linkedin/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { processLinkedInScreenshot, validateProfileData, type LinkedInProfile } from '@/lib/openai'\nimport { simulateAIProcessing } from '@/lib/demo-data'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Check if AIML API key is configured\n    const hasApiKey = !!process.env.AIML_API_KEY\n\n    if (!hasApiKey) {\n      console.log('AIML API key not configured, using demo data')\n    }\n\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No file provided' },\n        { status: 400 }\n      )\n    }\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      return NextResponse.json(\n        { error: 'File must be an image' },\n        { status: 400 }\n      )\n    }\n\n    // Validate file size (10MB limit)\n    if (file.size > 10 * 1024 * 1024) {\n      return NextResponse.json(\n        { error: 'File size must be less than 10MB' },\n        { status: 400 }\n      )\n    }\n\n    let profileData: LinkedInProfile\n\n    if (hasApiKey) {\n      console.log('AIML API key found, testing vision API...')\n      console.log('File type:', file.type)\n      console.log('File size:', file.size)\n\n      try {\n        // Convert file to base64 with optimization\n        const arrayBuffer = await file.arrayBuffer()\n        let base64 = Buffer.from(arrayBuffer).toString('base64')\n        console.log('Original base64 length:', base64.length)\n\n        // If image is very large, we might want to resize it\n        // For now, we'll use the original but log the size\n        const imageSizeKB = Math.round(base64.length * 0.75 / 1024) // Approximate KB\n        console.log(`Image size: ~${imageSizeKB}KB`)\n\n        if (imageSizeKB > 5000) { // If larger than 5MB\n          console.log('Large image detected, processing with high detail mode')\n        }\n\n        // Process with AIML API\n        console.log('Processing LinkedIn screenshot with enhanced AI...')\n        profileData = await processLinkedInScreenshot(base64)\n        console.log('AIML API processing successful!')\n\n      } catch (apiError) {\n        console.error('AIML API failed, falling back to demo data:', apiError)\n        profileData = await simulateAIProcessing()\n      }\n    } else {\n      console.log('No API key found, using demo data...')\n      // Use demo data when API key is not configured\n      profileData = await simulateAIProcessing()\n    }\n\n    // Validate the extracted data\n    if (!validateProfileData(profileData)) {\n      return NextResponse.json(\n        { error: 'Could not extract sufficient profile information from the image' },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: profileData\n    })\n\n  } catch (error) {\n    console.error('Error in process-linkedin API:', error)\n\n    // Fallback to demo data if AI processing fails\n    console.log('Falling back to demo data due to AI processing error')\n    try {\n      const demoData = await simulateAIProcessing()\n      return NextResponse.json({\n        success: true,\n        data: demoData,\n        warning: 'AI processing failed, showing demo data. Please check your API configuration.'\n      })\n    } catch (demoError) {\n      return NextResponse.json(\n        {\n          error: error instanceof Error ? error.message : 'Failed to process LinkedIn screenshot',\n          details: process.env.NODE_ENV === 'development' ? error : undefined\n        },\n        { status: 500 }\n      )\n    }\n  }\n}\n\n// Handle preflight requests for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,sCAAsC;QACtC,MAAM,YAAY,CAAC,CAAC,QAAQ,GAAG,CAAC,YAAY;QAE5C,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwB,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;QAEJ,IAAI,WAAW;YACb,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;YACnC,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;YAEnC,IAAI;gBACF,2CAA2C;gBAC3C,MAAM,cAAc,MAAM,KAAK,WAAW;gBAC1C,IAAI,SAAS,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;gBAC/C,QAAQ,GAAG,CAAC,2BAA2B,OAAO,MAAM;gBAEpD,qDAAqD;gBACrD,mDAAmD;gBACnD,MAAM,cAAc,KAAK,KAAK,CAAC,OAAO,MAAM,GAAG,OAAO,MAAM,iBAAiB;;gBAC7E,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC;gBAE3C,IAAI,cAAc,MAAM;oBACtB,QAAQ,GAAG,CAAC;gBACd;gBAEA,wBAAwB;gBACxB,QAAQ,GAAG,CAAC;gBACZ,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EAAE;gBAC9C,QAAQ,GAAG,CAAC;YAEd,EAAE,OAAO,UAAU;gBACjB,QAAQ,KAAK,CAAC,+CAA+C;gBAC7D,cAAc,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;YACzC;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,+CAA+C;YAC/C,cAAc,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;QACzC;QAEA,8BAA8B;QAC9B,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkE,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,+CAA+C;QAC/C,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;gBACN,SAAS;YACX;QACF,EAAE,OAAO,WAAW;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,SAAS,uCAAyC;YACpD,GACA;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAGO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}