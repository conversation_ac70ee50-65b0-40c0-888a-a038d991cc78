{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai'\n\n// Initialize AIML API client (using OpenAI SDK with custom base URL)\nconst aimlClient = new OpenAI({\n  apiKey: process.env.AIML_API_KEY,\n  baseURL: 'https://api.aimlapi.com',\n})\n\n// AIML API provides access to GPT-4o and other advanced vision models\n\nexport interface LinkedInProfile {\n  personalInfo: {\n    name: string\n    title: string\n    location: string\n    email?: string\n    phone?: string\n    linkedin?: string\n  }\n  summary: string\n  experience: Array<{\n    company: string\n    position: string\n    duration: string\n    description: string\n    location?: string\n  }>\n  education: Array<{\n    institution: string\n    degree: string\n    field: string\n    duration: string\n    location?: string\n  }>\n  skills: string[]\n  certifications?: Array<{\n    name: string\n    issuer: string\n    date?: string\n  }>\n  languages?: Array<{\n    language: string\n    proficiency: string\n  }>\n}\n\nexport async function processLinkedInScreenshot(imageBase64: string): Promise<LinkedInProfile> {\n  try {\n    console.log('Starting AIML API request...')\n\n    const response = await aimlClient.chat.completions.create({\n      model: \"gpt-4o-2024-08-06\",\n      messages: [\n        {\n          role: \"user\",\n          content: [\n            {\n              type: \"text\",\n              text: `You are a professional CV extraction specialist. Analyze this LinkedIn profile screenshot and extract ALL visible professional information with high accuracy.\n\nEXTRACT THE FOLLOWING DATA:\n\n1. **Personal Information:**\n   - Full name (exactly as shown)\n   - Current job title/headline\n   - Location (city, state/country)\n   - Contact information (email, phone if visible)\n   - LinkedIn URL (if visible)\n\n2. **Professional Summary:**\n   - Complete \"About\" section text\n   - Professional headline/tagline\n\n3. **Work Experience:** (For each position)\n   - Company name\n   - Job title/position\n   - Employment duration (start - end dates)\n   - Job description/responsibilities\n   - Location (if shown)\n\n4. **Education:** (For each degree)\n   - Institution name\n   - Degree type and field of study\n   - Duration/graduation year\n   - Location (if shown)\n\n5. **Skills:**\n   - All listed skills (technical and soft skills)\n   - Endorsement counts (if visible)\n\n6. **Certifications:** (if any)\n   - Certification name\n   - Issuing organization\n   - Date obtained\n\n7. **Languages:** (if any)\n   - Language name\n   - Proficiency level\n\n**IMPORTANT REQUIREMENTS:**\n- Return ONLY valid JSON format\n- Use the exact structure provided below\n- Extract text exactly as written (preserve professional language)\n- If information is not visible, use empty string \"\" or empty array []\n- Be thorough - this data will generate someone's professional CV\n\n**JSON STRUCTURE:**\n{\n  \"personalInfo\": {\n    \"name\": \"\",\n    \"title\": \"\",\n    \"location\": \"\",\n    \"email\": \"\",\n    \"phone\": \"\",\n    \"linkedin\": \"\"\n  },\n  \"summary\": \"\",\n  \"experience\": [\n    {\n      \"company\": \"\",\n      \"position\": \"\",\n      \"duration\": \"\",\n      \"description\": \"\",\n      \"location\": \"\"\n    }\n  ],\n  \"education\": [\n    {\n      \"institution\": \"\",\n      \"degree\": \"\",\n      \"field\": \"\",\n      \"duration\": \"\",\n      \"location\": \"\"\n    }\n  ],\n  \"skills\": [],\n  \"certifications\": [\n    {\n      \"name\": \"\",\n      \"issuer\": \"\",\n      \"date\": \"\"\n    }\n  ],\n  \"languages\": [\n    {\n      \"language\": \"\",\n      \"proficiency\": \"\"\n    }\n  ]\n}`\n            },\n            {\n              type: \"image_url\",\n              image_url: {\n                url: `data:image/jpeg;base64,${imageBase64}`,\n                detail: \"high\"\n              }\n            }\n          ]\n        }\n      ],\n      max_tokens: 4000,\n      temperature: 0.1, // Low temperature for consistent, accurate extraction\n      top_p: 0.9, // Focus on most likely tokens for accuracy\n    })\n\n    const content = response.choices[0]?.message?.content\n    if (!content) {\n      throw new Error('No response from OpenAI')\n    }\n\n    // Try to parse the JSON response\n    try {\n      const profileData = JSON.parse(content) as LinkedInProfile\n      return profileData\n    } catch (parseError) {\n      // If JSON parsing fails, try to extract JSON from the response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/)\n      if (jsonMatch) {\n        const profileData = JSON.parse(jsonMatch[0]) as LinkedInProfile\n        return profileData\n      }\n      throw new Error('Failed to parse AI response as JSON')\n    }\n\n  } catch (error) {\n    console.error('Error processing LinkedIn screenshot with AIML API:', error)\n\n    // Provide more detailed error information\n    if (error instanceof Error) {\n      console.error('Error message:', error.message)\n      console.error('Error stack:', error.stack)\n    }\n\n    // Check if it's an API-related error\n    if (error && typeof error === 'object' && 'response' in error) {\n      console.error('API response error:', error)\n    }\n\n    throw new Error(`Failed to process LinkedIn screenshot with AI: ${error instanceof Error ? error.message : 'Unknown error'}`)\n  }\n}\n\n// Helper function to convert file to base64\nexport function fileToBase64(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader()\n    reader.readAsDataURL(file)\n    reader.onload = () => {\n      const result = reader.result as string\n      // Remove the data URL prefix to get just the base64 string\n      const base64 = result.split(',')[1]\n      resolve(base64)\n    }\n    reader.onerror = error => reject(error)\n  })\n}\n\n// Validate extracted profile data\nexport function validateProfileData(profile: LinkedInProfile): boolean {\n  return !!(\n    profile.personalInfo?.name &&\n    profile.personalInfo?.title &&\n    (profile.experience?.length > 0 || profile.education?.length > 0)\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;AAEA,qEAAqE;AACrE,MAAM,aAAa,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC5B,QAAQ,QAAQ,GAAG,CAAC,YAAY;IAChC,SAAS;AACX;AAwCO,eAAe,0BAA0B,WAAmB;IACjE,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,WAAW,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACxD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;wBACP;4BACE,MAAM;4BACN,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2FpB,CAAC;wBACU;wBACA;4BACE,MAAM;4BACN,WAAW;gCACT,KAAK,CAAC,uBAAuB,EAAE,aAAa;gCAC5C,QAAQ;4BACV;wBACF;qBACD;gBACH;aACD;YACD,YAAY;YACZ,aAAa;YACb,OAAO;QACT;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,iCAAiC;QACjC,IAAI;YACF,MAAM,cAAc,KAAK,KAAK,CAAC;YAC/B,OAAO;QACT,EAAE,OAAO,YAAY;YACnB,+DAA+D;YAC/D,MAAM,YAAY,QAAQ,KAAK,CAAC;YAChC,IAAI,WAAW;gBACb,MAAM,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBAC3C,OAAO;YACT;YACA,MAAM,IAAI,MAAM;QAClB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uDAAuD;QAErE,0CAA0C;QAC1C,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;YAC7C,QAAQ,KAAK,CAAC,gBAAgB,MAAM,KAAK;QAC3C;QAEA,qCAAqC;QACrC,IAAI,SAAS,OAAO,UAAU,YAAY,cAAc,OAAO;YAC7D,QAAQ,KAAK,CAAC,uBAAuB;QACvC;QAEA,MAAM,IAAI,MAAM,CAAC,+CAA+C,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC9H;AACF;AAGO,SAAS,aAAa,IAAU;IACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,aAAa,CAAC;QACrB,OAAO,MAAM,GAAG;YACd,MAAM,SAAS,OAAO,MAAM;YAC5B,2DAA2D;YAC3D,MAAM,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;YACnC,QAAQ;QACV;QACA,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;IACnC;AACF;AAGO,SAAS,oBAAoB,OAAwB;IAC1D,OAAO,CAAC,CAAC,CACP,QAAQ,YAAY,EAAE,QACtB,QAAQ,YAAY,EAAE,SACtB,CAAC,QAAQ,UAAU,EAAE,SAAS,KAAK,QAAQ,SAAS,EAAE,SAAS,CAAC,CAClE;AACF", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/lib/demo-data.ts"], "sourcesContent": ["import { LinkedInProfile } from './openai'\n\n// Demo profile data for when OpenAI API is not configured\nexport const demoLinkedInProfile: LinkedInProfile = {\n  personalInfo: {\n    name: \"<PERSON>\",\n    title: \"Senior Software Engineer\",\n    location: \"San Francisco, CA\",\n    email: \"<EMAIL>\",\n    linkedin: \"linkedin.com/in/alex<PERSON><PERSON><PERSON>\"\n  },\n  summary: \"Experienced software engineer with 5+ years of expertise in full-stack development, cloud architecture, and team leadership. Passionate about building scalable applications and mentoring junior developers. Proven track record of delivering high-quality software solutions in fast-paced startup environments.\",\n  experience: [\n    {\n      company: \"TechCorp Inc.\",\n      position: \"Senior Software Engineer\",\n      duration: \"Jan 2022 - Present\",\n      description: \"Lead development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 60%. Mentored 3 junior developers and conducted technical interviews.\",\n      location: \"San Francisco, CA\"\n    },\n    {\n      company: \"StartupXYZ\",\n      position: \"Full Stack Developer\",\n      duration: \"Mar 2020 - Dec 2021\",\n      description: \"Built responsive web applications using React and Node.js. Designed and implemented RESTful APIs. Collaborated with product team to define technical requirements and user stories.\",\n      location: \"Remote\"\n    },\n    {\n      company: \"Digital Solutions Ltd\",\n      position: \"Junior Developer\",\n      duration: \"Jun 2019 - Feb 2020\",\n      description: \"Developed and maintained client websites using HTML, CSS, JavaScript, and PHP. Participated in code reviews and agile development processes. Fixed bugs and implemented new features.\",\n      location: \"New York, NY\"\n    }\n  ],\n  education: [\n    {\n      institution: \"University of California, Berkeley\",\n      degree: \"Bachelor of Science\",\n      field: \"Computer Science\",\n      duration: \"2015 - 2019\",\n      location: \"Berkeley, CA\"\n    }\n  ],\n  skills: [\n    \"JavaScript\", \"TypeScript\", \"React\", \"Node.js\", \"Python\", \"AWS\", \n    \"Docker\", \"Kubernetes\", \"PostgreSQL\", \"MongoDB\", \"Git\", \"Agile/Scrum\",\n    \"System Design\", \"API Development\", \"Microservices\", \"CI/CD\"\n  ],\n  certifications: [\n    {\n      name: \"AWS Certified Solutions Architect\",\n      issuer: \"Amazon Web Services\",\n      date: \"2023\"\n    },\n    {\n      name: \"Certified Kubernetes Administrator\",\n      issuer: \"Cloud Native Computing Foundation\",\n      date: \"2022\"\n    }\n  ],\n  languages: [\n    {\n      language: \"English\",\n      proficiency: \"Native\"\n    },\n    {\n      language: \"Spanish\",\n      proficiency: \"Conversational\"\n    }\n  ]\n}\n\n// Function to simulate AI processing delay\nexport function simulateAIProcessing(): Promise<LinkedInProfile> {\n  return new Promise((resolve) => {\n    setTimeout(() => {\n      resolve(demoLinkedInProfile)\n    }, 2000) // 2 second delay to simulate processing\n  })\n}\n"], "names": [], "mappings": ";;;;AAGO,MAAM,sBAAuC;IAClD,cAAc;QACZ,MAAM;QACN,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA,SAAS;IACT,YAAY;QACV;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;QACA;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;QACA;YACE,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;QACZ;KACD;IACD,WAAW;QACT;YACE,aAAa;YACb,QAAQ;YACR,OAAO;YACP,UAAU;YACV,UAAU;QACZ;KACD;IACD,QAAQ;QACN;QAAc;QAAc;QAAS;QAAW;QAAU;QAC1D;QAAU;QAAc;QAAc;QAAW;QAAO;QACxD;QAAiB;QAAmB;QAAiB;KACtD;IACD,gBAAgB;QACd;YACE,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IACD,WAAW;QACT;YACE,UAAU;YACV,aAAa;QACf;QACA;YACE,UAAU;YACV,aAAa;QACf;KACD;AACH;AAGO,SAAS;IACd,OAAO,IAAI,QAAQ,CAAC;QAClB,WAAW;YACT,QAAQ;QACV,GAAG,MAAM,wCAAwC;;IACnD;AACF", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Linkedin%20Ai%20app/linkedin-cv-app/src/app/api/process-linkedin/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { processLinkedInScreenshot, validateProfileData, type LinkedInProfile } from '@/lib/openai'\nimport { simulateAIProcessing } from '@/lib/demo-data'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Check if AIML API key is configured\n    const hasApiKey = !!process.env.AIML_API_KEY\n\n    if (!hasApiKey) {\n      console.log('AIML API key not configured, using demo data')\n    }\n\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No file provided' },\n        { status: 400 }\n      )\n    }\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      return NextResponse.json(\n        { error: 'File must be an image' },\n        { status: 400 }\n      )\n    }\n\n    // Validate file size (10MB limit)\n    if (file.size > 10 * 1024 * 1024) {\n      return NextResponse.json(\n        { error: 'File size must be less than 10MB' },\n        { status: 400 }\n      )\n    }\n\n    let profileData: LinkedInProfile\n\n    if (hasApiKey) {\n      console.log('Using AIML API for processing...')\n      console.log('File type:', file.type)\n      console.log('File size:', file.size)\n\n      // Convert file to base64\n      const arrayBuffer = await file.arrayBuffer()\n      const base64 = Buffer.from(arrayBuffer).toString('base64')\n      console.log('Base64 length:', base64.length)\n\n      // Process with AIML API\n      profileData = await processLinkedInScreenshot(base64)\n    } else {\n      console.log('No API key found, using demo data...')\n      // Use demo data when API key is not configured\n      profileData = await simulateAIProcessing()\n    }\n\n    // Validate the extracted data\n    if (!validateProfileData(profileData)) {\n      return NextResponse.json(\n        { error: 'Could not extract sufficient profile information from the image' },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: profileData\n    })\n\n  } catch (error) {\n    console.error('Error in process-linkedin API:', error)\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Failed to process LinkedIn screenshot',\n        details: process.env.NODE_ENV === 'development' ? error : undefined\n      },\n      { status: 500 }\n    )\n  }\n}\n\n// Handle preflight requests for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,sCAAsC;QACtC,MAAM,YAAY,CAAC,CAAC,QAAQ,GAAG,CAAC,YAAY;QAE5C,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwB,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;QAEJ,IAAI,WAAW;YACb,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;YACnC,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;YAEnC,yBAAyB;YACzB,MAAM,cAAc,MAAM,KAAK,WAAW;YAC1C,MAAM,SAAS,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;YACjD,QAAQ,GAAG,CAAC,kBAAkB,OAAO,MAAM;YAE3C,wBAAwB;YACxB,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EAAE;QAChD,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,+CAA+C;YAC/C,cAAc,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;QACzC;QAEA,8BAA8B;QAC9B,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkE,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS,uCAAyC;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}