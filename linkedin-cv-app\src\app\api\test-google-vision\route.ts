import { NextRequest, NextResponse } from 'next/server'
import { extractTextWithGoogleVision } from '@/lib/google-vision'

export async function GET() {
  try {
    if (!process.env.GOOGLE_VISION_API_KEY) {
      return NextResponse.json({
        error: 'Google Vision API key not configured',
        instructions: 'Add GOOGLE_VISION_API_KEY to your .env.local file'
      })
    }

    // Test with a simple image (1x1 pixel PNG with some text)
    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='

    console.log('Testing Google Vision API...')

    const extractedText = await extractTextWithGoogleVision(testImageBase64)

    return NextResponse.json({
      success: true,
      message: 'Google Vision API is working',
      extractedText: extractedText || 'No text found in test image (this is normal)',
      apiKeyConfigured: true
    })

  } catch (error) {
    console.error('Google Vision API test failed:', error)
    
    return NextResponse.json({
      error: 'Google Vision API test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      instructions: [
        '1. Get API key from Google Cloud Console',
        '2. Enable Vision API in your Google Cloud project',
        '3. Add GOOGLE_VISION_API_KEY to .env.local',
        '4. Make sure billing is enabled for your Google Cloud project'
      ]
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    if (!process.env.GOOGLE_VISION_API_KEY) {
      return NextResponse.json({
        error: 'Google Vision API key not configured'
      })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({
        error: 'No file provided'
      }, { status: 400 })
    }

    // Convert file to base64
    const arrayBuffer = await file.arrayBuffer()
    const base64 = Buffer.from(arrayBuffer).toString('base64')

    console.log('Testing Google Vision with uploaded file...')
    console.log('File type:', file.type)
    console.log('File size:', file.size)

    const extractedText = await extractTextWithGoogleVision(base64)

    return NextResponse.json({
      success: true,
      message: 'Google Vision text extraction successful',
      extractedText: extractedText,
      textLength: extractedText.length,
      preview: extractedText.substring(0, 500) + (extractedText.length > 500 ? '...' : '')
    })

  } catch (error) {
    console.error('Google Vision file test failed:', error)
    
    return NextResponse.json({
      error: 'Google Vision file test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
