["google/api/annotations.proto", "google/api/apikeys/v2/apikeys.proto", "google/api/apikeys/v2/resources.proto", "google/api/auth.proto", "google/api/backend.proto", "google/api/billing.proto", "google/api/client.proto", "google/api/cloudquotas/v1/cloudquotas.proto", "google/api/cloudquotas/v1/resources.proto", "google/api/config_change.proto", "google/api/consumer.proto", "google/api/context.proto", "google/api/control.proto", "google/api/distribution.proto", "google/api/documentation.proto", "google/api/endpoint.proto", "google/api/error_reason.proto", "google/api/expr/conformance/v1alpha1/conformance_service.proto", "google/api/expr/v1alpha1/checked.proto", "google/api/expr/v1alpha1/eval.proto", "google/api/expr/v1alpha1/explain.proto", "google/api/expr/v1alpha1/syntax.proto", "google/api/expr/v1alpha1/value.proto", "google/api/expr/v1beta1/decl.proto", "google/api/expr/v1beta1/eval.proto", "google/api/expr/v1beta1/expr.proto", "google/api/expr/v1beta1/source.proto", "google/api/expr/v1beta1/value.proto", "google/api/field_behavior.proto", "google/api/field_info.proto", "google/api/http.proto", "google/api/httpbody.proto", "google/api/label.proto", "google/api/launch_stage.proto", "google/api/log.proto", "google/api/logging.proto", "google/api/metric.proto", "google/api/monitored_resource.proto", "google/api/monitoring.proto", "google/api/policy.proto", "google/api/quota.proto", "google/api/resource.proto", "google/api/routing.proto", "google/api/service.proto", "google/api/servicecontrol/v1/check_error.proto", "google/api/servicecontrol/v1/distribution.proto", "google/api/servicecontrol/v1/http_request.proto", "google/api/servicecontrol/v1/log_entry.proto", "google/api/servicecontrol/v1/metric_value.proto", "google/api/servicecontrol/v1/operation.proto", "google/api/servicecontrol/v1/quota_controller.proto", "google/api/servicecontrol/v1/service_controller.proto", "google/api/servicecontrol/v2/service_controller.proto", "google/api/servicemanagement/v1/resources.proto", "google/api/servicemanagement/v1/servicemanager.proto", "google/api/serviceusage/v1/resources.proto", "google/api/serviceusage/v1/serviceusage.proto", "google/api/serviceusage/v1beta1/resources.proto", "google/api/serviceusage/v1beta1/serviceusage.proto", "google/api/source_info.proto", "google/api/system_parameter.proto", "google/api/usage.proto", "google/api/visibility.proto", "google/cloud/location/locations.proto", "google/iam/v1/iam_policy.proto", "google/iam/v1/logging/audit_data.proto", "google/iam/v1/options.proto", "google/iam/v1/policy.proto", "google/logging/type/http_request.proto", "google/logging/type/log_severity.proto", "google/longrunning/operations.proto", "google/monitoring/v3/alert.proto", "google/monitoring/v3/alert_service.proto", "google/monitoring/v3/common.proto", "google/monitoring/v3/dropped_labels.proto", "google/monitoring/v3/group.proto", "google/monitoring/v3/group_service.proto", "google/monitoring/v3/metric.proto", "google/monitoring/v3/metric_service.proto", "google/monitoring/v3/mutation_record.proto", "google/monitoring/v3/notification.proto", "google/monitoring/v3/notification_service.proto", "google/monitoring/v3/query_service.proto", "google/monitoring/v3/service.proto", "google/monitoring/v3/service_service.proto", "google/monitoring/v3/snooze.proto", "google/monitoring/v3/snooze_service.proto", "google/monitoring/v3/span_context.proto", "google/monitoring/v3/uptime.proto", "google/monitoring/v3/uptime_service.proto", "google/protobuf/any.proto", "google/protobuf/api.proto", "google/protobuf/bridge/message_set.proto", "google/protobuf/compiler/plugin.proto", "google/protobuf/compiler/ruby/ruby_generated_code.proto", "google/protobuf/compiler/ruby/ruby_generated_code_proto2.proto", "google/protobuf/compiler/ruby/ruby_generated_code_proto2_import.proto", "google/protobuf/compiler/ruby/ruby_generated_pkg_explicit.proto", "google/protobuf/compiler/ruby/ruby_generated_pkg_explicit_legacy.proto", "google/protobuf/compiler/ruby/ruby_generated_pkg_implicit.proto", "google/protobuf/cpp_features.proto", "google/protobuf/descriptor.proto", "google/protobuf/duration.proto", "google/protobuf/empty.proto", "google/protobuf/field_mask.proto", "google/protobuf/source_context.proto", "google/protobuf/struct.proto", "google/protobuf/timestamp.proto", "google/protobuf/type.proto", "google/protobuf/util/json_format.proto", "google/protobuf/util/json_format_proto3.proto", "google/protobuf/wrappers.proto", "google/rpc/code.proto", "google/rpc/context/attribute_context.proto", "google/rpc/context/audit_context.proto", "google/rpc/error_details.proto", "google/rpc/http.proto", "google/rpc/status.proto", "google/type/calendar_period.proto", "google/type/color.proto", "google/type/date.proto", "google/type/datetime.proto", "google/type/dayofweek.proto", "google/type/decimal.proto", "google/type/expr.proto", "google/type/fraction.proto", "google/type/interval.proto", "google/type/latlng.proto", "google/type/localized_text.proto", "google/type/money.proto", "google/type/month.proto", "google/type/phone_number.proto", "google/type/postal_address.proto", "google/type/quaternion.proto", "google/type/timeofday.proto"]